package com.dcas;

import cn.hutool.core.io.FileUtil;
import com.mchz.base.metis.common.MetisConst;
import com.mchz.mcdatasource.DbCoreInit;
import com.mchz.mcdatasource.core.DatasourceConstant;
import com.yomahub.tlog.core.enhance.bytes.AspectLogEnhance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.io.File;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@EnableAsync
@EnableScheduling
@ComponentScan(basePackages = {"com.mchz.*","com.dcas.*"})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, MongoAutoConfiguration.class, SecurityAutoConfiguration.class})
public class StartApplication {
    private static final Logger logger = LoggerFactory.getLogger(StartApplication.class);
    private static final String ENABLE = "enable";
    static {AspectLogEnhance.enhance();}

    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(StartApplication.class, args);
    }

    static {
        String path = System.getProperty(DatasourceConstant.MCDATASOURCE_HOME);
        System.setProperty(ENABLE, "false");

        if (!FileUtil.exist(path)) {
            path = String.format("%s%smcdatasource", System.getProperty("user.dir"), File.separator);
            if (FileUtil.exist(path)) {
                System.setProperty(DatasourceConstant.MCDATASOURCE_HOME, path);
                System.setProperty(DatasourceConstant.MCDATASOURCE_VERSION, "1.7.1.5");
                System.setProperty(ENABLE, "true");
            }
        } else {
            logger.info("MCDATASOURCE_HOME:{}, MCDATASOURCE_VERSION={}",path, System.getProperty(DatasourceConstant.MCDATASOURCE_VERSION));
            System.setProperty(ENABLE, "true");
        }

        // 统一数据源初始化
        DbCoreInit.init();

        // 分类分级metis app path初始化
        if (System.getProperty(MetisConst.APP_HOME) == null) {
            System.setProperty(MetisConst.APP_HOME, "/opt/mc/common/data/storage/metis");
            logger.info(MetisConst.APP_HOME+":{}", System.getProperty(MetisConst.APP_HOME));
        }
    }
}
