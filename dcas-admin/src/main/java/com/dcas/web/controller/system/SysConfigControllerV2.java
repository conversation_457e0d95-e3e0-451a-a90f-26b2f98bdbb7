package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.constant.UserConstants;
import com.dcas.common.core.controller.BaseController;
import com.dcas.common.core.domain.ResponseResult;
import com.dcas.common.core.page.TableDataInfo;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.utils.poi.ExcelUtil;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/2/22 14:10
 * @since 1.7.0
 */
@RestController
@RequestMapping("/system/config")
@Api(tags = "系统配置")
@RequiredArgsConstructor
public class SysConfigControllerV2 extends BaseController {

    private final ISysConfigService configService;

    /**
     * 获取参数配置列表
     */
    @GetMapping("/list")
    @Log(title = "获取参数配置列表", businessType = BusinessType.QUERY)
    public TableDataInfo list(SysConfig config) {
        startPage();
        List<SysConfig> list = configService.selectConfigList(config);
        return getDataTable(list);
    }

    @PostMapping("/export")
    @Log(title = "导出参数配置列表", businessType = BusinessType.EXPORT)
    public void export(HttpServletResponse response, SysConfig config) {
        List<SysConfig> list = configService.selectConfigList(config);
        ExcelUtil<SysConfig> util = new ExcelUtil<SysConfig>(SysConfig.class);
        util.exportExcel(response, list, "参数数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @GetMapping(value = "/{configId}")
    @ApiOperation("根据配置主键查询系统配置信息")
    @Log(title = "根据配置主键查询系统配置信息", businessType = BusinessType.QUERY)
    public ResponseResult getInfo(@PathVariable Long configId) {
        return success(configService.selectConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/configKey/{configKey}")
    @Log(title = "根据参数键名查询参数值", businessType = BusinessType.QUERY)
    public ResponseResult getConfigKey(@PathVariable String configKey) {
        return success(configService.selectConfigByKey(configKey));
    }

    /**
     * 新增参数配置
     */
    @PostMapping
    @Log(title = "新增参数配置", businessType = BusinessType.INSERT)
    public ResponseResult add(@Validated @RequestBody SysConfig config) {
        if (UserConstants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(config))) {
            return error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setCreateBy(getUsername());
        return toAjax(configService.insertConfig(config));
    }

    /**
     * 修改参数配置
     */
    @PutMapping
    @Log(title = "修改参数配置", businessType = BusinessType.UPDATE)
    public ResponseResult edit(@Validated @RequestBody SysConfig config) {
        if (UserConstants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(config))) {
            return error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setUpdateBy(getUsername());
        return toAjax(configService.updateConfig(config));
    }

    /**
     * 删除参数配置
     */
    @DeleteMapping("/{configIds}")
    @Log(title = "删除参数配置", businessType = BusinessType.DELETE)
    public ResponseResult remove(@PathVariable Long[] configIds) {
        configService.deleteConfigByIds(configIds);
        return success();
    }

    /**
     * 刷新参数缓存
     */
    @DeleteMapping("/refreshCache")
    @Log(title = "刷新参数缓存", businessType = BusinessType.OTHER)
    public ResponseResult refreshCache() {
        configService.resetConfigCache();
        return success();
    }

}
