package com.dcas.web.controller.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.AssessModuleType;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.core.param.DataSourceParam;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.dto.SchemaTableDTO;
import com.dcas.common.model.other.LabelValueItem;
import com.dcas.common.model.query.CompareColumnQuery;
import com.dcas.common.model.req.*;
import com.dcas.common.utils.Func;
import com.dcas.system.domain.resp.TestConnectionResponse;
import com.dcas.common.model.vo.*;
import com.dcas.system.service.CoConnService;
import com.dcas.system.service.CoDbSecurityService;
import com.dcas.system.service.DatabaseService;
import com.dcas.web.core.task.DatabasePermTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/api/db")
@RequiredArgsConstructor
@Api(tags = "数据源连接")
public class CoConnController {

    private final CoConnService coConnService;
    private final DatabaseService databaseService;

    private final DatabasePermTask databasePermTask;
    private final CoDbSecurityService coDbSecurityService;

    @GetMapping("/config/basic")
    @ApiOperation("查询数据源支持类型")
    public R<List<LabelValueItem>> getConfigBasic(@RequestParam String operationId) {
        return R.success(coConnService.getConfigBasic( operationId, "add"));
    }

    @GetMapping("/config/details")
    @ApiOperation("查询数据源配置详情")
    public R<Map<String, Object>> getConfigDetails() {
        return R.success(coConnService.getConfigDetails());
    }

    @GetMapping("/source")
    @ApiOperation("查询数据源类型")
    public R<Map<Long, String>> sourceType() {
        return R.success(coConnService.sourceType());
    }

    @GetMapping("/source/multi")
    @ApiOperation("根据数据源类型查询多版本数据源")
    public R<List<SelectItemVO<String, Integer>>> multiByConfigType(@RequestParam Integer configType) {
        return R.success(coConnService.getMultiByConfigType(configType));
    }

    /**
     * 数据库连接测试
     *
     * @param config 数据源配置
     * @return 连通性测试情况
     */
    @ApiOperation(value = "数据库连接测试")
    @PostMapping(value = "/ping")
    public R<TestConnectionResponse> ping(@RequestBody DataSourceParam config) {
        return R.success(databaseService.testConnection(config));
    }

    /**
     * 获取schemas
     */
    @GetMapping("/schemas")
    @ApiOperation("数据源schemas查询")
    public R<List<SchemaTableDTO>> getSchemas(@ApiParam("数据源id") @RequestParam Integer id, @ApiParam("作业id") /*@RequestParam*/ String operationId) {
        return R.success(databaseService.listSourceSchema(id, operationId));
    }

    /**
     * 获取tables
     */
    @GetMapping("/tables")
    @ApiOperation("数据源tables查询")
    public R<List<SchemaTableDTO>> getTables(@ApiParam("数据源id") @RequestParam Integer id, @ApiParam("schema名称") String schema) {
        return R.success(databaseService.listSourceTable(id, schema, true));
    }

    @GetMapping("/columns")
    @ApiOperation("数据源字段列表查询")
    public R<List<ColumnVO>> getColumns(@ApiParam("数据源id") @RequestParam Integer id, @ApiParam("schema名称") @RequestParam String schema, @ApiParam("table名称") @RequestParam String table) {
        return R.success(databaseService.listColumns(id, schema, table));
    }

    @PostMapping("/compare/columns")
    @ApiOperation("比对系统格式表格字段查询")
    public R<List<MkCompareColumnVO>> getColumns(@RequestBody CompareColumnQuery query) {
        return R.success(databaseService.listSourceTableColumn(query));
    }

    @ApiOperation("查询数据源列表")
    @GetMapping("/connection")
    public R<List<PreSourceConfigVO>> queryPreSourceConfigList(PreSourceConfigRequest request) {
        return R.success(databaseService.queryPreSourceConfigList(request));
    }

    @ApiOperation(value = "添加数据源连接")
    @PostMapping(value = "/connection")
    @Log(title = "添加数据源连接", businessType = BusinessType.INSERT)
    public R<Integer> addSourceConfig(@Validated @RequestBody SourceConfigAddReq request) {
        return R.success(databaseService.addSourceConfig(request));
    }

    @GetMapping("/connection/copy")
    @ApiOperation(value = "复制全局数据源连接")
    @Log(title = "复制数据源连接", businessType = BusinessType.INSERT)
    public R<Integer> copyGlobalSourceConfig(@RequestParam Integer sourceId, @RequestParam String operationId) {
        return R.success(databaseService.copyGlobalSourceConfig(sourceId, operationId));
    }

    @ApiOperation(value = "确定评估范围")
    @PutMapping("/connection")
    public R<Boolean> confirmAssessRange(@Validated @RequestBody SourceRangeConfirmReq req) {
        databaseService.confirmAssessRange(req);
        return R.success();
    }

    @ApiOperation(value = "执行配置补充")
    @PutMapping("/connection/fill")
    public R<Boolean> fillWorkConfig(@Validated @RequestBody WorkConfigFillReq req) {
        databaseService.fillWorkConfig(req);
        return R.success();
    }

    @ApiOperation("应用数据源配置")
    @PostMapping(value = "/connection/apply")
    @Log(title = "应用数据源配置", businessType = BusinessType.OTHER)
    public R<Boolean> applySourceConfig(@Validated @RequestBody SourcePageConfirmReq req) {
        try {
            SourceConfigResultVO vo = databaseService.applySourceConfig(req);
            if (CollUtil.isNotEmpty(vo.getTaskIds())) {
                vo.getTaskIds().forEach(databasePermTask::start);
            }
            if (CharSequenceUtil.isNotEmpty(vo.getErrorMessage())){
                return R.fail(vo.getErrorMessage());
            }
            return R.success();
        } catch (ServiceException e){
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation("根据数据源id查询数据源连接")
    @GetMapping("/connection/{id}")
    public R<SourceConfigUpdateReq> querySourceConfig(@PathVariable Integer id) {
        return R.success(databaseService.querySourceConfigById(id));
    }

    @ApiOperation("根据数据源id编辑数据源连接")
    @PutMapping("/connection/update")
    public R<Boolean> updateSourceConfig(@Validated @RequestBody SourceConfigUpdateReq req) {
        databaseService.updateSourceConfig(req);
        return R.success();
    }

    @ApiOperation("删除数据源连接")
    @DeleteMapping(value = "/connection")
    @Log(title = "删除数据源连接", businessType = BusinessType.DELETE)
    public R<Boolean> deleteSourceConfig(@RequestParam Integer id) {
        databaseService.deleteSourceConfig(id);
        return R.success();
    }

    @ApiOperation("批量删除数据源连接")
    @PostMapping(value = "/connection/del/batch")
    @Log(title = "批量删除数据源连接", businessType = BusinessType.DELETE)
    public R<Boolean> batchDeleteSourceConfig(@RequestBody IdsReq ids) {
        try {
            databaseService.batchDeleteSourceConfig(ids);
            return R.success();
        } catch (ServiceException e) {
          return R.fail(e.getMessage());
        }
    }

    @ApiOperation("查询任务列表")
    @GetMapping("/connection/task")
    public R<ConnectionTaskVO> queryConnectionTask(@RequestParam String operationId) {
        return R.success(databaseService.queryConnectionTask(operationId));
    }

    /**
     * 创建查询作业
     *
     * @param request request
     * @return 作业id
     */
    @ApiOperation(value = "添加连接查询作业")
    @PostMapping(value = "/job/create")
    @Log(title = "新建权限查询作业", businessType = BusinessType.INSERT)
    public R<Boolean> create(@RequestBody DataBaseWorkAddReq request) {
        coDbSecurityService.create(request);
        return R.success();
    }

    /**
     * 查询连接记录
     *
     * @return * @return ResponseApi<List<LinkDatabaseVo>>
     */
    @ApiOperation(value = "查询连接记录")
    @GetMapping(value = "/job/list")
    public R<List<DbSecurityResultVO>> query(@ApiParam("作业id") @RequestParam String operationId,
                                             @ApiParam("作业类型") @RequestParam String jobType) {
        return R.success(coDbSecurityService.listAll(operationId, jobType));
    }

    /**
     * 删除连接记录
     *
     * @param id 删除作业id
     * @return 是否删除成功
     */
    @DeleteMapping(value = "/job/{id}")
    @ApiOperation("删除作业")
    @Log(title = "删除作业", businessType = BusinessType.DELETE)
    public R<Boolean> delete(@PathVariable("id") Long id, String type, String abilityType) {
        coDbSecurityService.delete(id, type, abilityType);
        return R.success();
    }

    /**
     * 作业启动
     *
     * @param id 作业id
     * @return 是否成功
     */
    @GetMapping("/job/start/{id}")
    @ApiOperation("启动作业")
    @Log(title = "启动权限查询作业", businessType = BusinessType.OTHER)
    public R<Boolean> start(@PathVariable("id") Long id, String type, String abilityType) {
        if (Func.isNullStr(type) && Func.isNullStr(abilityType))
            throw new ServiceException("漏扫任务不支持重启");
        if (StrUtil.isNotEmpty(type)) {
            AssessModuleType moduleType = AssessModuleType.valueOf(type);
            switch (moduleType) {
                case ASSET:
                    databaseService.startAssetJob(id);
                    break;
                case AUTH:
                case BASIC:
                    databasePermTask.start(id);
                    break;
            }
        } else if (StrUtil.isNotEmpty(abilityType)) {
            // TODO 能力市场作业重启
            throw new ServiceException("能力市场任务不支持重启");
        }
        return R.success();
    }

    /**
     * 作业停止
     *
     * @param id 作业id
     * @return 是否成功
     */
    @GetMapping("/job/stop/{id}")
    @ApiOperation("手动停止")
    @Log(title = "手动停止权限查询作业", businessType = BusinessType.OTHER)
    public R<Boolean> stop(@PathVariable("id") Long id, String type, String abilityType) {
        if (Func.isNullStr(type) && Func.isNullStr(abilityType))
            throw new ServiceException("漏扫任务不支持停止");
        if (StrUtil.isNotEmpty(type)) {
            AssessModuleType moduleType = AssessModuleType.valueOf(type);
            switch (moduleType) {
                case ASSET:
                    databaseService.stopAssetJob(id);
                    break;
                case AUTH:
                case BASIC:
                    databasePermTask.stop(id);
                    break;
            }
        } else if (StrUtil.isNotEmpty(abilityType)) {
            // TODO 能力市场作业停止
        }
        return R.success();
    }

}
