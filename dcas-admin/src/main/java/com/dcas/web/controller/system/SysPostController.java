package com.dcas.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.dcas.common.enums.LogType;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dcas.common.annotation.Log;
import com.dcas.common.constant.UserConstants;
import com.dcas.common.core.controller.BaseController;
import com.dcas.common.core.domain.ResponseResult;
import com.dcas.common.core.page.TableDataInfo;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.utils.poi.ExcelUtil;
import com.dcas.common.domain.entity.SysPost;
import com.dcas.system.service.ISysPostService;

/**
 * 岗位信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/post")
@Api(tags = "岗位管理")
public class SysPostController extends BaseController {
    @Autowired
    private ISysPostService postService;

    /**
     * 获取岗位列表
     */
    @GetMapping("/list")
    @Log(title = "获取岗位列表", businessType = BusinessType.QUERY, logType = LogType.SYSTEM)
    public TableDataInfo list(SysPost post) {
        startPage();
        List<SysPost> list = postService.selectPostList(post);
        return getDataTable(list);
    }

    @PostMapping("/export")
    @Log(title = "导出岗位列表", businessType = BusinessType.EXPORT, logType = LogType.SYSTEM)
    public void export(HttpServletResponse response, SysPost post) {
        List<SysPost> list = postService.selectPostList(post);
        ExcelUtil<SysPost> util = new ExcelUtil<SysPost>(SysPost.class);
        util.exportExcel(response, list, "岗位数据");
    }

    /**
     * 根据岗位编号获取详细信息
     */
    @GetMapping(value = "/{postId}")
    @Log(title = "根据岗位编号获取详细信息", businessType = BusinessType.QUERY, logType = LogType.SYSTEM)
    public ResponseResult getInfo(@PathVariable Long postId) {
        return success(postService.selectPostById(postId));
    }

    /**
     * 新增岗位
     */
    @PostMapping
    @Log(title = "新增岗位", businessType = BusinessType.INSERT, logType = LogType.SYSTEM)
    public ResponseResult add(@Validated @RequestBody SysPost post) {
        if (UserConstants.NOT_UNIQUE.equals(postService.checkPostNameUnique(post))) {
            return error("新增岗位'" + post.getPostName() + "'失败，岗位名称已存在");
        } else if (UserConstants.NOT_UNIQUE.equals(postService.checkPostCodeUnique(post))) {
            return error("新增岗位'" + post.getPostName() + "'失败，岗位编码已存在");
        }
        post.setCreateBy(getUsername());
        return toAjax(postService.insertPost(post));
    }

    /**
     * 修改岗位
     */
    @PutMapping
    @Log(title = "修改岗位", businessType = BusinessType.UPDATE, logType = LogType.SYSTEM)
    public ResponseResult edit(@Validated @RequestBody SysPost post) {
        if (UserConstants.NOT_UNIQUE.equals(postService.checkPostNameUnique(post))) {
            return error("修改岗位'" + post.getPostName() + "'失败，岗位名称已存在");
        } else if (UserConstants.NOT_UNIQUE.equals(postService.checkPostCodeUnique(post))) {
            return error("修改岗位'" + post.getPostName() + "'失败，岗位编码已存在");
        }
        post.setUpdateBy(getUsername());
        return toAjax(postService.updatePost(post));
    }

    /**
     * 删除岗位
     */
    @DeleteMapping("/{postIds}")
    @Log(title = "删除岗位", businessType = BusinessType.DELETE, logType = LogType.SYSTEM)
    public ResponseResult remove(@PathVariable Long[] postIds) {
        return toAjax(postService.deletePostByIds(postIds));
    }

    /**
     * 获取岗位选择框列表
     */
    @GetMapping("/optionselect")
    @Log(title = "获取岗位选择框列表", businessType = BusinessType.QUERY, logType = LogType.SYSTEM)
    public ResponseResult optionselect() {
        List<SysPost> posts = postService.selectPostAll();
        return success(posts);
    }
}
