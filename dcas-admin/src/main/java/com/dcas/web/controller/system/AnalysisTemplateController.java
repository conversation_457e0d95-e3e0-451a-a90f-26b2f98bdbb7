package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.enums.LogType;
import com.dcas.common.utils.Func;
import com.dcas.common.utils.PageResult;
import com.dcas.common.domain.entity.AnalysisTemplate;
import com.dcas.common.model.param.AnalysisTempParam;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.TemplateSearchReq;
import com.dcas.common.model.vo.AnalysisTempVO;
import com.dcas.system.service.AnalysisTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/6 16:39
 * @since 1.2.0
 */
@RestController
@RequestMapping("/api/analysis")
@RequiredArgsConstructor
@Api(tags = "分析模型")
public class AnalysisTemplateController {

    private final AnalysisTemplateService analysisTemplateService;

    @GetMapping("/list")
    @ApiOperation(tags = "分析模型", value = "分页查询分析模板")
    public R<PageResult<AnalysisTemplate>> list(@RequestParam(value = "currentPage", defaultValue = "1") @ApiParam("当前页码") Integer currentPage,
                                                @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam("每页数量") Integer pageSize) {
        return R.success(analysisTemplateService.list(currentPage, pageSize));
    }

    @PostMapping
    @ApiOperation(tags = "分析模型", value = "添加分析模板")
    @Log(title = "分析模型-添加分析模板", businessType = BusinessType.INSERT, logType = LogType.SYSTEM, module = "分析模型")
    public R<Boolean> add(@RequestBody AnalysisTempParam param) {
        analysisTemplateService.add(param);
        return R.success();
    }

    @PutMapping
    @ApiOperation(tags = "分析模型", value = "更新分析模板")
    @Log(title = "分析模型-更新分析模板", businessType = BusinessType.UPDATE, logType = LogType.SYSTEM, module = "分析模型")
    public R<Boolean> update(@RequestBody AnalysisTempParam param) {
        analysisTemplateService.update(param);
        return R.success();
    }

    @PostMapping("/delete")
    @ApiOperation(tags = "分析模型", value = "批量删除分析模板")
    @Log(title = "分析模型-批量删除分析模板", businessType = BusinessType.DELETE, logType = LogType.SYSTEM, module = "分析模型")
    public R<Boolean> delete(@RequestBody IdsReq req) {
        analysisTemplateService.delete(req);
        return R.success();
    }

    @PutMapping("/default")
    @ApiOperation(tags = "分析模型", value = "设为默认")
    @Log(title = "分析模型-设为默认", businessType = BusinessType.UPDATE, logType = LogType.SYSTEM, module = "分析模型")
    public R<Boolean> setDefault(Integer id) {
        analysisTemplateService.setDefault(id);
        return R.success();
    }

    @PutMapping("/enable")
    @ApiOperation(tags = "分析模型", value = "启用禁用")
    @Log(title = "分析模型-启用禁用", businessType = BusinessType.ENABLE, logType = LogType.SYSTEM, module = "分析模型")
    public R<Boolean> enable(Integer id) {
        analysisTemplateService.enable(id);
        return R.success();
    }

    @PostMapping("/bak")
    @ApiOperation(tags = "分析模型", value = "创建副本")
    @Log(title = "分析模型-创建副本", businessType = BusinessType.INSERT, logType = LogType.SYSTEM, module = "分析模型")
    public R<Boolean> createBak(@RequestBody AnalysisTempParam param) {
        analysisTemplateService.createBak(param);
        return R.success();
    }

    @GetMapping
    @ApiOperation(tags = "分析模型", value = "查看详情")
    public R<AnalysisTempVO> details(Integer id) {
        return R.success(analysisTemplateService.details(id));
    }

    @PostMapping("/search")
    @ApiOperation(tags = "分析模型", value = "搜索分析模板")
    public R<List<AnalysisTemplate>> search(@RequestBody TemplateSearchReq req) {
        return R.success(analysisTemplateService.search(req));
    }

    @GetMapping("/download")
    @ApiOperation(tags = "分析模型", value = "模板下载")
    public void download(HttpServletResponse response) {
        Func.writeTemplate(response, "analysisTemplate.xlsx", "analysisTemplate.xlsx");
    }
}
