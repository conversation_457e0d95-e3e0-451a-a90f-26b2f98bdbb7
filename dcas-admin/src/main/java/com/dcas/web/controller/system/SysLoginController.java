package com.dcas.web.controller.system;

import com.dcas.common.constant.Constants;
import com.dcas.common.core.domain.ResponseResult;
import com.dcas.common.core.domain.entity.SysMenu;
import com.dcas.common.core.domain.entity.SysUser;
import com.dcas.common.core.domain.model.LoginBody;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.system.service.impl.SysLoginServiceImpl;
import com.dcas.system.service.impl.SysPermissionServiceImpl;
import com.dcas.system.service.ISysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "SpringSecurity登录验证")
@RestController
@RequestMapping("/api")
public class SysLoginController {
    @Autowired
    private SysLoginServiceImpl loginService;

    /**
     * 登录方法:用户登录，返回token
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @ApiOperation(value = "用户登录")
    @PostMapping("/login")
    public ResponseResult login(@RequestBody LoginBody loginBody) {
        ResponseResult ajax = ResponseResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }
}
