package com.dcas.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.dcas.common.core.domain.R;
import com.dcas.common.enums.LogType;
import com.dcas.common.model.dto.SSOAccountDTO;
import com.dcas.system.service.*;
import com.mchz.starter.sso.model.SecureUser;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dcas.common.annotation.Log;
import com.dcas.common.constant.UserConstants;
import com.dcas.common.core.controller.BaseController;
import com.dcas.common.core.domain.ResponseResult;
import com.dcas.common.core.domain.entity.SysDept;
import com.dcas.common.core.domain.entity.SysRole;
import com.dcas.common.core.domain.entity.SysUser;
import com.dcas.common.core.page.TableDataInfo;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.StringUtils;
import com.dcas.common.utils.poi.ExcelUtil;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/user")
@Api(tags = "用户管理")
@RequiredArgsConstructor
public class SysUserController extends BaseController {
    private final ISysUserService userService;
    private final ISysRoleService roleService;
    private final ISysDeptService deptService;
    private final ISysPostService postService;
    private final IMcCenterService mcCenterService;

    /**
     * 获取用户列表
     */
    @GetMapping("/list")
    public R<List<SSOAccountDTO>> list() {
        return R.success(mcCenterService.getSsoUsers());
    }

    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 根据用户编号获取详细信息
     */
    @GetMapping(value = {"/" , "/{userId}"})
    public ResponseResult getInfo(@PathVariable(value = "userId" , required = false) Long userId) {
        userService.checkUserDataScope(userId);
        ResponseResult ajax = ResponseResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles" , roles);
        ajax.put("posts" , postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(ResponseResult.DATA_TAG, sysUser);
            ajax.put("postIds" , postService.selectPostListByUserId(userId));
            ajax.put("roleIds" , sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @Log(title = "新增用户" , businessType = BusinessType.INSERT, logType = LogType.SYSTEM, module = "用户管理")
    @PostMapping
    public ResponseResult add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user))) {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhoneNumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(getUsername());
        // TODO 前端已传入sha256加密后的密码，再次BCrypt加密
//        user.setPassword(SecurityUtils.sha256Password(user.getPassword()));
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @Log(title = "编辑用户" , businessType = BusinessType.UPDATE, logType = LogType.SYSTEM, module = "用户管理")
    @PutMapping
    public ResponseResult edit(@Validated @RequestBody SysUser user) {
        userService.checkUserDataScope(user.getUserId());
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user))) {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhoneNumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 重置密码
     */
    @Log(title = "编辑密码" , businessType = BusinessType.UPDATE, logType = LogType.SYSTEM, module = "用户管理")
    @PutMapping("/resetPwd")
    public ResponseResult resetPwd(@RequestBody SysUser user) {
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(getUsername());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @PutMapping("/changeStatus")
    @Log(title = "用户状态修改", businessType = BusinessType.UPDATE, logType = LogType.SYSTEM, module = "用户管理")
    public ResponseResult changeStatus(@RequestBody SysUser user) {
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @GetMapping("/authRole/{userId}")
    public ResponseResult authRole(@PathVariable("userId") Long userId) {
        ResponseResult ajax = ResponseResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user" , user);
        ajax.put("roles" , roles);
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @PutMapping("/authRole")
    public ResponseResult insertAuthRole(Long userId, Long[] roleIds) {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 获取部门树列表
     */
    @GetMapping("/deptTree")
    public ResponseResult deptTree(SysDept dept) {
        return success(deptService.selectDeptTreeList(dept));
    }

    @GetMapping
    public R<SSOAccountDTO> getUserAccount() {
        return R.success(SecurityUtils.getMcUser());
    }
}
