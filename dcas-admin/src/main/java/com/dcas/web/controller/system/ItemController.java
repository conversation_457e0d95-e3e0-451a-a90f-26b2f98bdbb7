package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.utils.Func;
import com.dcas.common.domain.entity.Item;
import com.dcas.common.model.req.ExcludeItemReq;
import com.dcas.system.service.ItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/10 17:53
 * @since 1.2.0
 */
@RestController
@RequestMapping("/api/item")
@RequiredArgsConstructor
@Api(tags = "核查项")
public class ItemController {

    private final ItemService itemService;

    @PostMapping("/add")
    @ApiOperation(tags = "核查项", value = "新增核查项")
    @Log(title = "新增核查项", businessType = BusinessType.INSERT)
    public R<Boolean> add(@RequestBody Item item) {
        itemService.add(item);
        return R.success();
    }

    @PutMapping("/unbind")
    @ApiOperation(tags = "核查项", value = "解绑")
    @Log(title = "核查项-解绑", businessType = BusinessType.OTHER)
    public R<Boolean> unBind(String id) {
        itemService.unBind(id);
        return R.success();
    }

    @DeleteMapping("/delete")
    @ApiOperation(tags = "核查项", value = "删除核查项")
    @Log(title = "删除核查项", businessType = BusinessType.DELETE)
    public R<Boolean> delete(String id) {
        itemService.delete(id);
        return R.success();
    }

    @PutMapping("/update")
    @ApiOperation(tags = "核查项", value = "更新核查项")
    @Log(title = "更新核查项", businessType = BusinessType.UPDATE)
    public R<Boolean> update(@RequestBody Item item) {
        itemService.update(item);
        return R.success();
    }

    @PostMapping("/import")
    @ApiOperation(tags = "核查项", value = "导入更新")
    @Log(title = "核查项-导入更新", businessType = BusinessType.IMPORT)
    public R<Boolean> importQuestion(MultipartFile file) {
        itemService.importQuestion(file);
        return R.success();
    }

    @PostMapping("/exclude")
    @ApiOperation(tags = "核查项", value = "查询未添加核查项列表")
    @Log(title = "核查项-查询未添加核查项列表", businessType = BusinessType.QUERY)
    public R<List<Item>> selectExcludeItems(@RequestBody ExcludeItemReq req) {
        return R.success(itemService.excludeList(req.getItemIds()));
    }

    @GetMapping("/download")
    @ApiOperation(tags = "核查项", value = "模板下载")
    @Log(title = "核查项-模板下载", businessType = BusinessType.EXPORT)
    public void download(HttpServletResponse response) {
        Func.writeTemplate(response, "commonTemplate.xlsx", "itemTemplate.xlsx");
    }
}
