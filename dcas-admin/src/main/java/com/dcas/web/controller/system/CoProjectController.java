package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.exception.params.FailParamsException;
import com.dcas.common.model.vo.ProjectDetailVO;
import com.github.pagehelper.PageInfo;
import com.dcas.common.annotation.RepeatSubmit;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.ResponseApi;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.CoCustomer;
import com.dcas.common.model.vo.QueryProjectVo;
import com.dcas.system.service.CoProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 项目管理模块控制层
 *
 * <AUTHOR>
 * @Date 2022/4/18 10:13
 * @ClassName ProjectManageController
 */
@Api(tags = "项目管理")
@RestController
@RequestMapping(value = "/api/project")
@Slf4j
public class CoProjectController {

    @Autowired
    private CoProjectService coProjectService;

    /**
     * 新增项目记录
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 14:41
     */
    @ApiOperation(value = "新增项目")
    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @PostMapping(value = "/add")
    @Log(title = "新建项目", businessType = BusinessType.INSERT, module = "项目管理")
    public ResponseApi<Integer> add(@RequestBody RequestModel<AddProjectDto> dto) {
        try {
            int row = coProjectService.add(dto);
            return row > 0 ? ResponseApi.ok(row) : ResponseApi.fail();
        } catch (FailParamsException e) {  //捕捉入参校验异常
            return ResponseApi.fail(e.getMessage());
        } catch (ServiceException e){
            return ResponseApi.fail(e.getCode().intValue(), e.getMessage());
        } catch (Exception e) {  //其他异常
            log.error("新建项目失败", e);
            return ResponseApi.fail();
        }
    }

    /**
     * 删除项目
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 17:03
     */
    @ApiOperation(value = "删除项目")
    @PostMapping(value = "/remove")
    @Log(title = "删除项目", businessType = BusinessType.DELETE, module = "项目管理")
    public ResponseApi<Integer> remove(@RequestBody RequestModel<PrimaryKeyListDTO> dto) {
        try {
            coProjectService.remove(dto);
            return ResponseApi.ok();
        } catch (FailParamsException e) {  //捕捉入参校验异常
            return ResponseApi.fail(e.getMessage());
        } catch (Exception e) {  //其他异常
            log.error("删除项目失败", e);
            return ResponseApi.fail();
        }
    }

    /**
     * 更新项目
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 17:04
     */
    @ApiOperation(value = "更新项目")
    @PostMapping(value = "/edit")
    @Log(title = "编辑项目", businessType = BusinessType.UPDATE, module = "项目管理")
    public ResponseApi<String> edit(@RequestBody RequestModel<UpdateProjectDto> dto) {
        try {
            boolean flag = coProjectService.edit(dto);
            return flag ? ResponseApi.ok() : ResponseApi.fail();
        } catch (FailParamsException e) {  //捕捉入参校验异常
            return ResponseApi.fail(e.getMessage());
        } catch (Exception e) {  //其他异常
            log.error("编辑项目失败", e);
            return ResponseApi.fail();
        }
    }


    /**
     * 查询项目记录
     *
     * @param dto request
     * @return * @return ResponseApi<PageInfo<QueryProjectVo>>
     * @Date 2022/6/10 14:48
     */
    @ApiOperation(value = "查询项目记录")
    @PostMapping(value = "/retrieve")
    @Log(title = "查询项目列表", businessType = BusinessType.QUERY, module = "项目管理")
    public ResponseApi<PageInfo<QueryProjectVo>> query(@RequestBody RequestModel<QueryProjectDTO> dto) {
        try {
            PageInfo<QueryProjectVo> info = coProjectService.query(dto);
            return ResponseApi.ok(info);
        } catch (FailParamsException e) {  //捕捉入参校验异常
            return ResponseApi.fail(e.getMessage());
        } catch (Exception e) {  //其他异常
            e.printStackTrace();
            return ResponseApi.fail();
        }
    }

    /**
     * 查询客户
     *
     * @return * @return ResponseApi<PageInfo<QueryProjectVo>>
     * @Date 2022/6/10 14:48
     */
    @ApiOperation(value = "查询客户")
    @PostMapping(value = "/customer/retrieve")
    @Log(title = "查询客户", businessType = BusinessType.QUERY, module = "客户视图")
    public ResponseApi<List<CoCustomer>> queryCustomer(@RequestBody RequestModel<QueryCustomerDto> dto) {
        try {
            List<CoCustomer> list = coProjectService.queryCustomer(dto);
            return ResponseApi.ok(list);
        } catch (FailParamsException e) {  //捕捉入参校验异常
            return ResponseApi.fail(e.getMessage());
        } catch (Exception e) {  //其他异常
            e.printStackTrace();
            return ResponseApi.fail();
        }
    }

    @GetMapping("/detail")
    @ApiOperation(value = "项目详情")
    @Log(title = "项目详情", businessType = BusinessType.QUERY, module = "项目管理")
    public R<ProjectDetailVO> projectWorks(@RequestParam String projectId) {
        return R.success(coProjectService.projectWorks(projectId));
    }

    @GetMapping("/download")
    @ApiOperation(value = "项目附件下载")
    @Log(title = "项目附件下载", businessType = BusinessType.EXPORT, module = "项目管理")
    public void fileDownload(@ApiParam("项目id") @RequestParam String projectId, HttpServletResponse response) {
        coProjectService.fileDownload(projectId, response);
    }
}
