package com.dcas.web.controller.system;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.dcas.common.annotation.Log;
import com.dcas.common.annotation.Notify;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.enums.NotifyTypeEnum;
import com.dcas.common.model.param.ModelParam;
import com.dcas.common.model.vo.*;
import com.dcas.system.service.ISysUserService;
import com.github.pagehelper.PageInfo;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.ResponseApi;
import com.dcas.common.core.domain.TreeSelect;
import com.dcas.common.core.domain.entity.SysUser;
import com.dcas.common.enums.ChartEnum;
import com.dcas.common.exception.params.FailParamsException;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.CoOperation;
import com.dcas.common.mapper.CoOperationMapper;
import com.dcas.common.mapper.SysUserMapper;
import com.dcas.system.service.CoOperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;


/**
 * 作业管理模块控制层dev
 *
 * <AUTHOR>
 * @Date 2022/5/24 15:48
 */

@Api(tags = "作业管理")
@Slf4j
@Controller
@RequestMapping(value = "/api")
@RequiredArgsConstructor
public class CoOperationController {
    /**
     * 业务逻辑：
     * 1.过程清单分类树
     * 2.过程进度：查询分类树分支总数，每完成一个分支调用一次记为1，进度 = 已完成/总数*100%
     */

    private final CoOperationService coOperationService;
    private final CoOperationMapper coOperationMapper;
    private final SysUserMapper sysUserMapper;
    private final ISysUserService userService;

    /**
     * 新增作业
     */
    @ApiOperation(value = "新增作业")
    @ResponseBody
    @PostMapping(value = "/operation/add")
    @Log(title = "新建作业", businessType = BusinessType.INSERT, module = "评估作业")
    public R<Boolean> add(@RequestBody RequestModel<OperationAddDTO> dto) {
        coOperationService.add(dto, null);
        return R.success();
    }

    @ResponseBody
    @PutMapping("/operation/edit")
    @ApiOperation(value = "编辑作业")
    @Log(title = "编辑作业", businessType = BusinessType.UPDATE, module = "评估作业")
    public R<Boolean> update(@Validated @RequestBody OperationUpdateDTO dto) {
        coOperationService.update(dto);
        return R.success();
    }

    /**
     * 删除作业
     *
     * @param dto request
     * @return * @return ResponseApi<String>
     * @Date 2022/9/28 10:38
     */
    @ApiOperation(value = "删除作业")
    @ResponseBody
    @PostMapping(value = "/operation/remove")
    @Log(title = "删除作业", businessType = BusinessType.DELETE, module = "评估作业")
    public ResponseApi<Integer> remove(@RequestBody RequestModel<PrimaryKeyListDTO> dto) {
        try {
            int row = coOperationService.remove(dto);
            return row > 0 ? ResponseApi.ok(row) : ResponseApi.fail();
        } catch (FailParamsException e) {  //捕捉入参校验异常
            return ResponseApi.fail(e.getMessage());
        } catch (Exception e) {  //其他异常
            return ResponseApi.fail();
        }
    }


    /**
     * 更新作业状态
     *
     * @param dto request
     * @return * @return ResponseApi
     * @Date 2022/7/21 16:26
     */
    @ApiOperation(value = "更新作业状态")
    @ResponseBody
    @PostMapping(value = "/operation/edit")
    @Notify(type = NotifyTypeEnum.OTHER)
    @Log(title = "更新作业状态", businessType = BusinessType.UPDATE, module = "评估作业")
    public ResponseApi<Integer> edit(@RequestBody RequestModel<UpdateOperationDto> dto) {
        return ResponseApi.ok(coOperationService.edit(dto.getPrivator()));
    }

    /**
     * 查询作业管理记录
     *
     * @param dto request
     * @return * @return ResponseApi<PageInfo<QueryOperationVo>>
     * @Date 2022/7/21 16:26
     */
    @ApiOperation("查询作业管理记录")
    @ResponseBody
    @PostMapping(value = "/operation/retrieve")
    public ResponseApi<PageInfo<QueryOperationVo>> query(@RequestBody RequestModel<QueryOperationDTO> dto) {
        return ResponseApi.ok(coOperationService.query(dto));
    }

    /**
     * 获取评估作业下拉树列表
     */
    @ApiOperation(value = "获取评估作业下拉树列表")
    @ResponseBody
    @PostMapping("/operation/treeSelect")
    public ResponseApi<List<TreeSelect>> treeSelect(@RequestBody RequestModel<OperationIdDto> dto) {
        List<TreeSelect> list = coOperationService.treeSelect(dto);
        return ResponseApi.ok(list);
    }

    @ApiOperation(tags = "创建作业", value = "获取二级菜单列表（基础调研除外）")
    @GetMapping("/operation/treeLabel")
    @ResponseBody
    public R<List<LabelVO>> qryTreeLabel() {
        return R.success(coOperationService.qryTreeLabel());
    }

    @ApiOperation(tags = "创建作业", value = "查询调研模板")
    @PostMapping("/operation/template")
    @ResponseBody
    public R<LabelVO> qryTemplate(@Validated @RequestBody TemplateQueryDTO dto) {
        return R.success(coOperationService.qryTemplate(dto));
    }

    @GetMapping("/operation/type")
    @ResponseBody
    @ApiOperation(value = "是否为新作业 true：是")
    public R<Boolean> operationType(String operationId) {
        return R.success(coOperationService.operationType(operationId));
    }

    /**
     * 更新过程进度
     *
     * @param dto request
     * @return * @return ResponseApi
     * @Date 2022/8/11 16:02
     */
    @ApiOperation("更新过程进度")
    @ResponseBody
    @PostMapping(value = "/operation/progress/edit")
    @Notify(type = NotifyTypeEnum.OTHER)
    @Log(title = "更新过程进度", businessType = BusinessType.UPDATE, module = "评估作业")
    public ResponseApi editProgress(@RequestBody RequestModel<CommonDto> dto) {
        try {
            coOperationService.editProgress(dto);
            return ResponseApi.ok();
        } catch (Exception e) {  //捕捉入参校验异常
            return ResponseApi.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "问卷修改")
    @ResponseBody
    @PostMapping(value = "/operation/questionnaire/update")
    public R<Boolean> updateQuestionnaire(@Validated @RequestBody CommonDto dto) {
        coOperationService.updateQuestionnaire(dto);
        return R.success();
    }

    @ApiOperation("更新现状核验进度")
    @ResponseBody
    @PutMapping(value = "/operation/progress/verify")
    @Log(title = "更新现状核验进度", businessType = BusinessType.UPDATE, module = "评估作业")
    public R<Boolean> editVerProcess(String operationId) {
        coOperationService.editVerProcess(operationId);
        return R.success();
    }

    /**
     * 查询过程进度
     *
     * @param dto request
     * @return * @return ResponseApi
     * @Date 2022/8/11 16:42
     */
    @ApiOperation(value = "查询过程进度")
    @ResponseBody
    @PostMapping(value = "/operation/progress/retrieve")
    public ResponseApi<QueryProgressVo> retrieveProgress(@RequestBody RequestModel<PrimaryKeyDTO> dto) {
        QueryProgressVo vo = coOperationService.queryProgress(dto);
        return ResponseApi.ok(vo);
    }

    /**
     * 查询涉及行业
     *
     * @param industryName request
     * @return * @return ResponseApi
     * @Date 2022/8/11 16:42
     */
    @ApiOperation("查询涉及行业")
    @ApiImplicitParam(name = "industryName", value = "行业名称", dataType = "String", dataTypeClass = String.class)
    @ResponseBody
    @GetMapping(value = "/operation/industry/retrieve")
    public ResponseApi<List<String>> queryIndustry(String industryName) {
        List<String> industryNameList = coOperationService.queryIndustry(industryName);
        return ResponseApi.ok(industryNameList);
    }


    /**
     * 查询用户
     *
     * @return * @return ResponseApi<PageInfo<QueryProjectVo>>
     * @Date 2022/6/10 14:48
     */
    @ApiOperation(value = "查询用户")
    @ResponseBody
    @PostMapping(value = "/operation/queryUser")
    public ResponseApi<List<QueryUserVo>> queryUser(@RequestBody RequestModel<SysUser> dto) {
        List<QueryUserVo> voList = sysUserMapper.selectUserByNickName(dto.getPrivator().getNickName());
        return ResponseApi.ok(voList);
    }

    @ResponseBody
    @GetMapping("/operation/userList")
    @ApiOperation(value = "查询统一身份用户列表")
    public ResponseApi<List<SSOAccountDTO>> querySSOAccounts(SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        List<SSOAccountDTO> ssoAccountDTOList = new ArrayList<>();
        for (SysUser sysUser : list) {
            SSOAccountDTO ssoAccountDTO = new SSOAccountDTO();
            ssoAccountDTO.setAccount(sysUser.getUserName());
            ssoAccountDTO.setName(sysUser.getNickName());
            ssoAccountDTO.setStatus(sysUser.getStatus());
            ssoAccountDTO.setPhone(sysUser.getPhoneNumber());
            ssoAccountDTO.setEmail(sysUser.getEmail());
            ssoAccountDTOList.add(ssoAccountDTO);
        }
        return ResponseApi.ok(ssoAccountDTOList);
    }

    /**
     * 分配
     *
     * @return * @return ResponseApi<PageInfo<QueryProjectVo>>
     * @Date 2022/6/10 14:48
     */
    @ApiOperation(value = "分配")
    @ResponseBody
    @PostMapping(value = "/operation/divide")
    @Notify(type = NotifyTypeEnum.DISTRIBUTION)
    public ResponseApi<Integer> divide(@RequestBody RequestModel<CoOperation> dto) {
        try {
        UpdateWrapper<CoOperation> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("executor", dto.getPrivator().getExecutor());
        updateWrapper.set("reviewer", dto.getPrivator().getReviewer());
        updateWrapper.set("executor_account", dto.getPrivator().getExecutorAccount());
        updateWrapper.set("reviewer_account", dto.getPrivator().getReviewerAccount());
        updateWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        int row = coOperationMapper.update(new CoOperation(), updateWrapper);
        return ResponseApi.ok(row);
        } catch (FailParamsException e) {  //捕捉入参校验异常
            return ResponseApi.fail(e.getMessage());
        } catch (Exception e) {  //其他异常
            return ResponseApi.fail();
        }
    }

    /**
     * 导出word
     * TODO 要改一下接口路径和入参
     *
     * @param response request
     * @Date 2022/6/2 15:32
     */
    @PostMapping("/project/exportWord")
    @Log(title = "导出作业报告", businessType = BusinessType.EXPORT, module = "评估作业")
    public void exportWord(HttpServletRequest request, HttpServletResponse response, String operationId) {

        try {
            final String browserType = request.getHeader("USER-AGENT");
            String filename = System.currentTimeMillis() + "作业报告.docx";
            String encodeFilename = percentEncode(filename);
            response.setContentType("application/octet-stream; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            //添加响应头
            response.addHeader("Access-Control-Expose-Headers", "Content-Disposition,download-filename");
            if (browserType.contains("IE") || browserType.contains("Chrome")) {
                //添加响应头内容 注：utf-8'' 必须小写
                response.setHeader("Content-Disposition", "attachment; filename=" + encodeFilename + ";filename*=utf-8''" + encodeFilename);
                response.setHeader("download-filename", encodeFilename);
                log.info("USER-AGENT:......chrome........" + browserType);
            } else if (browserType.contains("Firefox")) {
                response.setHeader("Content-Disposition", "attachment; filename*=utf-8''" + encodeFilename);
                response.setHeader("download-filename", encodeFilename);
                log.info("USER-AGENT:.......Firefox......." + browserType);
            } else {
                response.setHeader("Content-Disposition", "attachment; filename=" + encodeFilename + ";filename*=utf-8''" + encodeFilename);
                response.setHeader("download-filename", encodeFilename);
                log.info("USER-AGENT:......other........" + browserType);
            }
            ExportWordChart chart0 = new ExportWordChart();
            chart0.setName("敏感数据占比图表");
            chart0.setPicture(ChartEnum.sensitiveAssetsProportionImg.getInfo());
            ExportWordChart chart1 = new ExportWordChart();
            chart1.setName("用户权限分类统计结果");
            chart1.setPicture(ChartEnum.userAuthorityTypeResult.getInfo());
            List<ExportWordChart> list = new ArrayList<>();
            list.add(chart0);
            list.add(chart1);
            ExportOperationWordDto operationWordDto = new ExportOperationWordDto();
            operationWordDto.setOperationId(operationId);
            operationWordDto.setChartList(list);
//            operationWordDto.setChartName("综合模型管理能力雷达图");
            RequestModel<ExportOperationWordDto> requestModel = new RequestModel<>();
            requestModel.setPrivator(operationWordDto);
            coOperationService.exportWord(response, requestModel);
        } catch (IOException e) {  //其他异常
            log.error("下载文件失败", e);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public static String percentEncode(String s) throws UnsupportedEncodingException {
        String encode = URLEncoder.encode(s, StandardCharsets.UTF_8.toString());
        return encode.replaceAll("\\+", "%20");
    }

    @ResponseBody
    @GetMapping("/operation/detail")
    @ApiOperation(tags = "作业管理", value = "查看作业详情")
    @Log(title = "查看作业详情", businessType = BusinessType.QUERY, module = "评估作业")
    public R<OperationContentVO> detail(String operationId) {
        return R.success(coOperationService.detail(operationId));
    }

    /**
     * 复制作业
     */
    @ApiOperation(value = "复制作业")
    @ResponseBody
    @PostMapping("/operation/copyjob")
    @Log(title = "复制作业", businessType = BusinessType.COPY, module = "评估作业")
    public R<OperationCopyVO> copyJob(@RequestBody RequestModel<OperationCopyDTO> dto) {
        OperationCopyVO operationCopyVO = coOperationService.copyJob(dto);
        return R.success(operationCopyVO);
    }

    /**
     * 应用风险评估模板
     */
    @ApiOperation(value = "应用风险评估模板")
    @PostMapping("/operation/use-template")
    @ResponseBody
    @Log(title = "应用风险评估模板", businessType = BusinessType.OTHER, module = "评估作业")
    public R<Boolean> useTemplate(@RequestBody ModelParam modelParam) {
        return R.success(coOperationService.useTemplate(modelParam));
    }

    /**
     * 获取可用风险评估模板列表
     */
    @ApiOperation(value = "获取可用风险评估模板列表")
    @ResponseBody
    @GetMapping("/operation/list-template")
    public R<List<SelectedKeyValueVO<Long, String>>> listTemplate(@ApiParam("作业ID") @RequestParam(value = "operationId", required = false) String operationId) {
        return R.success(coOperationService.listTemplate(operationId));
    }

    /**
     * 获取风险评估结果
     */
    @ApiOperation(value = "获取风险评估结果")
    @ResponseBody
    @GetMapping("/operation/get-risk-analysis-result")
    public R<RiskAnalysisResultVO> getRiskAnalysisResult(
        @ApiParam("作业ID") @RequestParam(value = "operationId") String operationId,
        @ApiParam("页码")@RequestParam("pageNum") Integer pageNum,
        @ApiParam("每页大小") @RequestParam("pageSize") Integer pageSize,
        @ApiParam("搜索类型") @RequestParam(value = "searchType", required = false) String searchType,
        @ApiParam("搜索内容") @RequestParam(value = "searchContent", required = false) String searchContent) {
        return R.success(coOperationService.getRiskAnalysisResult(operationId, pageNum, pageSize, searchType, searchContent));
    }

    /**
     * 获取专项评估内容
     */
    @ApiOperation(value = "获取专项评估内容")
    @ResponseBody
    @PostMapping("/operation/spec/list")
    public R<List<SpecVO>> getSpecList(@Validated @RequestBody TemplateQueryDTO dto) {
        return R.success(coOperationService.getSpecList(dto));
    }
}
