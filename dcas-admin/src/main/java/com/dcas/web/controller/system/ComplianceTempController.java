package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.enums.LogType;
import com.dcas.common.utils.Func;
import com.dcas.common.utils.PageResult;
import com.dcas.common.domain.entity.ComplianceTemplate;
import com.dcas.common.model.param.ComplianceTemParam;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.TemplateSearchReq;
import com.dcas.common.model.vo.ComplianceTemVO;
import com.dcas.system.service.ComplianceTempService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/2 16:52
 * @since 1.2.0
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/compliance")
@Api(tags = "合规模板")
public class ComplianceTempController {

    private final ComplianceTempService complianceTempService;

    @PostMapping("/template")
    @ApiOperation(tags = "合规模板", value = "添加合规模板")
    @Log(title = "添加合规模板", businessType = BusinessType.INSERT, logType = LogType.SYSTEM, module = "合规模板")
    public R<Boolean> addCompliance(@Validated @RequestBody ComplianceTemParam param) {
        complianceTempService.addCompliance(param);
        return R.success();
    }

    @GetMapping("/list")
    @ApiOperation(tags = "合规模板", value = "分页查询合法合规模板列表")
    public R<PageResult<ComplianceTemplate>> list(@RequestParam(value = "currentPage", defaultValue = "1") @ApiParam("当前页码") Integer currentPage,
                                                  @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam("每页数量") Integer pageSize) {
        return R.success(complianceTempService.list(currentPage, pageSize));
    }

    @PutMapping("/template")
    @ApiOperation(tags = "合规模板", value = "修改合规模板")
    @Log(title = "修改合规模板", businessType = BusinessType.UPDATE, logType = LogType.SYSTEM, module = "合规模板")
    public R<Boolean> updateCompliance(@Validated @RequestBody ComplianceTemParam param) {
        complianceTempService.updateCompliance(param);
        return R.success();
    }

    @PostMapping("/delete")
    @ApiOperation(tags = "合规模板", value = "删除合规模板")
    @Log(title = "删除合规模板", businessType = BusinessType.DELETE, logType = LogType.SYSTEM, module = "合规模板")
    public R<Boolean> deleteCompliance(@Validated @RequestBody IdsReq req) {
        complianceTempService.deleteCompliance(req);
        return R.success();
    }

    @PutMapping("/default")
    @ApiOperation(tags = "合规模板", value = "设为默认模板")
    @Log(title = "设为默认模板", businessType = BusinessType.OTHER, logType = LogType.SYSTEM, module = "合规模板")
    public R<Boolean> setDefault(Integer templateId) {
        complianceTempService.setDefault(templateId);
        return R.success();
    }

    @PutMapping("/enable")
    @ApiOperation(tags = "合规模板", value = "启用禁用")
    @Log(title = "启用禁用", businessType = BusinessType.ENABLE, logType = LogType.SYSTEM, module = "合规模板")
    public R<Boolean> enableCompliance(Integer templateId) {
        complianceTempService.enableCompliance(templateId);
        return R.success();
    }

    @PostMapping("/bak")
    @ApiOperation(tags = "合规模板", value = "创建副本")
    @Log(title = "创建副本", businessType = BusinessType.OTHER, logType = LogType.SYSTEM, module = "合规模板")
    public R<Boolean> createBak(@RequestBody ComplianceTemParam req) {
        complianceTempService.createBak(req);
        return R.success();
    }

    @GetMapping("/template")
    @ApiOperation(tags = "合规模板", value = "查询合规模板详情")
    public R<ComplianceTemVO> details(Integer templateId) {
        return R.success(complianceTempService.details(templateId));
    }

    @PostMapping("/search")
    @ApiOperation(tags = "合规模板", value = "搜索合规模板")
    public R<List<ComplianceTemplate>> search(@RequestBody TemplateSearchReq req) {
        return R.success(complianceTempService.search(req));
    }

    @GetMapping("/download")
    @ApiOperation(tags = "合规模板", value = "模板下载")
    public void download(HttpServletResponse response) {
        Func.writeTemplate(response, "complianceTemplate.xlsx", "complianceTemplate.xlsx");
    }
}
