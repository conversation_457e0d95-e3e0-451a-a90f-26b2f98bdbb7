package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.utils.Func;
import com.dcas.common.utils.PageResult;
import com.dcas.common.model.dto.TemplateGenerateDTO;
import com.dcas.common.domain.entity.Question;
import com.dcas.common.domain.entity.Template;
import com.dcas.common.model.req.*;
import com.dcas.common.model.vo.TemplateContentVO;
import com.dcas.common.model.vo.TemplateQuestionVO;
import com.dcas.system.service.TemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/13 14:13
 * @since 1.2.0
 */
@RestController
@RequestMapping("/api/template")
@RequiredArgsConstructor
@Api(tags = "调研模板")
public class TemplateController {

    private final TemplateService templateService;

    @GetMapping("/list")
    @Log(title = "查询模板列表", businessType = BusinessType.QUERY)
    public R<List<Template>> listAll() {
        return R.success(templateService.listAll());
    }

    @PostMapping("/add")
    @ApiOperation(tags = "调研模板", value = "新增调研模板")
    @Log(title = "新增调研模板", businessType = BusinessType.INSERT)
    public R<Boolean> addTemplate(@Validated @RequestBody TemplateInsertReq req) {
        templateService.addTemplate(req);
        return R.success();
    }

    @PostMapping("/delete")
    @ApiOperation(tags = "调研模板", value = "删除调研模板")
    @Log(title = "删除调研模板", businessType = BusinessType.DELETE)
    public R<Boolean> deleteTemplate(@Validated @RequestBody IdsReq req) {
        templateService.deleteTemplate(req);
        return R.success();
    }

    @PutMapping("/{templateId}")
    @ApiOperation(tags = "调研模板", value = "更新调研模板")
    @Log(title = "更新调研模板", businessType = BusinessType.UPDATE)
    public R<Boolean> updateTemplate(@RequestBody TemplateInsertReq req, @PathVariable("templateId") Long templateId) {
        templateService.updateTemplate(req, templateId);
        return R.success();
    }

    @PutMapping("/default/{templateId}")
    @ApiOperation(tags = "调研模板", value = "设为默认模板")
    @Log(title = "调研模板-设为默认模板", businessType = BusinessType.UPDATE)
    public R<Boolean> setDefault(@PathVariable("templateId") Long templateId) {
        templateService.setDefault(templateId);
        return R.success();
    }

    @PutMapping("/stop/{templateId}")
    @ApiOperation(tags = "调研模板", value = "启用禁用")
    @Log(title = "调研模板-启用禁用", businessType = BusinessType.UPDATE)
    public R<Boolean> stopTemplate(@PathVariable("templateId") Long templateId) {
        templateService.stopTemplate(templateId);
        return R.success();
    }

    @PostMapping("/bak")
    @ApiOperation(tags = "调研模板", value = "创建副本")
    @Log(title = "调研模板-创建副本", businessType = BusinessType.INSERT)
    public R<Boolean> createBak(@RequestBody TemplateInsertReq req, Long bakId) {
        templateService.createBak(req, bakId);
        return R.success();
    }

    @GetMapping("/content/{templateId}")
    @ApiOperation(tags = "调研模板", value = "查询模板问题列表")
    public R<List<TemplateQuestionVO>> queryQuestion(@PathVariable("templateId") Long templateId, String objectId) {
        return R.success(templateService.queryQuestion(templateId, objectId));
    }

    @PostMapping("/question/sort")
    @ApiOperation(tags = "调研模板", value = "模板问题排序")
    @Log(title = "调研模板-模板问题排序", businessType = BusinessType.OTHER)
    public R<Boolean> questionSorting(@RequestBody QuestionSortIngReq req) {
        templateService.questionSorting(req);
        return R.success();
    }

    @PostMapping("/batch/delete")
    @ApiOperation(tags = "调研模板", value = "批量删除模板问题")
    @Log(title = "调研模板-批量删除模板问题", businessType = BusinessType.DELETE)
    public R<Boolean> deleteQuestion(@Validated @RequestBody TemplateQuestionReq req) {
        templateService.deleteQuestion(req);
        return R.success();
    }

    @GetMapping("/exclude/questions")
    @ApiOperation(tags = "调研模板", value = "问题库中查询模板中不存在的问题列表")
    public R<List<Question>> selectExcludeQuestion(Long templateId) {
        return R.success(templateService.selectExcludeQuestion(templateId));
    }

    @PostMapping("/batch/add")
    @ApiOperation(tags = "调研模板", value = "批量新增模板问题")
    @Log(title = "调研模板-批量新增模板问题", businessType = BusinessType.INSERT)
    public R<Boolean> addQuestion(@Validated @RequestBody TemplateQuestionReq req) {
        templateService.addQuestion(req);
        return R.success();
    }

    @GetMapping("/question/{templateId}")
    @ApiOperation(tags = "调研模板", value = "查询模板内容")
    public R<List<TemplateContentVO>> queryContent(String questionId, @PathVariable("templateId") Long templateId) {
        return R.success(templateService.queryContent(questionId, templateId));
    }

    @PutMapping("/content/status")
    @ApiOperation(tags = "调研模板", value = "模板问题启用/禁用")
    @Log(title = "调研模板-模板问题启用/禁用", businessType = BusinessType.UPDATE)
    public R<Boolean> updateStatus(@RequestBody ContentStatusReq req) {
        templateService.updateStatus(req);
        return R.success();
    }

    @PostMapping("/item/add")
    @ApiOperation(tags = "调研模板", value = "添加核查项至模板问题")
    @Log(title = "调研模板-添加核查项至模板问题", businessType = BusinessType.INSERT)
    public R<Boolean> insertItemInTemplate(@Validated @RequestBody ItemInsertReq req) {
        templateService.insertItemInTemplate(req);
        return R.success();
    }

    @DeleteMapping("/content/{contentId}")
    @ApiOperation(tags = "调研模板", value = "删除模板内容")
    @Log(title = "调研模板-删除模板内容", businessType = BusinessType.DELETE)
    public R<Boolean> deleteContentById(@Validated @PathVariable("contentId") Long contentId) {
        templateService.deleteContentById(contentId);
        return R.success();
    }

    @PostMapping("/produce")
    @ApiOperation(tags = "调研模板", value = "生成调研模板")
    @Log(title = "调研模板-生成调研模板", businessType = BusinessType.OTHER)
    public R<Boolean> templateProduce(@Validated @RequestBody TemplateGenerateDTO dto) {
        templateService.templateProduce(dto);
        return R.success();
    }

    @PostMapping("/search")
    @ApiOperation(tags = "调研模板", value = "搜索调研模板")
    public R<List<Template>> searchTemplate(@RequestBody TemplateSearchReq req) {
        return R.success(templateService.searchTemplate(req));
    }

    @PostMapping("/search/question")
    @ApiOperation(tags = "调研模板", value = "搜索调研模板问题")
    public R<List<TemplateQuestionVO>> searchTemplateQuestion(@Validated @RequestBody TemplateQueSearchReq req) {
        return R.success(templateService.searchTemplateQuestion(req));
    }

    @GetMapping("/download")
    @ApiOperation(tags = "调研模板", value = "模板下载")
    @Log(title = "调研模板-模板下载", businessType = BusinessType.EXPORT)
    public void download(HttpServletResponse response) {
        Func.writeTemplate(response, "researchTemplate.xlsx", "researchTemplate.xlsx");
    }
}
