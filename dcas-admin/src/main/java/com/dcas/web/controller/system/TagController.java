package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.model.dto.TagsDTO;
import com.dcas.common.model.dto.TextDTO;
import com.dcas.common.domain.entity.Tag;
import com.dcas.system.service.TagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/9 15:37
 * @since 1.2.0
 */
@RestController
@RequestMapping("/api/tag")
@RequiredArgsConstructor
@Api(tags = "标签管理")
public class TagController {

    private final TagService tagService;

    @PostMapping("/generate")
    @ApiOperation(tags = "匹配标签", value = "标签生成")
    @Log(title = "标签生成", businessType = BusinessType.INSERT)
    public R<Set<String>> generate(@Validated @RequestBody TextDTO text) {
        return R.success(tagService.generate(text.getText()));
    }

    @PutMapping("/saveBatch")
    @ApiOperation(tags = "匹配标签", value = "批量保存标签")
    @Log(title = "批量保存标签", businessType = BusinessType.INSERT)
    public R<Boolean> saveBatch(TagsDTO dto) {
        tagService.saveBatch(dto.getTags());
        return R.success();
    }

    @GetMapping("/list")
    @ApiOperation(tags = "匹配标签", value = "查询所有标签")
    @Log(title = "查询所有标签", businessType = BusinessType.QUERY)
    public R<List<Tag>> listAll() {
        return R.success(tagService.listAll());
    }

    @GetMapping("/genSave")
    @Log(title = "生成并保存标签", businessType = BusinessType.INSERT)
    public R<Boolean> generateAndSave(String text) {
        tagService.saveBatch(tagService.generate(text));
        return R.success();
    }
}
