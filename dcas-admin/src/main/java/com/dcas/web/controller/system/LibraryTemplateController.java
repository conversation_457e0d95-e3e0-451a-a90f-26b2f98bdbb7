package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.domain.entity.AnalysisTemplate;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.enums.LogType;
import com.dcas.common.enums.TemplateTypeEnum;
import com.dcas.common.model.param.DiscoveryJobParam;
import com.dcas.common.model.vo.LibraryTemplateVO;
import com.dcas.common.utils.PageResult;
import com.dcas.system.service.LibraryTemplateConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @className LibraryTemplateController
 * @description 知识库模板controller
 * @date 2024/05/15 17:06
 */
@Api(tags = "知识库模板")
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api/library/template")
public class LibraryTemplateController {

    private final LibraryTemplateConfigService libraryTemplateConfigService;


    @GetMapping("/list")
    @ApiOperation(tags = "知识库模版", value = "分页查询模板")
    public R<PageResult<LibraryTemplateVO>> list(
        @RequestParam(value = "currentPage", defaultValue = "1") @ApiParam("当前页码") Integer currentPage,
        @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam("每页数量") Integer pageSize,
        @RequestParam(value = "type") @ApiParam("模板类型") TemplateTypeEnum type) {
        return R.success(libraryTemplateConfigService.list(currentPage, pageSize, type));
    }

    @PutMapping("/enable")
    @ApiOperation(tags = "知识库模版", value = "启用禁用")
    @Log(title = "知识库模版-启用禁用", businessType = BusinessType.ENABLE, logType = LogType.SYSTEM, module = "规则管理")
    public R<Boolean> enable(@RequestParam(value = "id") @ApiParam("模板ID")Integer id,
        @RequestParam(value = "type") @ApiParam("模板类型") TemplateTypeEnum type) {
        libraryTemplateConfigService.enable(id, type);
        return R.success();
    }
}
