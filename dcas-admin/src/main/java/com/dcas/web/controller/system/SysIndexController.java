package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.core.domain.ResponseApi;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.model.vo.AuthNodeVO;
import com.dcas.system.service.IMcCenterService;
import com.mchz.starter.sso.annotation.WhiteList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dcas.common.config.SafetyConfig;
import com.dcas.common.utils.StringUtils;

import java.util.List;

/**
 * 首页
 *
 * <AUTHOR>
 */
@Api(tags = "首页")
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class SysIndexController {
    private final IMcCenterService mcCenterService;

    /**
     * 查询统一身份登录首页
     *
     * @return 统一身份首页地址
     */
    @WhiteList
    @GetMapping("/loginIndex")
    public ResponseApi<String> queryLoginIndex() {
        return ResponseApi.ok(mcCenterService.queryLoginIndex());
    }

    @GetMapping("/auth")
    @ApiOperation(value = "查询用户权限")
    public R<List<AuthNodeVO>> queryAuth() {
        return R.success(mcCenterService.queryAuth());
    }
}
