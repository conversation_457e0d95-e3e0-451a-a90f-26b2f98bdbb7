# 项目相关配置
safety:
  # 名称
  name: DCAS
  # 版本
  version: *******
  # 版权年份
  copyrightYear: 2022
  scheduling:
    # 定时任务开关
    enabled: true
  # 实例演示开关
  demoEnabled: false
  # 文件路径 示例（ Windows配置D:/dcas/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /dcas/storage
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: char
  # 统一身份组件接口
  identityAddApi: http://*************:18020/application/_?cmd=add
  identityActivatedApi: http://*************:18020/application/_?cmd=activated
  identityStatusApi: http://*************:18020/application/_/status
  dark-host: http://**************
  scan-host: https://***************:23000
  net:
    script-path: /etc/sysconfig/network-scripts/ifcfg-ens33

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.dcas: debug
    org.springframework: info
    org.docx4j: info

tlog:
  id-generator: com.dcas.web.core.config.ShortUUIDTLogIdGenerator

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10
  #用户请求接口权限，普通用户
  perms:
    common: operation:add,operation:edit,operation:delete,operation:query,operation:treeSelect,operation:use-template,operation:list-template,operation:get-risk-analysis-result,project:add,project:edit,project:retrieve,system:user:list
  zip:
    password: MckJ$2023@.Dcas


# Spring配置
spring:
  mvc:
    servlet:
      load-on-startup: 1
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: org.postgresql.Driver
    druid:
      connectTimeout: 2000
      socketTimeout: 30000
      keepAlive: true
      # 主库数据源
      master:
        url: ******************************************************************************************************************************************************************
        username: dcas
        password: MckJ$2023@.Dcas
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT version()
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: ruoyi
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  flyway:
    enabled: true
    clean-disabled: true
    validate-on-migrate: true
    baseline-on-migrate: true
    baseline-version: 0

  # 全局时间格式化 返回参数Date类型转换配置 ex:2022-08-25 16:35:10
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 1024MB
      # 设置总上传的文件大小
      max-request-size: 1024MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 600

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名(实体类所在包)
  typeAliasesPackage: com.dcas.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: postgresql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

dcas:
  coc:
    host: http://dcas-coc.dev.mchz.com.cn
    timeOut: 30000
    dumpPath: /dcas/storage/dump

# 资产发现配置
discovery:
  sys:
    enableCollectView: false
    discoveryTaskSize: 5
    samplingThreadTotalSize: 32
    analyzeThreadTotalSize: 16

# 能力市场模块配置
market:
  base_dir: /opt
  json_dir: ""
  resource_dir: /opt/market/resource/
  install_dir: /opt/market/apps
ssh:
  strictHostKeyChecking: no
  timeout: 30000
  ip: 127.0.0.1
  port: 6222
  username: root
  password: MckJ$2023@.Dcas#pub
  charset: UTF-8
# docker客户端配置，默认：unix:///var/run/docker.sock或者tcp://127.0.0.1:2375
docker:
  host: tcp://127.0.0.1:2375
  cert:
    path: /dcas/docker/cert/


# 统一身份
mc:
  sso:
    user-center:
      host: https://***************:18020
      app-id:
      app-secret:
    app:
      name: 美创数据安全综合评估系统
      version: 2.3.0
      type: 数据安全管理平台
      index-url: https://***************:4433/#/auth
    auto-activation-scheme: start
    timout: 1000
    enabled: true

ehcache:
  directory: ./cache
  heap-size-mb: 16
  disk-size-gb: 4
