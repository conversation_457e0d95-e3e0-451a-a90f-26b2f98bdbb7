-- Add unique constraint to prevent duplicate questionnaire records
-- This prevents duplicate saves when users double-click on the frontend

-- First, remove any existing duplicates (if any)
DELETE FROM questionnaire q1 
WHERE q1.id NOT IN (
    SELECT MIN(q2.id) 
    FROM questionnaire q2 
    WHERE q2.operation_id = q1.operation_id 
    AND q2.object_id = q1.object_id 
    AND q2.question_id = q1.question_id
);

-- Add unique constraint
ALTER TABLE questionnaire 
ADD CONSTRAINT uk_questionnaire_operation_object_question 
UNIQUE (operation_id, object_id, question_id);

-- Add comment for documentation
COMMENT ON CONSTRAINT uk_questionnaire_operation_object_question ON questionnaire 
IS 'Prevents duplicate questionnaire records for the same operation, object, and question combination';
