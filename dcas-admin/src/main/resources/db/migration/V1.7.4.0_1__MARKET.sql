CREATE TABLE IF NOT EXISTS mk_app (
	id serial NOT NULL,
	"name" varchar(64) NOT NULL,
	"key" varchar(64) NULL,
	short_desc_zh varchar(1000) NULL,
	short_desc_en varchar(1000) NULL,
	icon text NULL,
	"type" varchar(64) NOT NULL,
	status varchar(64) NOT NULL,
	required varchar(64) NULL,
	cross_version_update bool NULL,
	"limit_count" int NOT NULL DEFAULT 0,
	website varchar(255) NULL,
	github varchar(255) NULL,
	"document" varchar(255) NULL,
	recommend int NULL,
	resource varchar(64) NULL DEFAULT 'remote',
	read_me text NULL,
	last_modified bigint NULL,
	created_at timestamp NULL,
	updated_at timestamp NULL
);
CREATE UNIQUE INDEX IF NOT EXISTS mk_app_un_idx ON mk_app ("name");
COMMENT ON TABLE mk_app IS 'app应用表';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN mk_app.id IS '主键，自增';
COMMENT ON COLUMN mk_app."name" IS 'app名称';
COMMENT ON COLUMN mk_app."key" IS 'key值';
COMMENT ON COLUMN mk_app.short_desc_zh IS '中文简介';
COMMENT ON COLUMN mk_app.short_desc_en IS '英文简介';
COMMENT ON COLUMN mk_app.icon IS '应用图标';
COMMENT ON COLUMN mk_app."type" IS '类型';
COMMENT ON COLUMN mk_app.status IS '状态';
COMMENT ON COLUMN mk_app.required IS '必须';
COMMENT ON COLUMN mk_app.cross_version_update IS '是否支持跨版本升级';
COMMENT ON COLUMN mk_app."limit_count" IS '限制数量';
COMMENT ON COLUMN mk_app.website IS '网址';
COMMENT ON COLUMN mk_app.github IS 'github地址';
COMMENT ON COLUMN mk_app."document" IS '在线文档地址';
COMMENT ON COLUMN mk_app.recommend IS '建议指数';
COMMENT ON COLUMN mk_app.resource IS '资源方式';
COMMENT ON COLUMN mk_app.read_me IS 'readme';
COMMENT ON COLUMN mk_app.last_modified IS '最后修改时间';
COMMENT ON COLUMN mk_app.created_at IS '创建时间';
COMMENT ON COLUMN mk_app.updated_at IS '修改时间';

CREATE TABLE IF NOT EXISTS mk_app_detail (
	id serial NOT NULL,
	app_id int NOT NULL,
	"version" varchar(64) NOT NULL,
	params varchar(4000) NULL,
	docker_compose text NULL,
	status varchar(64) NOT NULL,
	last_version varchar(64) NULL,
	last_modified bigint NULL,
	download_url varchar(255) NULL,
	download_call_back_url varchar(255) NULL,
	"need_update" bool NULL,
	ignore_upgrade bool NULL,
	created_at timestamp NULL,
	updated_at timestamp NULL
);
COMMENT ON TABLE mk_app_detail IS 'app应用详情表';

-- Column comments

COMMENT ON COLUMN mk_app_detail.id IS '主键ID，自增';
COMMENT ON COLUMN mk_app_detail.app_id IS 'app表ID';
COMMENT ON COLUMN mk_app_detail."version" IS '应用版本';
COMMENT ON COLUMN mk_app_detail.params IS '应用参数';
COMMENT ON COLUMN mk_app_detail.docker_compose IS 'dockerCompose配置';
COMMENT ON COLUMN mk_app_detail.status IS '状态';
COMMENT ON COLUMN mk_app_detail.last_version IS '最新版本';
COMMENT ON COLUMN mk_app_detail.last_modified IS '最近修改时间';
COMMENT ON COLUMN mk_app_detail.download_url IS '下载地址';
COMMENT ON COLUMN mk_app_detail.download_call_back_url IS '下载回调地址';
COMMENT ON COLUMN mk_app_detail."need_update" IS '是否需要更新';
COMMENT ON COLUMN mk_app_detail.ignore_upgrade IS '是否忽略升级';
COMMENT ON COLUMN mk_app_detail.created_at IS '创建时间';
COMMENT ON COLUMN mk_app_detail.updated_at IS '修改时间';


CREATE TABLE  IF NOT EXISTS mk_app_install (
	id serial NOT NULL,
	"name" varchar(64) NOT NULL,
	"key" varchar(64) NOT NULL,
	app_id int NOT NULL,
	app_detail_id int NOT NULL,
	"version" varchar(64) NOT NULL,
	param varchar(2000) NULL,
	env varchar(2000) NULL,
	docker_compose text NULL,
	status varchar(64) NOT NULL,
	description varchar(255) NULL,
	message varchar(2000) NULL,
	container_name varchar(64) NOT NULL,
	service_name varchar(64) NOT NULL,
	http_port int NOT NULL,
	https_port int NOT NULL,
	created_at timestamp NULL,
	updated_at timestamp NULL
);
CREATE UNIQUE INDEX IF NOT EXISTS mk_app_install_un_constraint ON mk_app_install ("name");
COMMENT ON TABLE mk_app_install IS 'app应用安装记录表';

-- Column comments

COMMENT ON COLUMN mk_app_install.id IS '主键ID，自增';
COMMENT ON COLUMN mk_app_install."name" IS '应用名称';
COMMENT ON COLUMN mk_app_install."key" IS '唯一标识名';
COMMENT ON COLUMN mk_app_install.app_id IS 'app表ID';
COMMENT ON COLUMN mk_app_install.app_detail_id IS 'app应用详情表ID';
COMMENT ON COLUMN mk_app_install."version" IS '安装版本';
COMMENT ON COLUMN mk_app_install.param IS '应用参数';
COMMENT ON COLUMN mk_app_install.env IS '安装环境变量';
COMMENT ON COLUMN mk_app_install.docker_compose IS 'dockercompose配置';
COMMENT ON COLUMN mk_app_install.status IS '状态';
COMMENT ON COLUMN mk_app_install.description IS '描述';
COMMENT ON COLUMN mk_app_install.message IS 'Message';
COMMENT ON COLUMN mk_app_install.container_name IS '容器名称';
COMMENT ON COLUMN mk_app_install.service_name IS '服务名称';
COMMENT ON COLUMN mk_app_install.http_port IS 'http端口';
COMMENT ON COLUMN mk_app_install.https_port IS 'https端口';
COMMENT ON COLUMN mk_app_install.created_at IS '创建时间';
COMMENT ON COLUMN mk_app_install.updated_at IS '修改时间';

CREATE TABLE  IF NOT EXISTS mk_app_install_resource (
	id serial NOT NULL,
	app_install_id int NOT NULL,
	link_id int NOT NULL,
	resource_id int NULL,
	"key" varchar(64) NOT NULL,
	"from" varchar(64) NOT NULL DEFAULT 'local',
	created_at timestamp NULL,
	updated_at timestamp NULL
);
COMMENT ON TABLE mk_app_install_resource IS 'app应用安装资源';

-- Column comments

COMMENT ON COLUMN mk_app_install_resource.id IS '主键ID，自增';
COMMENT ON COLUMN mk_app_install_resource.app_install_id IS 'app应用安装记录表ID';
COMMENT ON COLUMN mk_app_install_resource.link_id IS '链接ID';
COMMENT ON COLUMN mk_app_install_resource.resource_id IS '资源ID';
COMMENT ON COLUMN mk_app_install_resource."key" IS '关键字';
COMMENT ON COLUMN mk_app_install_resource."from" IS '来源';
COMMENT ON COLUMN mk_app_install_resource.created_at IS '创建时间';
COMMENT ON COLUMN mk_app_install_resource.updated_at IS '修改时间';

CREATE TABLE   IF NOT EXISTS mk_tag (
	id serial NOT NULL,
	"key" varchar(64) NOT NULL,
	"name" varchar(64) NOT NULL,
	"sort" int NOT NULL DEFAULT 1,
	created_at timestamp NULL,
	updated_at timestamp NULL
);
CREATE UNIQUE INDEX IF NOT EXISTS mk_tag_un_idx ON mk_tag ("key");

COMMENT ON TABLE mk_tag IS 'app应用标签表';

-- Column comments

COMMENT ON COLUMN mk_tag.id IS '主键ID，自增';
COMMENT ON COLUMN mk_tag."key" IS 'key值';
COMMENT ON COLUMN mk_tag."name" IS '标签名称';
COMMENT ON COLUMN mk_tag."sort" IS '排序';
COMMENT ON COLUMN mk_tag.created_at IS '创建时间';
COMMENT ON COLUMN mk_tag.updated_at IS '修改时间';

CREATE TABLE IF NOT EXISTS  mk_app_tag (
	id serial NOT NULL,
	app_id int NOT NULL,
	tag_id int NOT NULL,
	created_at timestamp NULL,
	updated_at timestamp NULL
);
COMMENT ON TABLE mk_app_tag IS 'app应用标签关联表';

-- Column comments

COMMENT ON COLUMN mk_app_tag.id IS '主键自增ID';
COMMENT ON COLUMN mk_app_tag.app_id IS 'app应用表ID';
COMMENT ON COLUMN mk_app_tag.tag_id IS '标签表ID';
COMMENT ON COLUMN mk_app_tag.created_at IS '创建时间';
COMMENT ON COLUMN mk_app_tag.updated_at IS '修改时间';

insert into mk_tag (id,"key", "name", sort, created_at, updated_at) values(2,'WebSite', '建站', 1, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id,"key", "name", sort, created_at, updated_at)values(3,'Database', '数据库', 2, now(), now())   ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(4,'Server', 'Web 服务器', 3, now(), now())   ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(5,'Runtime', '运行环境', 4, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(6,'Tool', '实用工具', 5, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(7,'Storage', '云存储', 6, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(8,'AI', 'AI / 大模型', 7, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(9,'BI', 'BI', 8, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(10,'Security', '安全', 9, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id,"key", "name", sort, created_at, updated_at)values(11, 'DevTool', '开发工具', 10, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(12,'DevOps', 'DevOps', 11, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(13,'Middleware', '中间件', 12, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(14, 'Media', '多媒体', 13, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at)values(15, 'Email', '邮件服务', 14, now(), now())  ON conflict("key") do nothing;
insert into mk_tag(id, "key", "name", sort, created_at, updated_at) values(16,'Game', '休闲游戏', 15, now(), now())  ON conflict("key") do nothing;
insert into mk_tag (id, "key", "name", sort, created_at, updated_at) values(1,'Local', '本地', 99, now(), now())  ON conflict("key") do nothing;

Do $$
BEGIN
BEGIN
alter table pre_source_config
    add user_account varchar(1000);
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column user_account already exists in pre_source_config.';
END;
END;
$$;
comment on column pre_source_config.user_account is '用户账号';


Do $$
BEGIN
BEGIN
alter table sys_notify
    add user_account varchar(255);
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column user_account already exists in sys_notify.';
END;
END;
$$;
comment on column sys_notify.user_account is '用户账号';

Do $$
BEGIN
BEGIN
alter table co_operation add inapplicable bool default false;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column inapplicable already exists in co_operation.';
END;
END;
$$;
comment on column co_operation.inapplicable is '个人信息不适用';


Do $$
BEGIN
BEGIN
alter table security_questionnaire add inapplicable bool default false;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column inapplicable already exists in security_questionnaire.';
END;
END;
$$;
comment on column security_questionnaire.inapplicable is '是否不适用';

Do $$
BEGIN
BEGIN
ALTER TABLE public.spec_calc_result ADD "type" varchar(50) NULL;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column type already exists in spec_calc_result.';
END;
END;
$$;
COMMENT ON COLUMN public.spec_calc_result."type" IS '类型：ANALYSIS->分析模板，LAW->合规模板';

Do $$
BEGIN
BEGIN
ALTER TABLE public.co_legal ADD score numeric(10, 2) NULL;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column type already exists in spec_calc_result.';
END;
END;
$$;
COMMENT ON COLUMN public.co_legal.score IS '分数';

select add_column_to_tables('special_evaluation_config', 'rel_law_id', 'int4');
select add_column_to_tables('special_evaluation_config', 'calc_template', 'varchar(50)');

alter table pre_source_config
    alter column user_id drop not null;


Do $$
BEGIN
BEGIN
alter table public.sys_notify
    add user_account varchar(255);
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column user_account already exists in sys_notify.';
END;
END;
$$;
comment on column public.sys_notify.user_account is '用户账号';


Do $$
BEGIN
BEGIN
alter table public.sys_notify
    add data_id varchar(64);
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column data_id already exists in sys_notify.';
END;
END;
$$;
comment on column public.sys_notify.data_id is '数据id';


alter table public.co_verification
    alter column description type varchar(2000) using description::varchar(2000);


Do $$
BEGIN
BEGIN
alter table public.co_asset_user add db_config json;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column data_id already exists in sys_notify.';
END;
END;
$$;
comment on column public.co_asset_user.db_config is '数据源配置';
