# 问卷重复保存问题解决方案

## 问题描述
用户在前端双击保存按钮时，会导致同一份问卷和问卷内容被重复保存到数据库中。

## 解决方案概述

我们实施了多层防护策略来防止重复保存：

### 1. 数据库层面 - 唯一约束（推荐）
**文件**: `dcas-admin/src/main/resources/db/migration/V2.1.6.0_1__QUESTIONNAIRE_UNIQUE_CONSTRAINT.sql`

添加了数据库唯一约束，防止在 `(operation_id, object_id, question_id)` 组合上出现重复记录。

```sql
ALTER TABLE questionnaire 
ADD CONSTRAINT uk_questionnaire_operation_object_question 
UNIQUE (operation_id, object_id, question_id);
```

**优点**:
- 最可靠的数据完整性保证
- 适用于多实例部署
- 即使应用层逻辑失败也能防止重复

### 2. 应用层面 - Redis分布式锁
**修改文件**: `dcas-system/src/main/java/com/dcas/system/service/impl/QuestionnaireServiceImpl.java`

在 `saveAndUpdate` 方法中实现了基于Redis的分布式锁机制：

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void saveAndUpdate(QuestionnaireContentReq req) {
    // 使用Redis简单锁防止并发重复保存
    String lockKey = String.format("questionnaire:save:%s:%s", req.getOperationId(), req.getLabelId());
    String lockValue = String.valueOf(System.currentTimeMillis());
    
    try {
        // 尝试获取锁，30秒过期
        boolean lockAcquired = tryAcquireLock(lockKey, lockValue, 30);
        if (!lockAcquired) {
            throw new ServiceException("系统繁忙，请稍后重试");
        }
        
        saveAndUpdateInternal(req);
    } finally {
        // 释放锁
        releaseLock(lockKey, lockValue);
    }
}
```

**优点**:
- 防止并发操作
- 适用于分布式环境
- 提供用户友好的错误提示

### 3. 前端层面 - 防抖处理（建议实施）
**文件**: `frontend-utils/debounce-util.js`

提供了前端防抖工具类，可以防止用户快速点击：

```javascript
// 使用示例
debounceUtil.debounceClick(
    document.getElementById('saveButton'),
    async (event) => {
        await saveQuestionnaire(data);
    },
    500
);
```

## 实施的具体改动

### 1. QuestionnaireServiceImpl 主要变更

1. **新增锁机制方法**:
   - `tryAcquireLock()`: 获取Redis锁
   - `releaseLock()`: 释放Redis锁
   - `saveAndUpdateInternal()`: 内部保存逻辑

2. **增强错误处理**:
   - 捕获数据库唯一约束违反异常
   - 自动从保存模式切换到更新模式
   - 添加详细的日志记录

3. **并发安全**:
   - 在锁保护下重新检查记录是否存在
   - 防止竞态条件

### 2. 数据库迁移脚本

- 清理现有重复数据
- 添加唯一约束
- 添加约束说明注释

## 编译问题解决

当前遇到的编译错误是由于Java版本不匹配导致的：
- 项目配置为Java 1.8
- 当前运行环境为Java 21
- Maven编译插件版本过旧(3.1)

**解决方案**:
1. 使用Java 1.8环境编译
2. 或者升级项目配置以支持Java 21

## 测试建议

1. **单元测试**: 测试锁机制和异常处理
2. **并发测试**: 模拟多用户同时保存
3. **数据库测试**: 验证唯一约束生效
4. **前端测试**: 验证防抖功能

## 部署步骤

1. **数据库迁移**: 执行SQL脚本添加唯一约束
2. **应用部署**: 部署包含锁机制的新版本
3. **前端更新**: 集成防抖工具类
4. **监控验证**: 检查日志确认功能正常

## 监控和维护

- 监控Redis锁的获取和释放
- 检查数据库约束违反日志
- 观察用户体验改善情况
- 定期清理过期的锁键（Redis自动过期）

这个解决方案提供了多层防护，确保在各种情况下都能有效防止重复保存问题。
