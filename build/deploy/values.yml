---
safety:
  dark:
    host: https://127.0.0.1:8081
  scan:
    host: https://127.0.0.1:23000
dcas:
  coc:
    host: https://*************:8086
dcasSystem:
  migration_enabled: False
  migration_user:
  migration_password:
  migration_adapter: postgresql
  migration_db:
  migration_host:
  migration_port: 5432
spring:
  redis:
    host: 127.0.0.1
    port: 6379
  database:
    host: 127.0.0.1
    port: 6433
    dbname: dcas
    username: <PERSON><PERSON>(LLNnCiw2ESVZMn5yyUBueOS7cddtJXK9vSFlx/x47ray2SuZcvpNRFljqib4asYzSGQK6O2HOr6uVbwixU396mJiKaJgdTP4icomN1KV64ZZTijL9PaMSW1j3dXlw3tviadMUnXpD0C1lsW02i09qZcYs4+iH9EEs1C0BCy94ik=)
    password: ENC(kVq806xdkhZrrWVJ2NACaaA5pxpmW8AcqCQeOqNb5zMnNd1FQymq0x2G9vardjl3bKvBJVh+Wiu0fTqEjdUDtvSs5iHqI41Gxwy3+H8NLP8twyDc2BgUxYmF5+wzN5Fk11YnZpsjXIzrIcBfLruXe9iuXOLbMRa9KLFZAqiCppE=)

mcCenter:
  sso:
    host: https://127.0.0.1:18020
  app:
    name: 美创数据安全综合评估系统
    version: 2.3.0
    type: 数据安全管理平台
    indexUrl: https://127.0.0.1:4433/#/auth