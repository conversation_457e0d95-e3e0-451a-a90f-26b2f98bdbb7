safety:
  name: DCAS
  version: *******
  copyrightYear: 2022
  demoEnabled: false
  profile: /dcas/storage
  addressEnabled: false
  captchaType: char
  identityAddApi: http://*************:18020/application/_?cmd=add
  identityActivatedApi: http://*************:18020/application/_?cmd=activated
  identityStatusApi: http://*************:18020/application/_/status
  dark-host: {{ safety.dark.host }}
  scan-host: {{ safety.scan.host }}
  net:
    script-path: /etc/sysconfig/network-scripts/ifcfg-ens33

server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    accept-count: 1000
    threads:
      max: 800
      min-spare: 100

logging:
  level:
    com.dcas: debug
    org.springframework: warn

tlog:
  id-generator: com.dcas.web.core.config.ShortUUIDTLogIdGenerator

user:
  password:
    maxRetryCount: 5
    lockTime: 10
  perms:
    common: operation:add,operation:edit,operation:delete,operation:query,operation:copyjob,operation:treeSelect,operation:use-template,operation:list-template,operation:get-risk-analysis-result,project:add,project:edit,project:retrieve,system:user:list
  zip:
    password: ENC(iIaqA/TWS8CRmrMchsgJPrJvK78vDF6Fd5FLy1uQBGM4M25QDx3E46c1EY6ukS3QNoAr57PAZmjsuGQfdXefEags8V4+BmXc29sTaRj5gGQnf/VgJOaee2CDxz9w5e3hxjEp6wYLVgHtEXGFXiEgr0Sa9wOKS5K2s8slmDoVnl0=)

spring:
  mvc:
    servlet:
      load-on-startup: 1
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: org.postgresql.Driver
    druid:
      connectTimeout: 2000
      socketTimeout: 30000
      keepAlive: true
      master:
        url: jdbc:postgresql://{{ spring.database.host }}:{{ spring.database.port }}/{{ spring.database.dbname }}?useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&serverTimezone=Asia/Shanghai
        username: {{ spring.database.username }}
        password: {{ spring.database.password }}
      initialSize: 5
      minIdle: 10
      maxActive: 20
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT version()
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: dcas
        login-password: 123456
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  messages:
    basename: i18n/messages
  profiles:
    active: druid
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  devtools:
    restart:
      enabled: true
  redis:
    host: {{ spring.redis.host }}
    port: {{ spring.redis.port }}
    database: 0
    password:
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
  flyway:
    enabled: true
    clean-disabled: true
    validate-on-migrate: true
    baseline-on-migrate: true
    baseline-version: 0

token:
  header: Authorization
  secret: abcdefghijklmnopqrstuvwxyz
  expireTime: 60

mybatis-plus:
  typeAliasesPackage: com.dcas.**.domain
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  configLocation: classpath:mybatis/mybatis-config.xml

pagehelper:
  helperDialect: postgresql
  supportMethodsArguments: true
  params: count=countSql

swagger:
  enabled: true
  pathMapping: /dev-api

xss:
  enabled: true
  excludes: /system/notice
  urlPatterns: /system/*,/monitor/*,/tool/*

dcas:
  coc:
    host: {{ dcas.coc.host }}
    timeOut: 30000
    dumpPath: /dcas/storage/dump

# 资产发现配置
discovery:
  sys:
    enableCollectView: false
    discoveryTaskSize: 5
    samplingThreadTotalSize: 32
    analyzeThreadTotalSize: 16


# 比对模块配置
diff:
  dir: /opt/mc/common/data/storage/diff
temp:
  dir: /opt/mc/common/data/storage/temp
# 文件存储相关信息
file1:
  delimeter: ","
  quote: "\""
  fetchRows: 100
  system: LocalFile
  client: CSVFILE
  auth: default
connection:
  pool:
    maximumPoolSize: 20
system:
  supportHashColumnName: hash_key_column
  supportStatusColumn: ods_delete_flag
  extractFetchSize: 100
  queueSize: 100
  repairFailedCount: 1000
  batchRrepairCount: 100
  degradeStrategyTables:
  fromCharset:
  toCharset:
  bigColumnTypeHash: true
  hdfsDataPath: "/user/hive/warehouse/${table}"
  openJobFinishCallBack: true
  openCoefficient: true
  openHdfsDownload: true
  hiveCompareSupportHashKey: false
third:
  dir: /opt/mc/common/data/storage/third
  resultUrl: http://127.0.0.1:49551/inc/compare/upload
agent:
  dir: /opt/mc/common/data/storage/agent
job:
  finish:
    callBackUrl: http://127.0.0.1:49551/api/v1/df/compare/callback
datasource:
  home: D:\projects\dcas-se\mcdatasource
  version: *******

# 能力市场模块配置
market:
  base_dir: /opt
  json_dir:
  resource_dir: /opt/market/resource/
  install_dir: /opt/market/apps
ssh:
  strictHostKeyChecking: no
  timeout: 30000
  ip: 127.0.0.1
  port: 6222
  username: root
  password: MckJ$2023@.Dcas#pub
# docker客户端配置，默认：unix:///var/run/docker.sock或者tcp://127.0.0.1:2375
docker:
  host: tcp://127.0.0.1:2375
  cert:
    path: /dcas/docker/cert/

# 统一身份
mc:
  sso:
    user-center:
      host: {{ mcCenter.sso.host }}
      app-id:
      app-secret:
    app:
      name: {{ mcCenter.app.name }}
      version: {{ mcCenter.app.version }}
      type: {{mcCenter.app.type}}
      index-url: {{ mcCenter.app.indexUrl }}
    auto-activation-scheme: start
    timout: 1000
    enabled: true

ehcache:
  directory: ./cache
  heap-size-mb: 16
  disk-size-gb: 4

ansible_os_family: RedHat