<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcas.common.mapper.ModelConfigMapper">

    <select id="list" resultType="com.dcas.common.model.vo.LibraryTemplateVO">
        SELECT
        id,
        "name",
        remark as introduce,
        tag as tags,
        update_time,
        create_time,
        create_by,
        update_by,
        "enable",
        is_default as understand,
        region_code,
        industry_code
        FROM model_config where enable = true order by id

    </select>
    <select id="getByOperationId" resultType="java.lang.String">
        select name from model_config mc, co_operation_model com where mc.id = com.model_id and com.operation_id = #{operationId}
    </select>
</mapper>
