<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.DbSqlConfigMapper">
    <select id="queryByType" resultType="com.dcas.common.model.dto.DbSqlConfigDTO">
        SELECT t1.*, t2.content as termName
        FROM
        db_sql_config t1 inner join security_term t2 on t1.term_id = t2.id
        WHERE
        t1.type = #{type}
    </select>
</mapper>