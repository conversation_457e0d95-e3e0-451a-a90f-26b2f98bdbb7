<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.DynamicProcessTreeMapper">
    <insert id="copyDynamicProcessTreeByOperationId" parameterType="java.lang.String">
        INSERT INTO dynamic_process_tree (operation_id, tree_id, parent_id, ancestors, tree_name, order_num, src_tree_id)
        SELECT
            #{newId}, tree_id, parent_id, ancestors, tree_name, order_num, src_tree_id
        FROM
            dynamic_process_tree
        WHERE
            operation_id = #{oldId}
    </insert>
    <select id="selectSrcTreeId" resultType="java.lang.Long">
        select src_tree_id from dynamic_process_tree where operation_id = #{operationId} and tree_id = #{treeId}
    </select>

    <select id="selectByOperationId" resultType="com.dcas.common.model.dto.TreeLabelDTO">
        select tree_id, tree_name, parent_id, src_tree_id srcTreeId from dynamic_process_tree where operation_id = #{operationId}
        and tree_id in
        <foreach collection="labelIds" item="labelId" open="(" separator="," close=")">
            #{labelId}
        </foreach>
        order by parent_id, order_num
    </select>
    <select id="selectTreeIdBySrc" resultType="java.lang.Long">
        select tree_id from dynamic_process_tree where operation_id = #{operationId} and src_tree_id = #{srcTreeId}
    </select>
    <select id="selectByOperationIdAndTreeId" resultType="com.dcas.common.model.dto.TreeLabelDTO">
        select tree_id, tree_name, parent_id, src_tree_id srcTreeId
        from dynamic_process_tree
        where operation_id = #{operationId}
          and src_tree_id = #{srcTreeId}
    </select>
    <select id="querySelectedBusSystemByOperationIdAndTreeId"
            resultType="com.dcas.common.model.dto.TreeLabelDTO">
        select tree_id, tree_name, parent_id, src_tree_id srcTreeId
        from dynamic_process_tree
        where operation_id = #{operationId}
          and src_tree_id = #{srcTreeId}
          and tree_name in (select cta.bus_system from co_threat_analysis cta where cta.operation_id = dynamic_process_tree.operation_id)
        union all
        select tree_id, tree_name, parent_id, src_tree_id srcTreeId
        from dynamic_process_tree
        where operation_id = #{operationId}
        and src_tree_id = #{srcTreeId}
        and tree_name in (select cta.bus_system from co_impact_analysis cta where cta.operation_id = dynamic_process_tree.operation_id)
    </select>
    <select id="selectTreeName" resultType="java.lang.String">
        select tree_name from dynamic_process_tree where operation_id = #{operationId} and tree_id = #{treeId}
    </select>
    <select id="queryBusSystemNameByOperationIds" resultType="java.lang.String">
        select tree_name from dynamic_process_tree where operation_id in
        <foreach collection="operationIds" item="id"  open="(" close=")" separator=",">
            #{id}
        </foreach>
        and src_tree_id = #{code}
    </select>
    <select id="queryBusSystemMapByOperationIds" resultType="java.util.Map">
        select operation_id as operationId, tree_id as systemId,tree_name as systemName from dynamic_process_tree where operation_id in
        <foreach collection="operationIds" item="id"  open="(" close=")" separator=",">
            #{id}
        </foreach>
        and src_tree_id = 107
    </select>
    <select id="selectKeyValueByOperationId" resultType="cn.hutool.core.lang.Pair">
        select tree_id as key, tree_name as value
        from dynamic_process_tree
        where operation_id = #{operationId}
        and src_tree_id = #{srcTreeId}
    </select>
</mapper>