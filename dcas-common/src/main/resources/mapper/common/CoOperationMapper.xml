<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcas.common.mapper.CoOperationMapper">

    <sql id="All_Column_List">
        a.operation_id as operationId,
        a.operation_name as operationName,
        a.create_time as createTime,
        a.update_time as updateTime,
        a.related_district as relatedDistrict,
        a.related_industry as relatedIndustry,
        a.related_system as relatedSystem,
        a.related_overseas as relatedOverseas,
        a.service_content as serviceContent,
        a.authorise as authorise,
        a.progress as progress,
        a.executor,
        a.reviewer,
        a.progress,
        a.status,
        a.user_id as userId,
        a.create_by as createBy,
        a.executor_account as executorAccount,
        a.reviewer_account as reviewerAccount,
        a.project_id as projectId,
        a.begin_date as beiginDate,
        a.end_date as endDate,
        a.duration as duration,
        a.project_manager as projectManager
    </sql>

    <!--查询指定条件数据-->
    <select id="queryOperationList" parameterType="com.dcas.common.model.dto.QueryOperationDTO" resultType="com.dcas.common.model.vo.QueryOperationVo">
        select
            a.operation_id as operationId,
            a.operation_name as operationName,
            a.create_time as createTime,
            a.update_time as updateTime,
            b.project_manager as projectManager,
            b.customer_id as customerId,
            a.executor,
            a.reviewer,
            a.progress,
            a.status,
            a.user_id as userId,
            a.create_by as createBy,
            a.executor_account as executorAccount,
            a.reviewer_account as reviewerAccount,
            a.project_id as projectId,
            a.begin_date as beiginDate,
            a.end_date as endDate,
            a.duration as duration,
            a.project_manager as projectManager,
            a.version,
            a.service_content as serviceContent,
            a.is_spec,
            a.spec_id,
            a.inapplicable
        from co_operation a join co_project b on a.project_id = b.project_id
        <where>
            a.del_flag = '0'
            <if test="operationName != null and operationName != ''">
                and a.operation_name ilike concat('%', #{operationName}, '%')
            </if>
            <if test="createBy != null and createBy != ''">
                and a.create_by ilike concat('%', #{createBy}, '%')
            </if>
            <if test="executor != null and executor != ''">
                and a.executor ilike concat('%', #{executor}, '%')
            </if>
            <if test="reviewer != null and reviewer != ''">
                and a.reviewer ilike concat('%', #{reviewer}, '%')
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by a.create_time DESC
    </select>


    <select id="queryArticle" resultType="com.dcas.common.model.vo.Article">

        select explain, point
        from co_article b
        where b.article_no = ANY (
            STRING_TO_ARRAY(
                    (select article_ids from co_question where question_id = #{questionId}), ','))

    </select>


    <select id="queryOperationView" parameterType="com.dcas.common.model.dto.QueryOperationViewDto" resultType="com.dcas.common.model.vo.QueryOperationView">
        select
        a.operation_id as operationId,
        b.project_id as projectId,
        a.operation_name as operationName,
        b.project_name as projectName,
        a.create_time as createTime,
        a.update_time as updateTime,
        b.project_manager as projectManager,
        a.progress,
        a.status
        from co_operation a join co_project b on a.project_id = b.project_id
        <where>
            a.del_flag = '0'
            <if test="customerId != null and customerId != ''">
                and b.customer_id = #{customerId}
            </if>
            <if test="searchType == 1 ">
                and a.operation_name like concat('%', #{searchContent}, '%')
            </if>
            <if test="searchType == 2 ">
                and b.project_name like concat('%', #{searchContent}, '%')
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by a.operation_id asc
    </select>

    <select id="queryOperationReviewing" parameterType="com.dcas.common.model.dto.QueryOperationOnGoingViewDto" resultType="com.dcas.common.model.vo.QueryOperationOnGoingView">
        select
        a.operation_id as operationId,
        b.project_id as projectId,
        a.operation_name as operationName,
        b.project_name as projectName,
        a.executor,
        a.executor_account,
        a.create_time as createTime,
        b.project_manager as projectManager,
        a.progress,
        a.status
        from co_operation a join co_project b on a.project_id = b.project_id
        <where>
            a.del_flag = '0' and a.status = 1 and progress = 100
            <if test="searchType == 1 ">
                and a.operation_name = #{searchContent}
            </if>
            <if test="searchType == 2 ">
                and b.project_name = #{searchContent}
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by a.operation_id
    </select>


    <!-- 批量删除作业记录 -->
    <update id="updateOperationByIds">
        update co_operation set del_flag='2' where operation_id in
        <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
            (#{id})
        </foreach>
    </update>

    <!-- 根据项目id批量删除作业记录 -->
    <update id="updateOperationByProjectIds">
        update co_operation set del_flag='2' where project_id in
        <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
            (#{id})
        </foreach>
    </update>

    <select id="queryOperationExport" resultType="com.dcas.common.model.vo.QueryProjectOperationExportVo">
        SELECT
        a.project_id as projectId,
        a.customer_id AS customerId,
        a.customer_name AS customerName,
        a.project_name AS projectName,
        a.customer_director as customerDirector,
        a.project_manager as projectManager,
        b.update_time as completedDate,
        b.operation_id as operationId,
        b.operation_name as operationName,
        b.related_system as relatedSystem,
        b.create_time as createTime,
        b.executor_account as executorAccount,
        b.reviewer_account as reviewerAccount,
        b.service_content as serviceContent,
        b.begin_date as startDate,
        b.end_date as endDate,
        b.duration as duration,
        b.create_by as createBy,
        su.phonenumber as phone,
        b.spec_id as specId,
        b.related_industry as relatedIndustry
        FROM
        co_project a JOIN co_operation b ON a.project_id = b.project_id left join sys_user su on b.user_id = su.user_id
        <where>
            a.del_flag = '0'
            <if test="operationId !=null and operationId !='' ">
                and b.operation_id = #{operationId}
            </if>
        </where>
    </select>

    <!-- 结果映射 -->
    <resultMap id="OperationContentVO" type="com.dcas.common.model.vo.OperationContentVO">
        <result property="scheme" column="scheme" typeHandler="com.dcas.common.model.other.StringListTypeHandler"/>
    </resultMap>

    <select id="operationDetail" resultMap="OperationContentVO">
        select operation_name   operationName,
               related_industry industry,
               related_district region,
               overseas_area    overseas,
               service_content  serviceContent,
               model_type modelType,
               co.begin_date,
               co.end_date endDate,
               co.duration duration,
               cp.project_manager projectManager,
               co.project_id projectId,
               co.ability_module,
               co.scheme,
               executor executor,
               executor_account executorAccount,
               reviewer reviewer,
               reviewer_account reviewerAccount,
               t.name           templateName,
               related_system relatedSystem,
               standard_service standardService,
               authorise,
               co.remark,
               co.status,
               co.customer_id customerId,
               co.customer_name customerName,
               co.del_flag delFlag,
               co.user_id userId,
               co.dept_id deptId,
               co.template_id templateId,
               co.asset_type,
               project_name,
               co.update_time,
               co.is_spec,
               co.spec_id,
               co.inapplicable,
               co.create_by
        from co_operation co
                 left join template t on co.template_id = t.id
                 inner join co_project cp on co.project_id = cp.project_id
        where operation_id = #{operationId}
    </select>

    <!--查询指定条件数据-->
    <select id="queryOperationById" resultType="com.dcas.common.model.vo.OperationCopyVO">
        select
        <include refid="All_Column_List"/>
        from co_operation a
        <where>
            a.del_flag = '0'
            and a.operation_id = #{operationId}
        </where>
    </select>
    <select id="selectCustomIdByOperationId" resultType="java.lang.String">
        select cp.customer_id
        from co_operation co
                 inner join co_project cp on co.project_id = cp.project_id
        where co.operation_id = #{operationId} and co.del_flag = '0'
        union all
        select cp.customer_id
        from security_operation so
                 inner join co_project cp on so.project_id = cp.project_id
        where so.id = #{operationId}::bigint
    </select>
    <select id="selectLibraryVersion" resultType="cn.hutool.core.lang.Pair">
        select version as key, operation_id as value from co_operation where del_flag = '0' and version is not null
        union all
        select version as key, id::varchar as value from security_operation where version is not null
    </select>
    <select id="selectOperationList" resultType="com.dcas.common.domain.entity.CoOperation">
        select
        <include refid="All_Column_List"/>
        from co_operation a
        <where>
            a.del_flag = '0'
        </where>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>
    <select id="queryOperationIdsByProjectIds" resultType="java.lang.String">
        select * from co_operation co where co.status=3 and del_flag = '0' and co.project_id in
        <foreach collection="projectIds" item="id"  open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="countModelFileSelected" resultType="com.dcas.common.model.dto.PieDataDTO">
        select com.model_id , mc."name" as name ,count(1) as value
        from co_operation_model com inner join model_config mc on mc.id  = com.model_id  where com.operation_id  in
        <foreach collection="operationIds" item="id"  open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by com.model_id ,mc."name"
    </select>
    <select id="selectOperationServiceContent" resultType="java.lang.String">
        select service_content
        from
        co_operation co,string_to_array(related_system, ',') AS elements
        where
        co.status = 3
        and #{busSystem} = ANY(elements)
    </select>
    <select id="labelIsExist" resultType="java.lang.Integer">
        SELECT count(1)
        FROM co_operation
        WHERE operation_id = #{operationId} AND service_content ~ CONCAT('(^|,)', #{labelId}, '(,|$)')
    </select>
</mapper>
