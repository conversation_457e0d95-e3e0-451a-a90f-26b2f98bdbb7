<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.QuestionnaireMapper">
    <insert id="copyQuestionnaireByOperationId" parameterType="java.lang.String">
            INSERT INTO questionnaire (operation_id, object_id, question_id, question_title, question_desc, followed,
                                       match_tags, finished, inapplicable, inapplicable_reason, sort)
            SELECT
                #{newId},object_id, question_id, question_title, question_desc, followed,
                match_tags, finished, inapplicable, inapplicable_reason, sort
            FROM
                questionnaire
            WHERE
                operation_id = #{oldId}
            <if test="list.size > 0">
                AND object_id IN
                <foreach collection="list" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
    </insert>
    <select id="selectQuestionnaireSort" resultType="com.dcas.common.domain.entity.Questionnaire">
        SELECT q.*
        FROM
            <if test="preview == null">
                questionnaire q
            </if>
            <if test="preview != null">
                questionnaire_import q
            </if>
        WHERE q.operation_id = #{operationId}
          AND q.object_id = #{labelId}
    </select>
    <select id="selectItemAbilityWithLifecycleTag" resultType="com.dcas.common.model.dto.ItemAbilityDTO">
        select
            distinct q.object_id ,t.ability, q2.lifecycle_ids
        from
            item t
        left join questionnaire_content qc on qc.item_id = t.id
        inner join questionnaire q on qc.question_id = q.question_id
        inner join question q2 on q2.id = q.question_id
        where
        q.operation_id = #{operationId}
        and q2.lifecycle_ids is not null
        and t.ability is not null
    </select>
</mapper>