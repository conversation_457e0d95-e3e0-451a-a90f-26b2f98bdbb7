<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.PreSourceConfigMapper">
    <select id="listByPermission" resultType="com.dcas.common.domain.entity.PreSourceConfig">
        select * from pre_source_config where operation_id = #{operationId}
            <if test="configName != null and configName != ''">
                and config_name like concat('%', #{configName}, '%')
            </if>
            <if test="configScan != null and configScan > 0">
                and config_scan = #{configScan}
            </if>
            <if test="description != null and description != ''">
                and (host like concat('%', #{description}, '%') or port like concat('%', #{description}, '%') or username like concat('%', #{description}, '%') or db_name like concat('%', #{description}, '%'))
            </if>
        order by id
    </select>
    <select id="querySourceSchema" resultType="java.lang.String">
        select t1.schemas
        from pre_source_config t1
        where t1.operation_id = #{operationId} and current_step > 1
        <if test="preSourceConfig.host != null and preSourceConfig.host != ''">
            and t1.host = #{preSourceConfig.host}
        </if>
        <if test="preSourceConfig.port != null and preSourceConfig.port != ''">
            and t1.port = #{preSourceConfig.port}
        </if>
        <if test="preSourceConfig.configType != null and preSourceConfig.configType != ''">
            and t1.config_type = #{preSourceConfig.configType}
        </if>
    </select>
    <select id="querySourceCount" resultType="java.lang.Integer">
        select count(1)
        from pre_source_config t1
        where t1.operation_id = #{operationId} and current_step > 1
        <if test="preSourceConfig.host != null and preSourceConfig.host != ''">
            and t1.host = #{preSourceConfig.host}
        </if>
        <if test="preSourceConfig.port != null and preSourceConfig.port != ''">
            and t1.port = #{preSourceConfig.port}
        </if>
        <if test="preSourceConfig.configType != null and preSourceConfig.configType != ''">
            and t1.config_type = #{preSourceConfig.configType}
        </if>
    </select>
    <select id="getBusSystemByOperationId" resultType="java.lang.String">
        select distinct bus_system from pre_source_config where operation_id = #{operationId}
    </select>
</mapper>