<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.SysNotifyMapper">
    <insert id="insertList">
        insert into sys_notify (operation_id, data_id, user_id, user_account, form, type, content, read, date)
        values
            <foreach collection="list" separator="," item="item">
                (#{item.operationId}, #{item.dataId}, #{item.userId}, #{item.userAccount}, #{item.form}, #{item.type}, #{item.content}, #{item.read}, #{item.date})
            </foreach>
    </insert>
    <select id="listAll" resultType="com.dcas.common.model.dto.NotifyDTO">
        SELECT
            "id",
            operation_id,
            u.nick_name userName,
            form,
            "type",
            "content",
            "date" dateTime
        FROM
            "sys_notify" n INNER JOIN "sys_user" u ON n.user_id = u.user_id
    </select>

    <select id="listUserMessages" resultType="com.dcas.common.model.dto.NotifyDTO">
        SELECT
            "id",
            operation_id,
            u.nick_name userName,
            form,
            "type",
            "content",
            "date" dateTime,
            n.data_id
        FROM
            "sys_notify" n INNER JOIN "sys_user" u ON n.user_id = u.user_id
        WHERE
            u.user_name = #{account}
        <if test="type != null">
            AND n.type = #{type}
        </if>
        <if test="form != null and form != ''">
            AND n.form like  concat('%', #{form}, '%')
        </if>
        <if test="startDate != null"><!-- 开始时间检索 -->
            AND <![CDATA[ n.date >= #{startDate,jdbcType=DATE}  ]]>
        </if>
        <if test="endDate != null"><!-- 结束时间检索 -->
            AND <![CDATA[ n.date <= #{endDate,jdbcType=DATE}  ]]>
        </if>
        ORDER BY "date" DESC
    </select>
    <select id="unreadMsg" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            "sys_notify"
        WHERE
            user_account = #{account} AND read = 0;
    </select>
</mapper>