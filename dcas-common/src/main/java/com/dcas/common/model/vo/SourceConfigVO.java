package com.dcas.common.model.vo;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/9/18 10:13
 * @since 1.5.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SourceConfigVO {
    @ApiModelProperty("主键id")
    private Integer id;
    @ApiModelProperty("作业ID")
    private String operationId;
    @ApiModelProperty("数据库类型")
    private String dbType;
    @ApiModelProperty("主机")
    private String host;
    @ApiModelProperty("端口")
    private String port;
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("数据库名称")
    private String dbName;
    @ApiModelProperty("数据源名称")
    private String configName;
    @ApiModelProperty("数据源附加信息")
    private Map<String, Object> attachment;
    @ApiModelProperty("连接状态：0-失败，1-成功")
    private Integer status;

    @Getter
    @Setter
    @ToString
    public static class TaskResult{
        @ApiModelProperty("任务ID")
        private Long taskId;

        @ApiModelProperty("评估模块")
        private String module;

        @ApiModelProperty("状态：0-等待中，1-运行中，2-完成，3-异常停止， 4-手工停止")
        private String status;

        @ApiModelProperty("错误信息")
        private String errMsg;
    }
}
