package com.dcas.common.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/6/9 14:02
 * @since 1.3.0
 */
@Data
public class LegalReportExcel {

    @ApiModelProperty(value = "文件名称")
    @ExcelProperty(value = "文件名称", index = 0)
    private String lawName;

    /**
     * 条文编号
     */
    @ApiModelProperty(value = "条文编号")
    @ExcelProperty(value = "条文编号", index = 1)
    private Integer itemNum;

    /**
     * 条文内容
     */
    @ApiModelProperty(value = "条文要求")
    @ExcelProperty(value = "条文内容", index = 2)
    private String itemContent;

    /**
     * 条文解释
     */
    @ApiModelProperty(value = "条文解释")
    @ExcelProperty(value = "条文解释", index = 3)
    private String itemExplain;

    /**
     * 现状描述
     */
    @ApiModelProperty(value = "现状描述")
    @ExcelProperty(value = "现状描述", index = 4)
    private String remark;

    /**
     * 符合性分析
     */
    @ApiModelProperty(value = "符合性分析结果")
    @ExcelProperty(value = "符合性分析结果", index = 5)
    private String result;
}
