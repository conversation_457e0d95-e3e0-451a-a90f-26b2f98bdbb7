package com.dcas.common.model.req;

import com.dcas.common.core.param.DataSourceParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/4/29 15:18
 * @since 1.6.6
 */
@Data
public class SourceConfigUpdateReq {

    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "主键不能为空")
    private Integer id;

    @ApiModelProperty(value = "数据源扫描范围 1-数据库；2-主机; 3-数据库&主机")
    @NotNull(message = "数据源扫描范围不能为空")
    private Integer configScan;

    @NotBlank(message = "数据源配置名称不能为空")
    @ApiModelProperty(value = "数据源配置名称", required = true)
    private String configName;

    @ApiModelProperty(value = "数据源配置类型", required = true)
    private Integer configType;

    @ApiModelProperty(value = "用户账号列表，英文逗号分隔")
    @NotBlank(message = "用户账号不能为空")
    private String userAccount;

    @NotNull(message = "数据源配置参数不能为空")
    @ApiModelProperty(value = "数据源配置参数", required = true)
    private DataSourceParam dbConfig;
}
