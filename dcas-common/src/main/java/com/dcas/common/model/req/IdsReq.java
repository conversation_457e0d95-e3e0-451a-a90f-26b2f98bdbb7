package com.dcas.common.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/3 15:09
 * @since 1.2.0
 */
@Data
public class IdsReq {

    @Size(min = 1, message = "整型主键列表不能为空")
    @ApiModelProperty(value = "整型主键列表")
    private List<Integer> ids;

    @ApiModelProperty(value = "类型：1-业务系统ID， 2-数源ID列表")
    private int type;

}
