package com.dcas.common.model.vo;

import com.dcas.common.model.dto.BusSystemTagDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/5/17 10:49
 * @since 1.0.0
 */
@Data
public class OperationContentVO {

    @ApiModelProperty("作业名称")
    private String operationName;

    @ApiModelProperty("涉及行业")
    private String industry;

    @ApiModelProperty("设计地域")
    private String region;

    @ApiModelProperty("涉及境外业务")
    private String overseas;

    @ApiModelProperty("评估内容")
    private String serviceContent;

    @ApiModelProperty("评估内容,前端显示")
    private String serviceContentName;

    @ApiModelProperty("能力模块内容")
    private String abilityModule;

    @ApiModelProperty("作业模式 1-简单模式 2-专业模式")
    private Integer modelType;

    @ApiModelProperty("资产类型 1-定量资产 2-定性资产")
    private Integer assetType;

    @ApiModelProperty("是否部署漏扫应用")
    private Boolean scanEnable;

    @ApiModelProperty("调研模板ID")
    private String templateId;
    @ApiModelProperty("调研模板名称")
    private String templateName;

    @ApiModelProperty("关联项目Id")
    private String projectId;

    @ApiModelProperty("关联项目名称")
    private String projectName;

    /**
     * 项目开工日期
     */
    @ApiModelProperty(value = "开工日期")
    private Date beginDate;

    /**
     * 项目完成日期
     */
    @ApiModelProperty(value = "完成日期")
    private Date endDate;

    @ApiModelProperty(value = "最后修改日期")
    private Date updateTime;

    /**
     * 工期 例：4
     */
    @ApiModelProperty(value = "工期（计划完成日期—开工日期）")
    private String duration;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    private String executor;

    /**
     * 执行人账号
     */
    @ApiModelProperty(value = "执行人账号")
    private String executorAccount;


    /**
     * 复核人
     */
    @ApiModelProperty(value = "复核人")
    private String reviewer;

    /**
     * 复核人账号
     */
    @ApiModelProperty(value = "复核人账号")
    private String reviewerAccount;

    @ApiModelProperty(value = "业务系统")
    private String relatedSystem;

    @ApiModelProperty(value = "是否标准服务")
    private Boolean standardService;

    @ApiModelProperty(value = "是否授权")
    private Boolean authorise;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "客户ID")
    private String customerId;
    @ApiModelProperty(value = "客户名")
    private String customerName;
    @ApiModelProperty(value = "删除标志")
    private String delFlag;
    @ApiModelProperty(value = "用户ID")
    private String userId;
    @ApiModelProperty(value = "部门ID")
    private String deptId;
    @ApiModelProperty(value = "是否专项评估")
    private Boolean isSpec;

    /**
     * 专项评估内容
     */
    @ApiModelProperty(value = "专项评估内容ID")
    private Long specId;
    /**
     * 专项评估内容
     */
    @ApiModelProperty(value = "专项评估内容")
    private String specContent;

    @ApiModelProperty(value = "是否开启个人信息不适用")
    private Boolean inapplicable;

    @ApiModelProperty(value = "创建人员")
    private String createBy;

    @ApiModelProperty(value = "业务系统标识列表")
    private List<BusSystemTagDTO> busSystemTags;

    @ApiModelProperty(value = "评估方案")
    private String[] scheme;
}
