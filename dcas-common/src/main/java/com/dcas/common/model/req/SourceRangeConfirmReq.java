package com.dcas.common.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/4/25 11:53
 * @since 1.6.6
 */
@Data
public class SourceRangeConfirmReq {

    @NotNull(message = "数据源id不能为空")
    @ApiModelProperty(value = "数据源id", required = true)
    private Integer sourceId;

    @NotBlank(message = "作业id不能为空")
    @ApiModelProperty(value = "作业id", required = true)
    private String operationId;
}
