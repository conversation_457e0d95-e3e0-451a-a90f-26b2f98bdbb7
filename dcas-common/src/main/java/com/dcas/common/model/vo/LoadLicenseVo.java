package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;

/**
 * 加载license返参
 *
 * <AUTHOR>
 * @Date 2022/11/21 10:53
 * @ClassName LoadLicenseVo
 */
@ApiModel(value = "加载license返参")
@Data
public class LoadLicenseVo implements Serializable {
    /**
     * 激活状态(true表示激活； false表示未激活)
     */
    @ApiModelProperty("激活状态(true表示激活； false表示未激活)")
    private Boolean active;
    /**
     * 激活返回码
     * 0 表示激活成功；1 表示没有license；2 表示机器码错误；
     * 3 表示license文件校验失败；4 表示license授权到期；5 表示其它异常；
     */
    @ApiModelProperty("激活返回码（0 表示激活成功；1 表示没有license；2 表示机器码错误；3 表示license文件校验失败；4 表示license授权到期；5 表示其它异常；）")
    private Byte activeCode;
    /**
     * 激活返回信息
     */
    @ApiModelProperty("激活返回信息")
    private String activeMsg;
    /**
     * 机器码
     * 激活成功或失败都会返回该字段
     */
    @ApiModelProperty("机器码")
    private String deviceId;
    /**
     * 激活方式
     * 1:代表手动激活2:代表自动激活
     */
    @ApiModelProperty("激活方式（1:代表手动激活2:代表自动激活）")
    private String activeMethod;

    /**
     * license唯一识别码
     * 激活成功时才显示该字段；产品在成功激活获得此字段后，之后再查询激活状态，需传入此字段；自动激活时，在license过期后，会自动重新申请license,该标识码会变更，产品需要更新成新的标识码；
     */
    @ApiModelProperty("license唯一识别码（1:代表手动激活2:代表自动激活）")
    private String lncSerialId;

    /**
     * license版本
     */
    @ApiModelProperty("license版本")
    private String lncVersion;

    /**
     * license类型（商用型&POC型&维保型&功能增强型&渠道型）
     */
    @ApiModelProperty("license类型（商用型&POC型&维保型&功能增强型&渠道型）")
    private String lncType;

    private String lncDesc;

    /**
     * 授权码
     */
    @ApiModelProperty("授权码")
    private String authorId;

    /**
     * 认证码
     */
    @ApiModelProperty("认证码")
    private String cloudSignData;
    /**
     * 扩展字段
     */
    @ApiModelProperty("扩展字段")
    private String externData;

    /**
     * 授权剩余时间 单位(天）
     */
    @ApiModelProperty("授权剩余时间 单位(天）")
    private Integer authorTimeExcess;
    /**
     * 授权激活起始时间 时间戳(s)
     */
    @ApiModelProperty("授权激活起始时间 时间戳(s)")
    private Long authorTimeStart;
    /**
     * 授权激活截止时间 时间戳(s)
     */
    @ApiModelProperty("授权激活截止时间 时间戳(s)")
    private Long authorTimeEnd;
    /**
     * 维保剩余时间 单位(天）
     */
    @ApiModelProperty("维保剩余时间 单位(天）")
    private Integer maintainTimeExcess;
    /**
     * 维保截止时间 时间戳(s)
     */
    @ApiModelProperty("维保截止时间 时间戳(s)")
    private Long maintenanceTimeEnd;

    /**
     * 合同编号
     */
    @ApiModelProperty("合同编号")
    private String contractId;
    /**
     * 产品线ID
     */
    @ApiModelProperty("产品线ID")
    private String productLineId;
    /**
     * 产品型号ID
     */
    @ApiModelProperty("产品型号ID")
    private String productId;
    /**
     * 产品型号识别码
     */
    @ApiModelProperty("产品型号识别码")
    private String productIdCode;
    /**
     * 产品特性
     */
    @ApiModelProperty("产品特性")
    private String productExtern;
    /**
     * 产品规格
     */
    @ApiModelProperty("产品规格")
    private String productSpecs;

    /**
     * 知识库授权截至日期
     */
    @ApiModelProperty("知识库授权截至日期")
    private Long libraryGrantTimeEnd;

    /**
     * 系统升级授权截至日期
     */
    @ApiModelProperty("系统升级授权截至日期")
    private Long upgradeGrantTimeEnd;

    @ApiModelProperty(value = "是否锁定")
    private Boolean locked;

    @ApiModelProperty(value = "是否展示获取主权按钮 true展示 false不展示")
    private Boolean expired;

    @ApiModelProperty(value = "客户名称")
    private String customName;

    @ApiModelProperty(value = "评估作业数量")
    private Integer operationJobNum;

    @ApiModelProperty(value = "检查作业数量")
    private Integer securityJobNum;
}
