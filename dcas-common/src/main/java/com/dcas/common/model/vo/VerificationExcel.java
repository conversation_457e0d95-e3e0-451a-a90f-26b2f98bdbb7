package com.dcas.common.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/11/3 11:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationExcel {

    @ExcelProperty(value = "模板名称", index = 0)
    private String modeName;

    @ExcelProperty(value = "文件名称", index = 1)
    private String fileName;

    @ExcelProperty(value = "编码", index = 2)
    private String bpCode;

    @ExcelProperty(value = "能力项", index = 3)
    private String gpDimension;

    @ExcelProperty(value = "能力要求", index = 4)
    private String standardProvision;

    @ExcelProperty(value = "等级", index = 5)
    private String level;

    @ExcelProperty(value = "现状描述", index = 6)
    private String description;

    @ExcelProperty(value = "具体结果", index = 7)
    private String resultDetail;

    @ExcelProperty(value = "符合性判断结果", index = 8)
    private String result;
}
