package com.dcas.common.domain.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.dcas.common.enums.LabelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/4/26 11:44
 * @since 1.6.6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("pre_source_config")
public class PreSourceConfig{

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String operationId;

    private String configName;

    private Integer configType;

    private Integer configScan;

    private String accessModule;

    private Integer abilityModule;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long systemId;

    private String host;

    private String port;

    private String username;

    private String password;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String dbName;

    private String schemas;

    private Integer currentStep;

    private String userId;

    private Integer configId;

    private String appIds;

    private String userAccount;

    public String getDbConfig(boolean withUserName){
        String template;
        if (dbName == null) {
            if (withUserName) {
                template = "主机:【{}】;端口:【{}】;用户:【{}】";
                return StrUtil.format(template, host, port, username);
            } else {
                template = "主机:【{}】;端口:【{}】";
                return StrUtil.format(template, host, port);
            }
        } else {
            if (withUserName) {
                template = "主机:【{}】;端口:【{}】;用户:【{}】;数据库:【{}】";
                return StrUtil.format(template, host, port, username, dbName);
            } else {
                template = "主机:【{}】;端口:【{}】;数据库:【{}】";
                return StrUtil.format(template, host, port, dbName);
            }
        }
    }
}
