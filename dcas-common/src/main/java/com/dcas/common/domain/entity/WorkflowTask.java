package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 工作流任务实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("workflow_task")
public class WorkflowTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "任务ID")
    private Long id;

    @ApiModelProperty(value = "作业ID")
    private String operationId;

    @ApiModelProperty(value = "任务名称", required = true)
    @NotBlank(message = "任务名称不能为空")
    private String name;

    @ApiModelProperty(value = "对接产品能力")
    private String capability;

    /** 产品ID */
    @ApiModelProperty(value = "产品ID", required = true)
    @NotNull(message = "产品ID不能为空")
    private Long productId;

    /** 产品名称 */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /** 产品logo */
    @ApiModelProperty(value = "产品logo")
    private String logo;

    @ApiModelProperty(value = "所属公司ID")
    private String company;

    /**
     * 任务类型 1-能力验证 2-展示分类分级结果
     */
    private Integer taskType;

    /** 任务配置JSON */
    @ApiModelProperty(value = "任务配置JSON", required = true)
    @NotBlank(message = "任务配置不能为空")
    private String taskConfig;

    /** 当前执行步骤 */
    private Integer currentStep;

    /** 总步骤数 */
    private Integer totalSteps;

    /** 执行进度百分比 */
    private Integer progressPercentage;

    /** 错误信息 */
    private String errorMessage;

    /** 执行结果 */
    private String executionResult;

    /** 验证结果 */
    private String verifyResult;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 执行时长（毫秒） */
    private Long executionDuration;

    /** 任务状态：PENDING-待执行, RUNNING-执行中, COMPLETED-已完成, FAILED-失败, TERMINATED-已终止 */
    @ApiModelProperty(value = "任务状态：0-待执行, 1-执行中, 2-已完成, 3-失败, 4-已终止")
    private Integer status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 创建人*/
    private String createBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 更新人*/
    private String updateBy;

    /** 任务步骤列表 */
    @TableField(exist = false)
    private List<TaskStep> taskSteps;

    /** 任务执行记录列表 */
    @TableField(exist = false)
    private List<TaskExecution> taskExecutions;
}
