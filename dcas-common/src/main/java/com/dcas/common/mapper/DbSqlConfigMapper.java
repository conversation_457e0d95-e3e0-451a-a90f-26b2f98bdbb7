package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.DbSqlConfig;
import com.dcas.common.model.dto.DbSqlConfigDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据库规则表-主要配置热力图、最小权限校验sql Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface DbSqlConfigMapper extends BaseMapper<DbSqlConfig> {

    List<DbSqlConfigDTO> queryByType(@Param("type") Integer type);
}
