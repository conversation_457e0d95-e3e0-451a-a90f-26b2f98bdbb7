package com.dcas.common.mapper;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.core.domain.BaseEntity;
import com.dcas.common.model.dto.PieDataDTO;
import com.dcas.common.model.dto.QueryOperationDTO;
import com.dcas.common.model.dto.QueryOperationOnGoingViewDto;
import com.dcas.common.model.dto.QueryOperationViewDto;
import com.dcas.common.domain.entity.CoOperation;
import com.dcas.common.model.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【operation(评估管理表)】的数据库操作Mapper
 * @createDate 2022-05-24 15:56:31
 * @Entity generator.domain.Operation
 */
@Mapper
public interface CoOperationMapper extends BaseMapper<CoOperation> {

    /**
     * 批量删除作业记录
     *
     * @param idList request
     * @return * @return int
     * @Date 2022/8/12 14:12
     */
    int updateOperationByIds(@Param("idList") List<String> idList);

    /**
     * 根据项目id批量删除作业记录
     *
     * @param idList request
     * @return * @return int
     * @Date 2022/8/12 14:12
     */
    int updateOperationByProjectIds(@Param("idList") List<String> idList);

    /**
     * 查询作业列表
     *
     * @param dto request
     * @return * @return List<RetrieveOperationVo>
     * @Date 2022/8/23 10:57
     */
    List<QueryOperationVo> queryOperationList(QueryOperationDTO dto);


    /**
     * 根据题目id查询法条
     *
     * @param questionId request
     * @return * @return List<QueryQuestionVo>
     * @Date 2022/5/30 10:04
     */
    List<Article> queryArticle(@Param("questionId") String questionId);

    /**
     * 查询作业视图列表
     *
     * @param dto request
     * @return * @return List<RetrieveOperationView>
     * @Date 2022/8/23 10:57
     */
    List<QueryOperationView> queryOperationView(QueryOperationViewDto dto);

    /**
     * 查询待复核作业列表
     */
    List<QueryOperationOnGoingView> queryOperationReviewing(QueryOperationOnGoingViewDto dto);

    /**
     * 作业导出查询
     * @Date 2022/10/24 18:40
     * @param operationId  request
     * @return * @return QueryOperationExportVo
     */
    QueryProjectOperationExportVo queryOperationExport(@Param("operationId") String operationId);

    OperationContentVO operationDetail(@Param("operationId") String operationId);

    /**
     * 通过作业ID查询作业信息
     * @param operationId 作业ID
     * @return {@link OperationCopyVO}
     */
    OperationCopyVO queryOperationById(@Param("operationId") String operationId);

    String selectCustomIdByOperationId(@Param("operationId") String operationId);

    List<Pair<String, String>> selectLibraryVersion();

    List<CoOperation> selectOperationList(BaseEntity entity);

    List<String> queryOperationIdsByProjectIds(@Param("projectIds")List<String> projectIds);

    List<PieDataDTO> countModelFileSelected(@Param("operationIds") List<String> operationIds);

    List<String> selectOperationServiceContent(@Param("busSystem") String busSystem);

    Integer labelIsExist(@Param("operationId") String operationId, @Param("labelId") Long labelId);
}
