package com.dcas.common.mapper;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.model.dto.TreeLabelDTO;
import com.dcas.common.domain.entity.DynamicProcessTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/4 10:38
 * @since 1.4.0
 */
public interface DynamicProcessTreeMapper extends BaseMapper<DynamicProcessTree> {

    /**
     * 根据作业ID和树ID查询树ID
     *
     * @param operationId 作业ID
     * @param treeId      树ID
     * @return Long
     * @date 2023/7/4 10:38
     */
    Long selectSrcTreeId(@Param("operationId") String operationId, @Param("treeId") Long treeId);


    String selectTreeName(@Param("operationId") String operationId, @Param("treeId") Long treeId);

    /**
     * 根据作业ID和树ID查询树ID
     *
     * @param operationId 作业ID
     * @param labelIds      树ID
     * @return Long
     * @date 2023/7/4 10:38
     */
    List<TreeLabelDTO> selectByOperationId(@Param("operationId") String operationId, @Param("labelIds") Set<Long> labelIds);

    List<Long> selectTreeIdBySrc(@Param("operationId") String operationId, @Param("srcTreeId") Long srcTreeId);

    List<TreeLabelDTO> selectByOperationIdAndTreeId(@Param("operationId") String operationId, @Param("srcTreeId") Long srcTreeId);

    /**
     * 通过作业ID复制动态标签表信息
     * @param oldId 旧的作业ID
     * @param newId 新的作业ID
     */
    void copyDynamicProcessTreeByOperationId(@Param("oldId")String oldId, @Param("newId")String newId);

    /**
     * 风险评估计算 获取选中威胁分析的业务系统
     * @param operationId 作业名
     * @param srcTreeId
     * @return
     */
    List<TreeLabelDTO> querySelectedBusSystemByOperationIdAndTreeId(@Param("operationId") String operationId, @Param("srcTreeId") Long srcTreeId);

    /**
     * 根据作业id列表获取业务系统名称
     *
     * @param operationIds
     * @param code
     * @return
     */
    List<String> queryBusSystemNameByOperationIds(@Param("operationIds")List<String> operationIds, @Param("code")Long code);

    List<Map<String, Object>> queryBusSystemMapByOperationIds(@Param("operationIds") List<String> operationIds);

    Pair<Integer, String> selectKeyValueByOperationId(@Param("operationId") String operationId, @Param("srcTreeId") Long srcTreeId);
}
