package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.Questionnaire;
import com.dcas.common.model.dto.ItemAbilityDTO;
import com.dcas.common.model.vo.QuestionnaireSortVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/14 15:09
 * @since 1.2.0
 */
public interface QuestionnaireMapper extends BaseMapper<Questionnaire> {

    List<Questionnaire> selectQuestionnaireSort(@Param("operationId") String operationId,
                                                      @Param("labelId") Long labelId, @Param("preview") Boolean preview);

    void copyQuestionnaireByOperationId(@Param("oldId")String oldId, @Param("newId")String newId, @Param("list") List<Long> objectIds);

    List<ItemAbilityDTO> selectItemAbilityWithLifecycleTag(@Param("operationId") String operationId);
}
