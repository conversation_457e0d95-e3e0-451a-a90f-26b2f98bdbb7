package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.ModelConfig;
import com.dcas.common.model.vo.LibraryTemplateVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 模型配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
public interface ModelConfigMapper extends BaseMapper<ModelConfig> {

    List<LibraryTemplateVO> list();

    String getByOperationId(@Param("operationId") String operationId);
}
