package com.dcas.common.utils;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import com.dcas.common.constant.Constants;
import com.dcas.common.model.dto.SSOAccountDTO;
import com.mchz.starter.sso.model.SecureUser;
import com.mchz.starter.sso.util.SsoUtil;
import lombok.extern.slf4j.Slf4j;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.common.mapper.SysConfigMapper;
import com.dcas.common.utils.spring.SpringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import com.dcas.common.constant.HttpStatus;
import com.dcas.common.core.domain.model.LoginUser;
import com.dcas.common.exception.ServiceException;

import java.util.Objects;

/**
 * 安全服务工具类
 */
@Slf4j
public class SecurityUtils {
    private static final AES AES = SecureUtil.aes(getKey().getBytes(CharsetUtil.CHARSET_UTF_8));
    private static final String SAMPLE_DATA_AES_CONFIG_KEY = "sys.aes.key";
    private static String getKey(){
        SysConfigMapper sysConfigMapper = SpringUtils.getBean(SysConfigMapper.class);
        SysConfig sysConfig =  sysConfigMapper.selectConfigByKey(SAMPLE_DATA_AES_CONFIG_KEY);
        if (sysConfig != null && CharSequenceUtil.isNotEmpty(sysConfig.getConfigValue())){
            return sysConfig.getConfigValue();
        } else {
            return CharSequenceUtil.EMPTY;
        }
    }

    /**
     * 获取用户账户
     **/
    public static String getAccount() {
        String account = null;
        try {
            SecureUser currentUser = SsoUtil.getCurrentUser();
            Objects.requireNonNull(currentUser);
            account = currentUser.getAccount();
            log.debug("当前用户 {}", account);
        } catch (Exception e) {
            log.warn("获取用户失败 {}", e.getMessage());
        }
        if (StrUtil.isEmpty(account)) {
            account = "manager";
        }
        return account;
    }

    /**
     * 获取用户账户ID
     **/
    public static Long getUserId() {
        Long account = null;
        try {
            SecureUser currentUser = SsoUtil.getCurrentUser();
            Objects.requireNonNull(currentUser);
            account = currentUser.getId();
            log.debug("当前用户ID {}", account);
        } catch (Exception e) {
            log.warn("获取用户ID失败 {}", e.getMessage());
        }
        if (Objects.isNull(account)) {
            account = 1L;
        }
        return account;
    }


    /**
     * 获取用户姓名
     **/
    public static String getNickname() {
        String username = null;
        try {
            SecureUser currentUser = SsoUtil.getCurrentUser();
            Objects.requireNonNull(currentUser);
            username = currentUser.getUsername();
        } catch (Exception e) {
            log.warn("获取用户失败 {}", e.getMessage());
        }
        if (StrUtil.isEmpty(username)) {
            username = "管理员";
        }
        return username;
    }

    /**
     * 获取用户
     **/
    public static SSOAccountDTO getLoginUser() {
        return getMcUser();
    }

    public static SSOAccountDTO getMcUser() {
        SecureUser currentUser = SsoUtil.getCurrentUser();
        Objects.requireNonNull(currentUser);
        SSOAccountDTO dto = new SSOAccountDTO();
        dto.setName(currentUser.getUsername());
        dto.setStatus("ON");
        dto.setAccount(currentUser.getAccount());
        return dto;
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }


    /**
     * 判断密码是否相同
     *
     * @param rawPassword     真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * TODO 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    /**
     * 加密
     *
     * @param password 真实密码
     * @return 加密后的密码
     */
    public static String encryptAes(String password) {
        return null == password ? StrUtil.EMPTY : AES.encryptBase64(password);
    }

    /**
     * 解密
     *
     * @param password 加密后的密码
     * @return 真实密码
     */
    public static String decryptAes(String password) {
        return StrUtil.isEmpty(password) ? null : AES.decryptStr(password);
    }
}
