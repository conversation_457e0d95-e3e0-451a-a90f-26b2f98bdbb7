package com.dcas.common.utils.excel;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.util.IOUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.Color;
import java.awt.Font;
import java.awt.font.FontRenderContext;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Excel水印工具类
 * 
 * <AUTHOR> Agent
 * @since 1.0.0
 */
@Slf4j
public class ExcelWatermarkUtil {

    /**
     * 为工作簿添加水印
     *
     * @param workbook 工作簿
     * @param config 水印配置
     */
    public static void addWatermark(Workbook workbook, WatermarkConfig config) {
        if (workbook == null || config == null || config.getText() == null || config.getText().trim().isEmpty()) {
            log.warn("工作簿或水印配置为空，跳过水印添加");
            return;
        }

        try {
            // 生成水印图片
            byte[] watermarkImage = createWatermarkImage(config);
            
            // 为每个工作表添加水印
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                addWatermarkToSheet(workbook, sheet, watermarkImage);
            }
            
            log.info("成功为工作簿添加水印，文本: {}", config.getText());
        } catch (Exception e) {
            log.error("添加水印失败", e);
        }
    }

    /**
     * 为指定工作表添加水印
     *
     * @param workbook 工作簿
     * @param sheet 工作表
     * @param watermarkImage 水印图片字节数组
     */
    private static void addWatermarkToSheet(Workbook workbook, Sheet sheet, byte[] watermarkImage) {
        try {
            // 添加图片到工作簿
            int pictureIdx = workbook.addPicture(watermarkImage, Workbook.PICTURE_TYPE_PNG);

            // 创建绘图对象
            Drawing<?> drawing = sheet.createDrawingPatriarch();

            // 创建锚点（覆盖整个工作表的大范围区域）
            ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();
            anchor.setCol1(0);
            anchor.setRow1(0);
            // 设置大范围覆盖：50列（A到AX）和1000行
            anchor.setCol2(50);   // 覆盖50列，足够大部分使用场景
            anchor.setRow2(1000); // 覆盖1000行，足够大部分使用场景

            // 设置锚点类型为不随单元格移动和调整大小
            anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);

            // 创建图片
            Picture picture = drawing.createPicture(anchor, pictureIdx);

            log.debug("成功为工作表 {} 添加全覆盖水印，覆盖区域: 行0-1000, 列0-50", sheet.getSheetName());

        } catch (Exception e) {
            log.error("为工作表 {} 添加水印失败", sheet.getSheetName(), e);
        }
    }

    /**
     * 创建水印图片
     *
     * @param config 水印配置
     * @return 水印图片字节数组
     * @throws IOException IO异常
     */
    private static byte[] createWatermarkImage(WatermarkConfig config) throws IOException {
        // 计算图片尺寸 - 适配大范围覆盖（50列x1000行）
        // 每列约75像素，每行约20像素
        int imageWidth = 50 * 75;   // 3750像素，覆盖50列
        int imageHeight = 1000 * 20; // 20000像素，覆盖1000行

        // 限制最大尺寸，避免内存问题
        imageWidth = Math.min(imageWidth, 4000);
        imageHeight = Math.min(imageHeight, 6000);
        
        // 创建透明背景的图片
        BufferedImage image = new BufferedImage(imageWidth, imageHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        
        try {
            // 设置透明背景
            g2d.setComposite(AlphaComposite.Clear);
            g2d.fillRect(0, 0, imageWidth, imageHeight);
            
            // 设置渲染质量
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            
            // 设置字体
            int fontStyle = Font.PLAIN;
            if (config.isBold()) fontStyle |= Font.BOLD;
            if (config.isItalic()) fontStyle |= Font.ITALIC;
            
            Font font = new Font(config.getFontName(), fontStyle, config.getFontSize());
            g2d.setFont(font);
            
            // 设置颜色和透明度
            Color color = config.getFontColor();
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, config.getTransparency()));
            g2d.setColor(color);
            
            // 获取文本尺寸
            FontRenderContext frc = g2d.getFontRenderContext();
            Rectangle2D textBounds = font.getStringBounds(config.getText(), frc);
            int textWidth = (int) textBounds.getWidth();
            int textHeight = (int) textBounds.getHeight();
            
            // 计算旋转后的文本位置并绘制多个水印
            AffineTransform originalTransform = g2d.getTransform();
            
            for (int x = config.getOffsetX(); x < imageWidth; x += config.getHorizontalSpacing()) {
                for (int y = config.getOffsetY(); y < imageHeight; y += config.getVerticalSpacing()) {
                    // 保存当前变换
                    g2d.setTransform(originalTransform);
                    
                    // 移动到绘制位置
                    g2d.translate(x, y);
                    
                    // 旋转
                    g2d.rotate(Math.toRadians(config.getRotationAngle()));
                    
                    // 绘制文本
                    g2d.drawString(config.getText(), -textWidth / 2, textHeight / 2);
                }
            }
            
        } finally {
            g2d.dispose();
        }
        
        // 将图片转换为字节数组
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        return baos.toByteArray();
    }

    /**
     * 为工作簿添加简单水印
     *
     * @param workbook 工作簿
     * @param watermarkText 水印文本
     */
    public static void addSimpleWatermark(Workbook workbook, String watermarkText) {
        WatermarkConfig config = WatermarkConfig.defaultConfig(watermarkText);
        addWatermark(workbook, config);
    }

    /**
     * 为工作簿添加自定义水印
     *
     * @param workbook 工作簿
     * @param watermarkText 水印文本
     * @param fontSize 字体大小
     * @param transparency 透明度
     */
    public static void addCustomWatermark(Workbook workbook, String watermarkText, int fontSize, float transparency) {
        WatermarkConfig config = WatermarkConfig.simpleConfig(watermarkText, fontSize, transparency);
        addWatermark(workbook, config);
    }
}
