package com.dcas.common.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁工具类
 * 用于防止并发操作导致的数据重复问题
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisLockUtil {

    private final RedisTemplate<String, String> redisTemplate;

    private static final String LOCK_PREFIX = "lock:";
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "return redis.call('del', KEYS[1]) " +
        "else return 0 end";

    /**
     * 尝试获取分布式锁
     *
     * @param lockKey 锁的key
     * @param expireTime 锁的过期时间（秒）
     * @return 锁的值，获取失败返回null
     */
    public String tryLock(String lockKey, long expireTime) {
        String lockValue = UUID.randomUUID().toString();
        String key = LOCK_PREFIX + lockKey;
        
        Boolean success = redisTemplate.opsForValue()
            .setIfAbsent(key, lockValue, expireTime, TimeUnit.SECONDS);
        
        if (Boolean.TRUE.equals(success)) {
            log.debug("Successfully acquired lock: {}", key);
            return lockValue;
        }
        
        log.debug("Failed to acquire lock: {}", key);
        return null;
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey 锁的key
     * @param lockValue 锁的值
     * @return 是否释放成功
     */
    public boolean releaseLock(String lockKey, String lockValue) {
        String key = LOCK_PREFIX + lockKey;
        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(UNLOCK_SCRIPT);
        script.setResultType(Long.class);
        
        Long result = redisTemplate.execute(script, Collections.singletonList(key), lockValue);
        boolean success = Long.valueOf(1).equals(result);
        
        if (success) {
            log.debug("Successfully released lock: {}", key);
        } else {
            log.warn("Failed to release lock: {}, value: {}", key, lockValue);
        }
        
        return success;
    }

    /**
     * 执行带锁的操作
     *
     * @param lockKey 锁的key
     * @param expireTime 锁的过期时间（秒）
     * @param action 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁失败
     */
    public <T> T executeWithLock(String lockKey, long expireTime, LockAction<T> action) {
        String lockValue = tryLock(lockKey, expireTime);
        if (lockValue == null) {
            throw new RuntimeException("无法获取锁，请稍后重试");
        }
        
        try {
            return action.execute();
        } finally {
            releaseLock(lockKey, lockValue);
        }
    }

    /**
     * 锁操作接口
     */
    @FunctionalInterface
    public interface LockAction<T> {
        T execute();
    }
}
