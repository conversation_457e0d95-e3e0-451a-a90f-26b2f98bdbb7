package com.dcas.common.excel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import lombok.extern.slf4j.Slf4j;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/6/30 18:15
 * @since 1.0.0
 */
@Slf4j
public class WaterMarkHandler implements SheetWriteHandler {

    private final boolean hasLineBreak;

    private final String[] paramArray;

    public WaterMarkHandler(boolean hasLineBreak, String... paramArray) {
        super();
        this.hasLineBreak = hasLineBreak;
        this.paramArray = paramArray;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        try {
            WaterMarkUtil.insertWaterMarkTextToXlsx(writeWorkbookHolder.getWorkbook(),
                    writeSheetHolder.getSheet(), this.hasLineBreak, this.paramArray);
        } catch (Exception e) {
            log.error("添加水印时出错啦！", e);
        }
    }

    public static WaterMarkHandler simple(String watermarkText) {
        return new WaterMarkHandler(false, watermarkText);
    }
}
