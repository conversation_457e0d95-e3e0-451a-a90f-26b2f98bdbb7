package com.dcas.common.excel;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.opc.PackagePartName;
import org.apache.poi.openxml4j.opc.PackageRelationship;
import org.apache.poi.openxml4j.opc.TargetMode;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFPictureData;
import org.apache.poi.xssf.usermodel.XSSFRelation;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/6/30 18:16
 * @since 1.0.0
 */
@Slf4j
public class WaterMarkUtil {

    /**
     * 描述：Excel 导出添加水印
     *
     */
    public static void insertWaterMarkTextToXlsx(Workbook workbook, Sheet sheet,
                                                 boolean hasLineBreak, String... paramArray) throws IOException {
        String split = " ";
        if (hasLineBreak) {
            split = System.lineSeparator();
        }

        String waterMarkText = "";
        StringBuilder waterMarkTextBuilder = new StringBuilder();
        for (int i = 0; i < paramArray.length; i++) {
            String param = paramArray[i];
            if (StrUtil.isNotBlank(param)) {
                if (i == 0) {
                    waterMarkTextBuilder.append(param);
                } else {
                    waterMarkTextBuilder.append(split).append(param);
                }
            }
        }
        waterMarkText = waterMarkTextBuilder.toString();

        if (workbook instanceof SXSSFWorkbook) {
            insertWaterMarkTextToXlsx((SXSSFWorkbook) workbook, (SXSSFSheet) sheet, waterMarkText, hasLineBreak);
        } else if (workbook instanceof XSSFWorkbook) {
            insertWaterMarkTextToXlsx((XSSFWorkbook) workbook, (XSSFSheet) sheet, waterMarkText, hasLineBreak);
        }
    }


    /**
     * 描述：给 SXSSFWorkbook 添加水印
     */
    public static void insertWaterMarkTextToXlsx(SXSSFWorkbook workbook, SXSSFSheet sheet,
                                                 String waterMarkText, boolean hasLineBreak) throws IOException {
        BufferedImage image = createWatermarkImage(waterMarkText, hasLineBreak);
        ByteArrayOutputStream imageOs = new ByteArrayOutputStream();
        ImageIO.write(image, "png", imageOs);
        int pictureIdx = workbook.addPicture(imageOs.toByteArray(), XSSFWorkbook.PICTURE_TYPE_PNG);
        XSSFPictureData pictureData = (XSSFPictureData) workbook.getAllPictures().get(pictureIdx);

        // 这里由于 SXSSFSheet 没有 getCTWorksheet() 方法，通过反射取出 _sh 属性
        XSSFSheet shReflect = (XSSFSheet) ReflectUtil.getFieldValue(sheet, "_sh");
        PackagePartName ppn = pictureData.getPackagePart().getPartName();
        String relType = XSSFRelation.IMAGES.getRelation();
        PackageRelationship pr = shReflect.getPackagePart().addRelationship(ppn, TargetMode.INTERNAL, relType, null);
        shReflect.getCTWorksheet().addNewPicture().setId(pr.getId());
    }

    /**
     * 描述：给 XSSFWorkbook 添加水印
     *
     */
    public static void insertWaterMarkTextToXlsx(XSSFWorkbook workbook, XSSFSheet sheet,
                                                 String waterMarkText, boolean hasLineBreak) throws IOException {
        BufferedImage image = createWatermarkImage(waterMarkText, hasLineBreak);
        ByteArrayOutputStream imageOs = new ByteArrayOutputStream();
        ImageIO.write(image, "png", imageOs);
        int pictureIdx = workbook.addPicture(imageOs.toByteArray(), XSSFWorkbook.PICTURE_TYPE_PNG);
        XSSFPictureData pictureData = workbook.getAllPictures().get(pictureIdx);

        PackagePartName ppn = pictureData.getPackagePart().getPartName();
        String relType = XSSFRelation.IMAGES.getRelation();
        PackageRelationship pr = sheet.getPackagePart().addRelationship(ppn, TargetMode.INTERNAL, relType, null);
        sheet.getCTWorksheet().addNewPicture().setId(pr.getId());
    }

    /**
     * 描述：创建水印图片
     */
    public static BufferedImage createWatermarkImage(String waterMark, boolean hasLineBreak) {
        // 设置宋体字体
        Font font = new Font("宋体", Font.PLAIN, 50);

        // 创建一个模版 Graphics2D 上下文来获取文本的尺寸
        BufferedImage tempImage = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);
        Graphics2D tempGraphics = tempImage.createGraphics();
        tempGraphics.setFont(font);
        FontMetrics tempMetrics = tempGraphics.getFontMetrics();

        int textWidthMax = 0;
        int textHeightSum = 0;
        if (hasLineBreak) {
            // 换行水印，分割并绘制文本
            String[] lines = waterMark.split(System.lineSeparator());
            for (String line : lines) {
                // 计算文本宽度和高度
                int textWidth = tempMetrics.stringWidth(line);
                int textHeight = tempMetrics.getHeight();
                if (textWidth > textWidthMax) {
                    textWidthMax = textWidth;
                }
                textHeightSum += textHeight;
            }
        } else {
            // 计算文本宽度和高度
            textWidthMax = tempMetrics.stringWidth(waterMark);
            textHeightSum = tempMetrics.getHeight();
        }
        // 清除模版 Graphics2D
        tempGraphics.dispose();

        // 因为后面会把文字旋转 45 %，所以上面算出的 textWidthMax 和 textHeightSum 实际上是斜边
        textWidthMax = (int) (textWidthMax / Math.sqrt(2));
        textHeightSum = (int) (textHeightSum / Math.sqrt(2));

        int imageEdgeLength = textWidthMax + textHeightSum;
        imageEdgeLength = Math.max(imageEdgeLength, 200) + 100;

        // 创建一个大小恰好适合文本的图像
        BufferedImage image = new BufferedImage(imageEdgeLength, imageEdgeLength, BufferedImage.TYPE_INT_ARGB);
        // 背景透明 开始
        Graphics2D graphics = image.createGraphics();
        // 设定画笔颜色
        graphics.setColor(new Color(0, 0, 0, 40));
        // 设置字体
        graphics.setStroke(new BasicStroke(1));
        // 设置画笔字体
        graphics.setFont(font);
        // 设置倾斜度
        graphics.rotate(0 - (Math.PI / 4), (double) image.getWidth() / 2, (double) image.getHeight() / 2);

        // 设置字体平滑
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        // 获取FontMetrics用于测量文本宽度
        FontMetrics metrics = graphics.getFontMetrics();

        double baseY = (double) (imageEdgeLength - textHeightSum) / 2;
        if (hasLineBreak) {
            // 换行水印，分割并绘制文本
            String[] lines = waterMark.split(System.lineSeparator());
            for (String line : lines) {
                byte[] bytes = line.getBytes(StandardCharsets.UTF_8);
                line = new String(bytes, StandardCharsets.UTF_8);
                log.info("当前要添加的这行水印是：{}", line);
                // 计算文本宽度
                int textWidth = metrics.stringWidth(line);

                // 计算图像的中心点
                int centerX = imageEdgeLength / 2;

                // 计算文本起始位置（使文本居中）
                int x = centerX - textWidth / 2;

                // 绘制文本
                graphics.drawString(line, x, (int) baseY);

                // 计算下一行的y坐标
                baseY += metrics.getHeight();
            }
        } else {
            /*byte[] bytes = waterMark.getBytes(StandardCharsets.UTF_8);
            waterMark = new String(bytes, StandardCharsets.UTF_8);*/
            log.info("当前要添加的这行水印是：{}", waterMark);
            // 计算文本宽度和高度
            int textWidth = metrics.stringWidth(waterMark);

            // 计算图像的中心点
            int centerX = imageEdgeLength / 2;

            // 计算文本起始位置（使文本居中）
            int x = centerX - textWidth / 2;

            // 绘制文本
            graphics.drawString(waterMark, x, (int) baseY);
        }

        // 释放画笔
        graphics.dispose();

        return image;
    }
}
