package com.dcas.system.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.Mail;
import cn.hutool.extra.mail.MailAccount;
import com.dcas.common.annotation.Notify;
import com.dcas.common.constant.WsConstants;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.entity.SysUser;
import com.dcas.common.enums.NotifyFormEnum;
import com.dcas.common.enums.NotifyTypeEnum;
import com.dcas.common.utils.PartitionUtils;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.model.dto.CommonDto;
import com.dcas.common.domain.entity.CoOperation;
import com.dcas.common.domain.entity.CoProject;
import com.dcas.common.domain.entity.SysNotify;
import com.dcas.common.model.vo.SystemConfigVO;
import com.dcas.system.handler.INotifyHandler;
import com.dcas.common.mapper.CoOperationMapper;
import com.dcas.system.service.CoProjectService;
import com.dcas.system.service.ISysNotifyService;
import com.dcas.system.service.ISysPageConfigService;
import com.dcas.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/1/29 13:47
 * @since 1.0.1
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NotifyHandler implements INotifyHandler {
    private final CoOperationMapper coOperationMapper;

    private final CoProjectService coProjectService;
    private final SimpMessagingTemplate wsTemplate;
    private final ISysUserService userService;
    private final ISysNotifyService sysNotifyService;
    private final ISysPageConfigService sysPageConfigService;

    private static final String CONTENT = "ID为【%s】、项目经理为【%s】的作业当前可进行【%s】";

    @Override
    public void after(Object[] args, Notify annotation) {
        String operationId = null;
        Set<String> account = null;
        NotifyTypeEnum type = annotation.type();
        SystemConfigVO pageConfig = sysPageConfigService.details();
        if (StrUtil.isEmpty(pageConfig.getNotifyContent())) {
            return;
        }
        RequestModel requestModel = Convert.convert(RequestModel.class, args[0]);
        if (Objects.requireNonNull(type) == NotifyTypeEnum.DISTRIBUTION) {
            CoOperation coOperation = Convert.convert(CoOperation.class, requestModel.getPrivator());
            operationId = coOperation.getOperationId();
            account = StrUtil.isEmpty(coOperation.getExecutor()) ? null : new HashSet<>(StrUtil.split(coOperation.getExecutor(), StrUtil.COMMA));
        } else {
            CommonDto commonDto = Convert.convert(CommonDto.class, requestModel.getPrivator());
            operationId = commonDto.getOperationId();
            CoOperation coOperation = coOperationMapper.selectById(operationId);
            if (coOperation.getProgress().compareTo(new BigDecimal(100)) != 0)
                return;
            type = NotifyTypeEnum.of(coOperation.getStatus() + 1);
            if (type == NotifyTypeEnum.REVIEW) {
                account = StrUtil.isEmpty(coOperation.getReviewer()) ? null : new HashSet<>(StrUtil.split(coOperation.getReviewer(), StrUtil.COMMA));
            } else {
                account = new HashSet<>();
                if (StrUtil.isNotEmpty(coOperation.getExecutor()))
                    account.addAll(new HashSet<>(StrUtil.split(coOperation.getExecutor(), StrUtil.COMMA)));
                if (StrUtil.isNotEmpty(coOperation.getReviewer()))
                    account.addAll(new HashSet<>(StrUtil.split(coOperation.getReviewer(), StrUtil.COMMA)));
            }
        }
        if (!pageConfig.getNotifyContent().contains(type.getCode().toString()) || CollUtil.isEmpty(account))
            return;
        CoProject coProject = coProjectService.queryProject(operationId);
        String content = String.format(CONTENT, operationId, coProject.getProjectManager(), type.getName());
        List<SysNotify> beans = build(operationId, type, account, pageConfig.getNotifyType(), content);
        PartitionUtils.part(beans, sysNotifyService::insertList);
        // 站内信
        sendMessage(beans);
        if (pageConfig.getNotifyType().contains(NotifyFormEnum.MAIL.getCode().toString())) {
            ThreadUtil.execAsync(() -> sendMail(beans, pageConfig));
        }
    }

    private void sendMessage(List<SysNotify> messages) {
        wsTemplate.convertAndSend(WsConstants.MESSAGE_NOTIFY, messages);
    }

    private void sendMail(List<SysNotify> messages, SystemConfigVO pageConfig) {
        MailAccount mailAccount = new MailAccount();
        mailAccount.setHost(pageConfig.getMailHost());
        mailAccount.setUser(pageConfig.getMailUser());
        mailAccount.setFrom(pageConfig.getMailUser());
        mailAccount.setPass(SecurityUtils.decryptAes(pageConfig.getMailPass()));
        mailAccount.setPort(pageConfig.getMailPort());
        mailAccount.setAuth(true);
        mailAccount.setSslEnable(pageConfig.getMailSsl() == 1);
        Mail mail = Mail.create(mailAccount);
        messages.forEach(msg -> {
            SysUser user = userService.selectUserById(msg.getUserId());
            if (Objects.isNull(user)) {
                log.warn("邮件发送失败，用户不存在");
                return;
            }
            if (StrUtil.isEmpty(user.getEmail())) {
                log.warn("邮件发送失败，用户未填写邮箱");
                return;
            }
            mail.setTitle(NotifyTypeEnum.of(msg.getType()).getName());
            mail.setContent(msg.getContent());
            mail.setTos(user.getEmail());
            mail.send();
        });
    }

    private List<SysNotify> build(String operationId, NotifyTypeEnum type, Set<String> userIds, String form, String content) {
        return userIds.stream().filter(StrUtil::isNotEmpty).map(id -> {
            return SysNotify.builder()
                    .operationId(operationId)
                    .userAccount(SecurityUtils.getAccount())
                    .form(form)
                    .type(type.getCode())
                    .content(content)
                    .read(0)
                    .date(new Date())
                    .build();
        }).collect(Collectors.toList());
    }
}
