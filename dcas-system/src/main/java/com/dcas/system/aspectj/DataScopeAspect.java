package com.dcas.system.aspectj;

import com.dcas.common.annotation.DataScope;
import com.dcas.common.core.domain.BaseEntity;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.model.dto.SSOAccountDTO;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class DataScopeAspect {
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 部门数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 部门及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, DataScope controllerDataScope) throws Throwable {
        clearDataScope(point);
        handleDataScope(point, controllerDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, DataScope controllerDataScope) {
        // 获取当前的用户
        SSOAccountDTO loginUser = SecurityUtils.getMcUser();
        // 如果是超级管理员，则不过滤数据
        if (!Objects.equals("manager", loginUser.getAccount())) {
            dataScopeFilter(joinPoint, loginUser, controllerDataScope.deptAlias(),
                    controllerDataScope.userAlias(), controllerDataScope.extra());
        }
    }

    /**
     * 数据范围过滤
     *  @param joinPoint  切点
     * @param user       用户
     * @param deptAlias  部门别名
     * @param userAlias  用户别名
     * @param extra
     */
    public static void dataScopeFilter(JoinPoint joinPoint, SSOAccountDTO user, String deptAlias, String userAlias, String extra) {
        StringBuilder sqlString = new StringBuilder();

        // 若未配置角色，则只能看自己
        if (StringUtils.isNotBlank(userAlias)) {
            if ("job".equals(extra)) {
                sqlString.append(StringUtils.format(
                        "AND ({}.create_by = '{}' or find_in_set('{}',{}.executor_account) or find_in_set('{}',{}.reviewer_account))",
                        userAlias, user.getAccount(), user.getAccount(), userAlias, user.getAccount(), userAlias));
            } else  if ("job-review".equals(extra)) {
                sqlString.append(StringUtils.format(
                        " OR ({}.create_by = '{}' or find_in_set('{}',{}.reviewer_account))",
                        userAlias, user.getAccount(), user.getAccount(), userAlias));
            }  else if ("job-executor".equals(extra)){
                sqlString.append(StringUtils.format(
                        " OR ({}.create_by = '{}' or find_in_set('{}',{}.executor_account))",
                        userAlias, user.getAccount(), user.getAccount(), userAlias));
            } else if ("source".equals(extra)) {
                sqlString.append(StringUtils.format(
                        "AND ( find_in_set('{}', user_account) )", user.getAccount()));
            } else {
                sqlString.append(StringUtils.format(" OR {}.create_by = '{}' ", userAlias, user.getAccount()));
            }
        }

        if (StringUtils.isNotBlank(sqlString.toString())) {
            Object params = joinPoint.getArgs()[0];
            if (StringUtils.isNotNull(params) && params instanceof RequestModel){
                RequestModel requestModel = (RequestModel)params;
                if (requestModel.getPrivator() instanceof BaseEntity){
                    BaseEntity baseEntity = (BaseEntity) requestModel.getPrivator();
                    baseEntity.getParams().put(DATA_SCOPE, " AND (" + sqlString.substring(4) + ") " );
                }
            } else if (StringUtils.isNotNull(params) && params instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) params;
                baseEntity.getParams().put(DATA_SCOPE, " AND (" + sqlString.substring(4) + ") " );
            }
        }
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint) {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params) && params instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) params;
            baseEntity.getParams().put(DATA_SCOPE, "");
        }
    }
}
