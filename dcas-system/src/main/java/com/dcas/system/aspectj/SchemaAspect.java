package com.dcas.system.aspectj;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.mark.LatestVersion;
import com.dcas.system.holder.DynamicSchemaContextHolder;
import com.dcas.system.manager.DynamicSchemaManager;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/11/14 17:25
 * @since 1.6.0
 */
@Slf4j
@Aspect
@Component
public class SchemaAspect {

    @Pointcut("@annotation(com.dcas.common.annotation.SchemaSwitch)")
    public void switchSchema() {
    }

    @Around("switchSchema()")
    public Object around(ProceedingJoinPoint pointcut) throws Throwable {
        String version = null;
        String operationId = null;
        MethodSignature signature = (MethodSignature)pointcut.getSignature();
        SchemaSwitch annotation = signature.getMethod().getAnnotation(SchemaSwitch.class);
        Class<?> clazz = annotation.value();
        if (clazz == LatestVersion.class) {
            // 默认切换最新版本知识库
            version = DynamicSchemaManager.getLatestVersion();
        } else {
            Object[] args = pointcut.getArgs();
            for (Object arg : args) {
                if (arg.getClass() != clazz)
                    continue;
                Object obj = JSONObject.parseObject(JSON.toJSONString(arg), clazz);
                if (clazz == String.class || clazz == Integer.class) {
                    operationId = obj.toString();
                } else {
                    operationId = ReflectUtil.getFieldValue(obj, "operationId").toString();
                }
                if (StrUtil.isEmpty(operationId))
                    continue;
                version = DynamicSchemaManager.getVersion(operationId);
                break;
            }
        }
        if (StrUtil.isNotEmpty(version))
            DynamicSchemaContextHolder.push(version);
        return pointcut.proceed();
    }


    @After("switchSchema()")
    public void after() {
        DynamicSchemaContextHolder.clear();
        log.info("clear dynamic schema!");

    }
}
