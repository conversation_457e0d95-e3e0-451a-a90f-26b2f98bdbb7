package com.dcas.system.report.attachment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.*;
import com.dcas.common.excel.WaterMarkHandler;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.model.excel.InventoryAttachmentDefaultExcel;
import com.dcas.common.model.excel.InventoryAttachmentIovExcel;
import com.dcas.common.model.vo.FormConfigTreeVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.system.report.ReportTypeEnum;
import com.dcas.system.report.ReportUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 汽车模板资产清单附件报告
 *
 * <AUTHOR>
 * @date 2024/01/08 20:02
 **/
@RequiredArgsConstructor
@Component
public class IovAttachmentReport implements AttachmentReportInterface {

    private final CoInventoryMapper coInventoryMapper;
    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;
    private final CoLicenseMapper coLicenseMapper;

    @Value("${safety.profile}")
    private String basePath;

    @SchemaSwitch(ExportWordDto.class)
    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo vo) throws Exception {

        String path = String.join(File.separator, basePath, "temp", "附件：数据资产清单.xlsx");
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        // 附件 资产列表
        //查询资产盘点表
        List<InventoryAttachmentIovExcel> result = getAssetRisk(dto.getOperationId());
        EasyExcelFactory.write(out)
            .withTemplate(new ClassPathResource("classpath://template/InventoryAttachmentIovTemplate.xlsx").getStream())
            .registerWriteHandler(WaterMarkHandler.simple(coLicenseMapper.queryCustomName()))
            .sheet().doWrite(result);
        return path;
    }

    @Override
    public void exportWord(HttpServletResponse response, ExportWordDto dto, QueryProjectOperationExportVo vo)
        throws Exception {
        String path = exportWord(dto, vo);
        ReportUtil.output(response, path);
    }

    private List<InventoryAttachmentIovExcel> getAssetRisk(String operationId) {
        // 附件 资产列表
        //查询资产盘点表
        QueryWrapper<CoInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        queryWrapper.orderByDesc("bus_system");
        List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper);
        List<InventoryAttachmentIovExcel> list = new ArrayList<>();
        Map<String, String> riskMap = getRiskLevelMap(operationId);
        coInventoryList.stream().map(
                co -> InventoryAttachmentIovExcel.builder().schemaName(co.getSchemaName()).dataAsset(co.getDataAsset())
                    .assetComment(co.getAssetComment()).busSystem(co.getBusSystem()).sensitiveLevel(co.getSensitiveLevel()).riskLevel(riskMap.get(
                        StrUtil.join("_", co.getBusSystem(), co.getDataAsset())))
                    .build()).collect(Collectors.groupingBy(InventoryAttachmentIovExcel::getBusSystem))
            .forEach((k, v) -> {
                list.addAll(v);
            });
        return list;
    }

    private Map<String, String> getRiskLevelMap(String operationId) {
        List<Map<String, Object>> riskCalcList = new ArrayList<>();
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        String titlesStr = "";
        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            titlesStr = coModelAnalysisResult.getTitles();
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
        Map<String, String> indexColumnMap = new HashMap<>(16);
        titles.forEach(formConfigTreeVO -> {
            if (formConfigTreeVO.getDataColumn()) {
                String title = formConfigTreeVO.getTitle().trim();
                switch (title) {
                    case "风险等级":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskLevel");
                        break;
                    default:
                        break;
                }
            } else {
                if (CollUtil.isNotEmpty(formConfigTreeVO.getChildren())) {
                    formConfigTreeVO.getChildren().forEach(child -> {
                        if ("资产名称".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "name");
                        }
                    });
                } else {
                    if ("所属业务系统".equals(formConfigTreeVO.getTitle())) {
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "busSystem");
                    }
                }
            }
        });
        resultList.forEach(map -> {
            Map<String, Object> resultMap = new HashMap<>();
            for (Object o : map.keySet()) {
                String key = (String)o;
                if (indexColumnMap.get(key) == null) {
                    continue;
                }
                String column = indexColumnMap.get(key);
                switch (column) {
                    case "busSystem":
                        resultMap.put("busSystem", (String)map.get(key));
                        break;
                    case "name":
                        resultMap.put("name", (String)map.get(key));
                        break;
                    case "riskLevel":
                        resultMap.put("riskLevel", (String)map.get(key));
                        break;
                    default:
                        break;
                }
            }
            riskCalcList.add(resultMap);
        });
        return riskCalcList.stream().collect(Collectors.toMap(map -> (String) map.get("busSystem") + "_" + (String) map.get("name"), map -> (String) map.get("riskLevel"), (k1,k2)->k1));
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.IOV;
    }

}
