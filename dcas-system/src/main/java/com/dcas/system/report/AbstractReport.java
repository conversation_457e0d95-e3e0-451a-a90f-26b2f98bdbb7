package com.dcas.system.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.*;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.Arith;
import com.dcas.common.utils.PageResult;
import com.dcas.common.utils.StringUtils;
import com.dcas.common.utils.html.EscapeUtil;
import com.dcas.common.utils.poi.DocxUtil;
import com.dcas.common.utils.sign.Base64;
import com.dcas.common.utils.spring.SpringUtils;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.vo.*;
import com.dcas.system.holder.DynamicSchemaContextHolder;
import com.dcas.system.manager.DynamicSchemaManager;
import com.dcas.common.mapper.*;
import com.dcas.system.service.CoAuthorityService;
import com.dcas.system.service.ScanTaskService;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 报告抽象类
 *
 * <AUTHOR>
 * @date 2024/01/08 16:06
 **/
@Slf4j
public abstract class AbstractReport implements ReportInterface {

    public static final String IMAGE_PNG_BASE64 = "data:image/png;base64,";
    private static final String BASE64 = "base64";
    private static final String HEIGHT = "height";
    private static final String WIDTH = "width";

    /**
     * 3.资产分析
     *
     * @param chartList
     * @param requestModel
     * @param model
     * @param coInventories
     * @param coInventoryList
     * @param serviceContentList
     */
    protected void assetAnalysis(List<ExportWordChart> chartList, RequestModel<OperationIdDto> requestModel,
        Map<String, Object> model, List<String> coInventories, List<CoInventory> coInventoryList,
        List<Long> serviceContentList) {
        CoOperationMapper coOperationMapper = SpringUtils.getBean(CoOperationMapper.class);
        CoConstantMapper coConstantMapper = SpringUtils.getBean(CoConstantMapper.class);
        CoAuthorityService coAuthorityService = SpringUtils.getBean(CoAuthorityService.class);

        // 资产盘点
        model.put("assetsCheck", false);
        if (serviceContentList.contains(LabelEnum.ZCPD.getCode())) {
            model.put("assetsCheck", true);
            model.put("relatedSystem", coInventories.isEmpty() ? null : String.join("、", coInventories));
            model.put("relatedSystemNum", coInventories.size());
            //数据资产总条数
            int dataAssetsTotal = coInventoryList.size();
            //高敏感资产
            List<CoInventory> highSensitiveAssetsList = new ArrayList<>();
            QueryWrapper<CoConstant> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("customer_id",
                coOperationMapper.selectCustomIdByOperationId(requestModel.getPrivator().getOperationId()));
            List<CoConstant> coConstants = coConstantMapper.selectList(queryWrapper);
            //客户最高敏感等级
            int level = coConstants.get(0).getHighestSensitiveLevel();
            //客户最高敏感等级
            sensitiveAssetProcess(level, coInventoryList, highSensitiveAssetsList, new ArrayList<>(),
                new ArrayList<>());

            //高敏感资产占比
            double highSensitiveAssetsProportion = dataAssetsTotal == 0 ? 0.0 :
                Arith.round((double)highSensitiveAssetsList.size() / dataAssetsTotal * 100, 2);
            model.put("dataAssetsNum", dataAssetsTotal);
            model.put("highSensitiveAssetsNum", highSensitiveAssetsList.size());
            model.put("highSensitiveAssetsProportion", highSensitiveAssetsProportion);

            List<ExportWordChart> sensitiveChartList =
                chartList.stream().filter(s -> "敏感数据占比".equals(s.getName())).collect(Collectors.toList());
            Map<String, String> imageBase64Data1 = getPicture(sensitiveChartList);
            putImage("sensitiveAssetsProportionImg", imageBase64Data1, model);

        }

        // 数据权限
        model.put("dataAuthority", false);
        if (serviceContentList.contains(LabelEnum.SJQX.getCode())) {
            model.put("dataAuthority", true);
            List<ReportAuthorityStatusQuoVo> statusQuoList = coAuthorityService.queryStatusQuo(requestModel);
            int crudTableNum = 0;
            int insertTableNum = 0;
            int deleteTableNum = 0;
            int dropTableNum = 0;
            int updateTableNum = 0;
            int selectTableNum = 0;
            int usernameTotal = 0;
            for (ReportAuthorityStatusQuoVo s : statusQuoList) {
                if (Objects.equals(s.getName(), ReportAuthorityPriEnum.CRUD_TABLE.getInfo())) {
                    crudTableNum = s.getUsernameNum();
                } else if (Objects.equals(s.getName(), ReportAuthorityPriEnum.INSERT_TABLE.getInfo())) {
                    insertTableNum = s.getUsernameNum();
                } else if (Objects.equals(s.getName(), ReportAuthorityPriEnum.DELETE_TABLE.getInfo())) {
                    deleteTableNum = s.getUsernameNum();
                } else if (Objects.equals(s.getName(), ReportAuthorityPriEnum.DROP_TABLE.getInfo())) {
                    dropTableNum = s.getUsernameNum();
                } else if (Objects.equals(s.getName(), ReportAuthorityPriEnum.UPDATE_TABLE.getInfo())) {
                    updateTableNum = s.getUsernameNum();
                } else if (Objects.equals(s.getName(), ReportAuthorityPriEnum.SELECT_TABLE.getInfo())) {
                    selectTableNum = s.getUsernameNum();
                }
                usernameTotal = s.getTotal();
            }

            double crudTableNumProportion = usernameTotal > 0 ? (double)crudTableNum / (double)usernameTotal : 0;
            double dropTableNumProportion = usernameTotal > 0 ? (double)dropTableNum / (double)usernameTotal : 0;
            double deleteTableNumProportion = usernameTotal > 0 ? (double)deleteTableNum / (double)usernameTotal : 0;
            double updateTableNumProportion = usernameTotal > 0 ? (double)updateTableNum / (double)usernameTotal : 0;
            double insertTableNumProportion = usernameTotal > 0 ? (double)insertTableNum / (double)usernameTotal : 0;
            double selectTableNumProportion = usernameTotal > 0 ? (double)selectTableNum / (double)usernameTotal : 0;
            model.put("usernameTotal", usernameTotal);
            model.put("crudTableNum", crudTableNum);
            model.put("dropTableNum", dropTableNum);
            model.put("deleteTableNum", deleteTableNum);
            model.put("updateTableNum", updateTableNum);
            model.put("insertTableNum", insertTableNum);
            model.put("selectTableNum", selectTableNum);
            model.put("crudTableNumProportion", Arith.round(crudTableNumProportion * 100, 2));
            model.put("dropTableNumProportion", Arith.round(dropTableNumProportion * 100, 2));
            model.put("deleteTableNumProportion", Arith.round(deleteTableNumProportion * 100, 2));
            model.put("updateTableNumProportion", Arith.round(updateTableNumProportion * 100, 2));
            model.put("insertTableNumProportion", Arith.round(insertTableNumProportion * 100, 2));
            model.put("selectTableNumProportion", Arith.round(selectTableNumProportion * 100, 2));

            // 用户权限分类统计结果
            // 生命周期图表

            //3.2.4	用户-资产权限分布分析
            List<ReportAuthorityUserAssetVo> reportAuthorityUserAssetVos =
                coAuthorityService.queryUserAssetsResultWithBusSystem(requestModel);
            AtomicInteger i = new AtomicInteger();
            model.put("totalUser", reportAuthorityUserAssetVos.size());
            List<ReportAuthorityUserAssetVo> top20 = reportAuthorityUserAssetVos.stream().peek(p -> p.setSort(i.getAndIncrement() + 1)).limit(20).collect(Collectors.toList());
            model.put("userAssetAuthorityList", top20);

            // 3.2.2 高危数据权限分布
            List<BusSystemAuthorityOverviewDTO> busSystemAuthorityOverviewList =
                coAuthorityService.queryBusSystemAuthorityOverview(requestModel.getPrivator().getOperationId());
            AtomicInteger sort = new AtomicInteger();
            busSystemAuthorityOverviewList.forEach(p -> p.setSort(sort.getAndIncrement() + 1));
            model.put("busSystemAuthorityList", busSystemAuthorityOverviewList);

        }
    }

    /**
     * 6.处置建议
     *
     * @param operationId
     * @param model
     */
    protected void processSuggestions(String operationId, Map<String, Object> model) {
        List<Map<String, Object>> dataSaftyHighSuggests = new ArrayList<>();
        List<Map<String, Object>> dataTecHighSuggests = new ArrayList<>();
        List<Map<String, Object>> dataSaftyMidSuggests = new ArrayList<>();
        List<Map<String, Object>> dataTecMidSuggests = new ArrayList<>();
        AdviseSchemeMapper adviseSchemeMapper = SpringUtils.getBean(AdviseSchemeMapper.class);
        CoLegalMapper coLegalMapper = SpringUtils.getBean(CoLegalMapper.class);

        // 按照处置建议type（管理方案、技术方案）分组
        //        List<AdviseSchemeVO> list = adviseSchemeMapper.selectByOperationIdAndType(operationId, null);
        Map<Integer, List<AdviseSchemeVO>> abilityItems =
            adviseSchemeMapper.selectSuggestsByOperationId(operationId, null).stream()
                .collect(Collectors.groupingBy(AdviseSchemeVO::getSchemeType));
        //        Set<String> itemIdList = list.stream().map(AdviseSchemeVO::getItemId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(abilityItems)) {
            abilityItems.forEach((k, adviseSchemeVOList) -> {
                if (k == 1) {
                    // 技术方案
                    AtomicInteger sort = new AtomicInteger(1);
                    adviseSchemeVOList.stream().filter(adviseSchemeVO -> adviseSchemeVO.getPriority() == 3)
                        .collect(Collectors.groupingBy(AdviseSchemeVO::getDescribe)).forEach((k1, v1) -> {
                        Map<String, Object> dataTec = new HashMap<>(16);
                        dataTec.put("sort", sort.get());
                        dataTec.put("item", k1);
                        dataTec.put("desc", getDesc(v1));
                        dataTecHighSuggests.add(dataTec);
                        sort.getAndIncrement();
                    });
                    AtomicInteger sort1 = new AtomicInteger(1);
                    adviseSchemeVOList.stream().filter(adviseSchemeVO -> adviseSchemeVO.getPriority() == 2)
                        .collect(Collectors.groupingBy(AdviseSchemeVO::getDescribe)).forEach((k1, v1) -> {
                        Map<String, Object> dataTec = new HashMap<>(16);
                        dataTec.put("sort", sort1.get());
                        dataTec.put("item", k1);
                        dataTec.put("desc", getDesc(v1));
                        dataTecMidSuggests.add(dataTec);
                        sort1.getAndIncrement();
                    });
                }
                if (k == 2) {
                    //管理方案
                    AtomicInteger sort = new AtomicInteger(1);
                    adviseSchemeVOList.stream().filter(adviseSchemeVO -> adviseSchemeVO.getPriority() == 3)
                        .collect(Collectors.groupingBy(AdviseSchemeVO::getDescribe)).forEach((k1, v1) -> {
                        Map<String, Object> dataSafty = new HashMap<>(16);
                        dataSafty.put("sort", sort.get());
                        dataSafty.put("item", k1);
                        dataSafty.put("desc", getDesc(v1));
                        dataSaftyHighSuggests.add(dataSafty);
                        sort.getAndIncrement();
                    });
                    AtomicInteger sort1 = new AtomicInteger(1);
                    adviseSchemeVOList.stream().filter(adviseSchemeVO -> adviseSchemeVO.getPriority() == 2)
                        .collect(Collectors.groupingBy(AdviseSchemeVO::getDescribe)).forEach((k1, v1) -> {
                        Map<String, Object> dataSafty = new HashMap<>(16);
                        dataSafty.put("sort", sort1.get());
                        dataSafty.put("item", k1);
                        dataSafty.put("desc", getDesc(v1));
                        dataSaftyMidSuggests.add(dataSafty);
                        sort1.getAndIncrement();
                    });
                }
            });
        }
        model.put("dataSaftyHighSuggests", dataSaftyHighSuggests);
        model.put("dataTecHighSuggests", dataTecHighSuggests);
        model.put("dataSaftyMidSuggests", dataSaftyMidSuggests);
        model.put("dataTecMidSuggests", dataTecMidSuggests);
        String lawFile = coLegalMapper.queryLawFileByOptionId(operationId).stream()
            .map(lawName -> StrUtil.concat(true, "《", lawName, "》")).collect(Collectors.joining("、"));
        model.put("lawFile", lawFile);
    }


    protected String getDesc(List<AdviseSchemeVO> list) {
        StringBuilder sb = new StringBuilder();
        Set<String> duplicateSet = new HashSet<>();
        int sort = 1;
        for (int i = 0; i < list.size(); i++) {
            String schema = list.get(i).getScheme();
            if (duplicateSet.contains(schema)) {
                continue;
            } else {
                duplicateSet.add(schema);
            }
            sb.append(sort++).append(".").append(HtmlUtil.cleanHtmlTag(EscapeUtil.fixIncompleteTags(schema)));
            if (i == list.size() - 1) {
                continue;
            }
            sb.append(StrUtil.LF);
        }
        return sb.toString().trim();
    }

    public static void main(String[] args) {
        StringBuilder sb = new StringBuilder();
        sb.append("11111");
        sb.append(StrUtil.LF);
        System.out.print(sb.toString().trim());
    }

    protected Map<String, String> getPicture(List<ExportWordChart> list) {
        Map<String, String> map = new HashMap<>();
        if (CollUtil.isNotEmpty(list) && Objects.nonNull(list.get(0))) {
            ExportWordChart exportWordChart = list.get(0);
            if (exportWordChart.getPicture().length() <= IMAGE_PNG_BASE64.length())
                return map;
            map.put(BASE64, exportWordChart.getPicture().substring(IMAGE_PNG_BASE64.length()));
            map.put(HEIGHT, exportWordChart.getHeight().toString());
            map.put(WIDTH, exportWordChart.getWidth().toString());
        }
        return map;
    }

    protected void putImage(String name, Map<String, String> map, Map<String, Object> model) {
        if (CollUtil.isNotEmpty(map)) {
            model.put(name, getPictureStream(map));
        }
    }

    protected PictureRenderData getPictureStream(Map<String, String> map) {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(Base64.decode(map.get(BASE64)));
        int width = Integer.parseInt(map.getOrDefault(WIDTH, "550"));
        int height = Integer.parseInt(map.getOrDefault(HEIGHT, "450"));
        // 超过居中宽度等比缩小
        double rate = NumberUtil.div(550, width, 2);
        return Pictures.ofStream(inputStream, PictureType.PNG).size((int)(width * rate), (int)(height * rate)).create();
    }

    /**
     * 获取合规结果
     *
     * @param retrieveLegal request
     * @return * @return QueryViewLegalResultVo
     * @Date 2022/9/21 15:47
     */
    public QueryViewLegalResultVo getLegalResultList(QueryLegalDTO retrieveLegal) {
        CoLegalMapper coLegalMapper = SpringUtils.getBean(CoLegalMapper.class);
        List<CoLegal> coLegalList = coLegalMapper.queryLegalList(retrieveLegal);
        List<CoLegal> lawList = coLegalList.stream().collect(Collectors
            .collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CoLegal::getLawName))),
                ArrayList::new));
        //总对标文件数量
        List<CoLegal> lawNumList =
            lawList.stream().filter(p -> StringUtils.isNotBlank(p.getArticleCode())).collect(Collectors.toList());
        Integer lawTotalNum = lawNumList.size();
        //法律文件数量
        List<CoLegal> lawDocList =
            lawList.stream().filter(p -> p.getArticleCode().contains("NL")).collect(Collectors.toList());
        Integer lawDocNum = lawDocList.size();
        //法规文件数量
        List<CoLegal> ruleDocList = lawList.stream().filter(
            p -> p.getArticleCode().contains("GL") || p.getArticleCode().contains("DR") || p.getArticleCode()
                .contains("LL") || p.getArticleCode().contains("IR") || p.getArticleCode().contains("LR"))
            .collect(Collectors.toList());
        Integer ruleDocNum = ruleDocList.size();
        //标准文件数量
        List<CoLegal> standardDocList = lawList.stream().filter(
            p -> p.getArticleCode().contains("GS") || p.getArticleCode().contains("NS") || p.getArticleCode()
                .contains("IS") || p.getArticleCode().contains("LS") || p.getArticleCode().contains("TS"))
            .collect(Collectors.toList());
        Integer standardDocNum = standardDocList.size();

        //法律法规名称
        String lawName = "";
        if (LegalModelEnum.ZHMB.getInfo().equals(retrieveLegal.getLawName())) {
            lawName = LegalModelEnum.ZHMB.getInfo();
        } else {
            lawName = CollUtil.isEmpty(coLegalList) ? retrieveLegal.getLawName() : coLegalList.get(0).getLawName();
        }
        //总对标项数量(合计对标条文数量)
        double itemTotalNum = coLegalList.size();

        //完全符合项数量
        List<CoLegal> countAList =
            coLegalList.stream().filter(p -> OptEnum.A.getInfo().equals(p.getResult())).collect(Collectors.toList());
        double countANum = countAList.size();
        //完全符合项占比
        BigDecimal countAProportion =
            itemTotalNum > 0 ? BigDecimal.valueOf(Arith.round(countANum / itemTotalNum * 100, 2)) : BigDecimal.ZERO;
        //部分符合项数量
        List<CoLegal> countBList =
            coLegalList.stream().filter(p -> OptEnum.B.getInfo().equals(p.getResult())).collect(Collectors.toList());
        double countBNum = countBList.size();
        //部分符合项占比
        BigDecimal countBProportion =
            itemTotalNum > 0 ? BigDecimal.valueOf(Arith.round(countBNum / itemTotalNum * 100, 2)) : BigDecimal.ZERO;
        //合格率（“完全符合项”* 1+”部分符合项“*0.5）/（”总对标项“）*100%
        BigDecimal qualifiedProportion = itemTotalNum > 0 ?
            BigDecimal.valueOf(Arith.round((countANum * 1 + countBNum * 0.5) / itemTotalNum * 100, 2)) :
            BigDecimal.ZERO;
        //不符合项数量
        List<CoLegal> countCList =
            coLegalList.stream().filter(p -> OptEnum.C.getInfo().equals(p.getResult())).collect(Collectors.toList());
        double countCNum = countCList.size();
        //不符合项占比
        BigDecimal countCProportion =
            itemTotalNum > 0 ? BigDecimal.valueOf(Arith.round(countCNum / itemTotalNum * 100, 2)) : BigDecimal.ZERO;
        //不涉及项数量
        List<CoLegal> countDList =
            coLegalList.stream().filter(p -> OptEnum.D.getInfo().equals(p.getResult())).collect(Collectors.toList());
        double countDNum = countDList.size();
        //不涉及项占比
        BigDecimal countDProportion =
            itemTotalNum > 0 ? BigDecimal.valueOf(Arith.round(countDNum / itemTotalNum * 100, 2)) : BigDecimal.ZERO;

        QueryViewLegalResultVo vo = new QueryViewLegalResultVo();
        vo.setLawTotalNum(lawTotalNum);
        vo.setLawDocNum(lawDocNum + ruleDocNum);
        vo.setRuleDocNum(ruleDocNum);
        vo.setStandardDocNum(standardDocNum);
        vo.setLawName(lawName);
        vo.setItemTotalNum((int)itemTotalNum);
        vo.setCountANum((int)countANum);
        vo.setCountAProportion(countAProportion);
        vo.setCountBNum((int)countBNum);
        vo.setCountBProportion(countBProportion);
        vo.setQualifiedProportion(qualifiedProportion);
        vo.setCountCNum((int)countCNum);
        vo.setCountCProportion(countCProportion);
        vo.setCountDNum((int)countDNum);
        vo.setCountDProportion(countDProportion);
        return vo;
    }

    protected void sensitiveAssetProcess(int level, List<CoInventory> coInventories,
        List<CoInventory> highSensitiveAssetsList, List<CoInventory> mediumSensitiveAssetsList,
        List<CoInventory> lowSensitiveAssetsList) {
        // 设计一个哈希表存储level对应的敏感等级分类标准
        Map<Integer, int[]> sensitivityMapping = new HashMap<>();
        sensitivityMapping.put(3, new int[] {1, 2});
        sensitivityMapping.put(4, new int[] {1, 2});
        sensitivityMapping.put(5, new int[] {1, 3});
        sensitivityMapping.put(6, new int[] {2, 4});
        sensitivityMapping.put(7, new int[] {2, 5});
        sensitivityMapping.put(8, new int[] {2, 5});

        int[] sensitivityThresholds = sensitivityMapping.get(level);
        for (CoInventory coInventory : coInventories) {
            Integer sensitiveLevel = coInventory.getSensitiveLevel();
            if (sensitiveLevel != null) {
                if (sensitiveLevel <= sensitivityThresholds[0]) {
                    lowSensitiveAssetsList.add(coInventory);
                } else if (sensitiveLevel <= sensitivityThresholds[1]) {
                    mediumSensitiveAssetsList.add(coInventory);
                } else {
                    highSensitiveAssetsList.add(coInventory);
                }
            }
        }
    }

    protected void putModelPicture(List<ExportWordChart> chartList, Map<String, Object> model) {
        //敏感数据占比图表 从入参中获取
        List<ExportWordChart> userAddChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(新增)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userDeleteDataChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(删除数据)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userDeleteTableChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(删除表)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userUpdateChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(修改)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userQueryChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(查询)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> basicEnvAnalysis =
            chartList.stream().filter(s -> "基础环境风险分析".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> hostAnalysisResult =
            chartList.stream().filter(s -> "按数据库分析结果".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> legalResult =
            chartList.stream().filter(s -> "合规结果分布一览".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> overviewBusiness =
            chartList.stream().filter(s -> "业务系统风险一览".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> overviewAsset =
            chartList.stream().filter(s -> "资产数量列表".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> overviewBusinessSort =
            chartList.stream().filter(s -> "业务系统风险排序".equals(s.getName())).collect(Collectors.toList());

        Map<String, String> imageBase64Data2 = getPicture(userAddChartList);
        Map<String, String> imageBase64Data3 = getPicture(userDeleteDataChartList);
        Map<String, String> imageBase64Data4 = getPicture(userDeleteTableChartList);
        Map<String, String> imageBase64Data5 = getPicture(userUpdateChartList);
        Map<String, String> imageBase64Data6 = getPicture(userQueryChartList);
        Map<String, String> imageBase64Data7 = getPicture(basicEnvAnalysis);
        Map<String, String> imageBase64Data8 = getPicture(hostAnalysisResult);
        Map<String, String> imageBase64Data9 = getPicture(legalResult);
        Map<String, String> imageBase64Data10 = getPicture(overviewBusiness);
        Map<String, String> imageBase64Data11 = getPicture(overviewBusinessSort);
        Map<String, String> imageBase64Data12 = getPicture(overviewAsset);

        putImage("userAuthorityTypeAddImg", imageBase64Data2, model);
        putImage("userAuthorityTypeDeleteImg", imageBase64Data3, model);
        putImage("userAuthorityTypeDropImg", imageBase64Data4, model);
        putImage("userAuthorityTypeUpdateImg", imageBase64Data5, model);
        putImage("userAuthorityTypeSelectImg", imageBase64Data6, model);
        putImage("loopholAnalysisResultImg", imageBase64Data7, model);
        putImage("loopholByDBIPResultImg", imageBase64Data8, model);
        putImage("legalResultImg", imageBase64Data9, model);
        putImage("queryLifecycleReslutImg", imageBase64Data10, model);
        putImage("queryLifecycleAssetRiskNumImg", imageBase64Data12, model);
        putImage("queryLifecycleReslutListImg", imageBase64Data11, model);
    }

    @Override
    public void exportWord(HttpServletResponse response, ExportWordDto dto) throws IOException {
        throw new ServiceException("not implements!");
    }

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception {
        throw new ServiceException("not implements!");
    }

    protected void addScanReportToPath(List<String> filePathList, String operationId) {
        ScanTaskMapper scanTaskMapper = SpringUtil.getBean(ScanTaskMapper.class);
        QueryWrapper<ScanTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("report_path");
        queryWrapper.eq("operation_id", operationId);
        queryWrapper.eq("status", ScanTaskStatus.FINISH.getCode());
        List<ScanTask> scanTasks = scanTaskMapper.selectList(queryWrapper);
        scanTasks.forEach(t -> filePathList.add(t.getReportPath()));
    }

    protected String zip(List<String> filePathList, String operationName, String basePath) {
        log.info("filePathList is 【{}】",JSONUtil.toJsonStr(filePathList));
        String realFileName = String.format("%s报告.zip", operationName);
        String tarFilePath = basePath + File.separator + "temp" + File.separator + realFileName;
        File targetFile = new File(tarFilePath);
        FileUtil.touch(targetFile);
        ZipUtil.zip(targetFile, false, filePathList.stream().filter(FileUtil::exist).map(File::new).toArray(File[]::new));
        return tarFilePath;
    }

    @Override
    public Set<LabelEnum> getNeedExcludeContent() {
        return CollUtil.newHashSet();
    }

    protected void putCommonInfo(QueryProjectOperationExportVo poVo, Map<String, Object> model){
        CoProcessTreeMapper coProcessTreeMapper = SpringUtils.getBean(CoProcessTreeMapper.class);

        //文本
        model.put("reportCompany", "杭州美创科技股份有限公司");
        model.put("reportCompanyAbbreviation", "美创科技");
        model.put("productName", "美创数据安全综合评估系统（DCAS）");
        model.put("analysisFileRefList", "分析模板文件列表");
        model.put("relatedSystem", StringUtils.replace(poVo.getRelatedSystem(), ",", "、"));
        model.put("relatedSystemCount", StringUtils.countMatches(poVo.getRelatedSystem(), ",")+1);
        model.put("customerName", poVo.getCustomerName());
        model.put("projectName", poVo.getOperationName());
        model.put("date", LocalDate.now());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        model.put("createTime", simpleDateFormat.format(poVo.getCreateTime()));
        model.put("startDate", poVo.getStartDate() != null ? simpleDateFormat.format(poVo.getStartDate()) : "");
        model.put("endDate", poVo.getEndDate() != null ? simpleDateFormat.format(poVo.getEndDate()) : "");
        model.put("completedDate",
            ObjectUtils.isEmpty(poVo.getCompletedDate()) ? null : simpleDateFormat.format(poVo.getCompletedDate()));
        model.put("customerDirector", poVo.getCustomerDirector());
        model.put("projectManager", poVo.getProjectManager());
        model.put("executor", StringUtils.replace(poVo.getExecutorAccount(), ",", "、"));
        model.put("reviewer", StringUtils.replace(poVo.getReviewerAccount(), ",", "、"));
        model.put("projectStartTime", simpleDateFormat.format(poVo.getCreateTime()));
        model.put("duration", poVo.getDuration());
        model.put("createBy", poVo.getCreateBy());
        model.put("phone", poVo.getPhone());
        SimpleDateFormat simpleDateFormatYearMonth = new SimpleDateFormat("yyyy年MM月dd日");
        model.put("reportDate", simpleDateFormatYearMonth.format(new Date()));
        model.put("startJobDate", simpleDateFormatYearMonth.format(poVo.getCreateTime()));

        //评估内容
        List<Long> serviceContentList = JSON.parseArray("[" + poVo.getServiceContent() + "]", Long.class);
        // 过程清单
        List<LabelVO> processLabelList = coProcessTreeMapper.selectParentByLabelIds(serviceContentList);
        model.put("processBillByLine",
            processLabelList.stream().map(LabelVO::getName).collect(Collectors.joining(StrUtil.DASHED)));
        model.put("processBill", processLabelList.stream().map(LabelVO::getName).collect(Collectors.joining("、")));
    }

    protected void pushVersion(String operationId){
        String version = DynamicSchemaManager.getVersion(operationId);
        if (StrUtil.isNotEmpty(version)) {
            DynamicSchemaContextHolder.push(version);
        }
    }

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws Exception {
        throw new ServiceException("not implements!");
    }

    @Override
    public String process(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception {
        throw new ServiceException("not implements!");
    }

    @Override
    public List<String> process(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws Exception {
        throw new ServiceException("not implements!");
    }

    public Map<String, String> getBpCodeDescribe(String operationId) {
        // 手动push知识库版本
        pushVersion(operationId);
        QuestionnaireContentMapper questionnaireContentMapper = SpringUtils.getBean(QuestionnaireContentMapper.class);
        List<QuestionnaireResultDTO> questionnaireResultList =
            questionnaireContentMapper.getQuestionnaireResult(operationId);
        Map<String, String> bpCodeMap = new HashMap<>(16);
        if (CollUtil.isEmpty(questionnaireResultList)) {
            return bpCodeMap;
        }
        Map<String, List<QuestionnaireResultDTO>> map =
            questionnaireResultList.stream().collect(Collectors.groupingBy(QuestionnaireResultDTO::getBpCode));
        map.forEach((k, v) -> {
            Set<String> duplicateSet = new HashSet<>();
            StringBuilder desc = new StringBuilder();
            for (QuestionnaireResultDTO questionnaireResultDTO : v) {
                if (!duplicateSet.contains(questionnaireResultDTO.getItem())) {
                    if (CharSequenceUtil.isEmpty(questionnaireResultDTO.getExplain())) {
                        desc.append(CharSequenceUtil.addSuffixIfNot(questionnaireResultDTO.getItem(), ";"));
                    } else {
                        desc.append(CharSequenceUtil.join(":", questionnaireResultDTO.getItem(),
                            questionnaireResultDTO.getExplain())).append(";");
                    }
                    duplicateSet.add(questionnaireResultDTO.getItem());
                }
            }
            bpCodeMap.put(k, desc.toString());
        });
        return bpCodeMap;
    }

    public Map<String, String> getLawDescribe(String operationId) {
        QuestionnaireContentMapper questionnaireContentMapper = SpringUtils.getBean(QuestionnaireContentMapper.class);
        List<QuestionnaireResultDTO> questionnaireResultList =
            questionnaireContentMapper.getLawQuestionnaireResult(operationId);
        Map<String, String> bpCodeMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(questionnaireResultList)) {
            Map<String, List<QuestionnaireResultDTO>> map =
                questionnaireResultList.stream().collect(Collectors.groupingBy(QuestionnaireResultDTO::getBpCode));
            map.forEach((k, v) -> {
                Set<String> duplicateSet = new HashSet<>();
                StringBuilder desc = new StringBuilder();
                for (QuestionnaireResultDTO questionnaireResultDTO : v) {
                    if (!duplicateSet.contains(questionnaireResultDTO.getItem())) {
                        if (CharSequenceUtil.isEmpty(questionnaireResultDTO.getExplain())) {
                            desc.append(CharSequenceUtil.addSuffixIfNot(questionnaireResultDTO.getItem(), ";"));
                        } else {
                            desc.append(CharSequenceUtil.join(":", questionnaireResultDTO.getItem(),
                                questionnaireResultDTO.getExplain())).append(";");
                        }
                        duplicateSet.add(questionnaireResultDTO.getItem());
                    }
                }
                bpCodeMap.put(k, desc.toString());
            });
        }
        return bpCodeMap;
    }


    protected void baseAssessment(Map<String, Object> model, List<ExportWordChart> chartList,
        List<Long> serviceContentList, ExportWordDto privator) {
        DetectionResultMapper detectionResultMapper = SpringUtils.getBean(DetectionResultMapper.class);
        CoOperationMapper coOperationMapper = SpringUtils.getBean(CoOperationMapper.class);
        ScanTaskService scanTaskService = SpringUtils.getBean(ScanTaskService.class);

        // 5.1基础评估
        model.put("baseAssessment", false);
        if (serviceContentList.contains(LabelEnum.JCHJ.getCode()) && !privator.getNeedExcludeContent()
            .contains(LabelEnum.JCHJ)) {
            model.put("baseAssessment", true);

            List<DetectionResult> detectionResults = detectionResultMapper
                .selectList(new QueryWrapper<DetectionResult>().eq("operation_id", privator.getOperationId()));
            Set<String> dbTypeSet = new HashSet<>();
            Map<String, List<DetectionResult>> dbConfigMap = new HashMap<>(16);
            int successCount = 0;
            int failCount = 0;
            for (DetectionResult detectionResult : detectionResults) {
                dbTypeSet.add(DataSourceType.getType(detectionResult.getDbType()).getName());
                if (dbConfigMap.containsKey(detectionResult.getDbConfig())) {
                    dbConfigMap.get(detectionResult.getDbConfig()).add(detectionResult);
                } else {
                    List<DetectionResult> list = new ArrayList<>();
                    list.add(detectionResult);
                    dbConfigMap.put(detectionResult.getDbConfig(), list);
                }
                if (detectionResult.getAccord()) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            model.put("dbTypeStr", String.join("、", dbTypeSet));
            model.put("dbCount", dbConfigMap.size());
            model.put("totalCount", detectionResults.size());
            model.put("successCount", successCount);
            model.put("failCount", failCount);
            BigDecimal rate = BigDecimal.valueOf(successCount)
                .divide(BigDecimal.valueOf(detectionResults.size()), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            model.put("successRate", rate.setScale(2, RoundingMode.HALF_UP));

            //整体检测记录
            //1、2阶段查询
            QueryProjectOperationExportVo poVo = coOperationMapper.queryOperationExport(privator.getOperationId());
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<Map<String, Object>> detectionRecordList = new ArrayList<>();
            List<String> projectManagerList = StrUtil.split(poVo.getProjectManager(), StrUtil.C_COMMA);
            List<String> executorList = StrUtil.split(poVo.getExecutorAccount(), StrUtil.C_COMMA);
            List<String> reviewerList = StrUtil.split(poVo.getReviewerAccount(), StrUtil.C_COMMA);
            model.put("executor",
                String.join("、", CollUtil.unionDistinct(projectManagerList, executorList, reviewerList)));
            model.put("createTime", simpleDateFormat.format(poVo.getCreateTime()));

            // 数据库检测详情
            List<Map<String, Object>> dbSourceList = new ArrayList<>();

            AtomicInteger sort = new AtomicInteger();
            dbConfigMap.forEach((k, v) -> {

                Map<String, Object> recordMap = new HashMap<>(16);
                DetectionResult detectionResult = v.get(0);

                SourceConfig sourceConfig =
                    com.alibaba.fastjson.JSON.parseObject(detectionResult.getDbConfig(), SourceConfig.class);
                String dbType = DataSourceType.getType(v.get(0).getDbType()).getName();
                long count = v.stream().filter(DetectionResult::getAccord).count();
                int total = v.size();
                recordMap.put("sort", sort.incrementAndGet());
                recordMap.put("dbName", sourceConfig == null ? null : sourceConfig.getConfigName());
                recordMap.put("dbType", dbType);
                recordMap.put("pointCount", total);
                BigDecimal pointRate =
                    BigDecimal.valueOf(count).divide(BigDecimal.valueOf(total), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                recordMap.put("pointRate", pointRate.setScale(2, RoundingMode.HALF_UP));
                detectionRecordList.add(recordMap);

                Map<String, Object> dbSourceInfo = new HashMap<>(16);
                dbSourceInfo.put("dbSourceName", sourceConfig == null ? null : sourceConfig.getConfigName());
                List<ExportWordChart> chartList2 = chartList.stream()
                    .filter(s -> String.format("基础评估分析-%s数据源检测结果图", sourceConfig.getConfigName()).equals(s.getName()))
                    .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(chartList2)) {
                    dbSourceInfo.put("dbSourceAccordImg", getPictureStream(getPicture(chartList2)));
                }
                dbSourceInfo.put("dbType", DataSourceType.getType(detectionResult.getDbType()).getName());
                dbSourceInfo.put("ip", sourceConfig == null ? null : sourceConfig.getHost());
                dbSourceInfo.put("port", sourceConfig == null ? null : sourceConfig.getPort());
                dbSourceInfo.put("dbName", sourceConfig == null ? null : sourceConfig.getDbName());
                dbSourceInfo.put("total", total);
                dbSourceInfo.put("sCount", count);
                dbSourceInfo.put("fCount", total - count);
                dbSourceInfo.put("rate", recordMap.get("pointRate"));

                Map<String, List<DetectionResult>> optionMap =
                    v.stream().filter(result -> StrUtil.isNotEmpty(result.getOption()))
                        .collect(Collectors.groupingBy(DetectionResult::getOption));
                // 检查点详情
                List<Map<String, Object>> detectionPointDetails = new ArrayList<>();
                AtomicInteger pointSort = new AtomicInteger();
                optionMap.forEach((optionName, pointList) -> {
                    pointList.forEach(point -> {
                        Map<String, Object> pointInfo = new HashMap<>(16);
                        pointInfo.put("sort", pointSort.incrementAndGet());
                        pointInfo.put("option", point.getOption());
                        pointInfo.put("point", point.getContent());
                        String accord = point.getAccord() ? "符合" : "不符合";
                        pointInfo.put("detectionResult", accord);
                        pointInfo.put("desc", point.getDescribe());
                        pointInfo.put("suggest", "符合".equals(accord) ? "" : point.getUnqualified());
                        detectionPointDetails.add(pointInfo);
                    });
                });
                dbSourceInfo.put("detectionPointDetails", detectionPointDetails);
                dbSourceList.add(dbSourceInfo);
            });
            model.put("detectionRecordList", detectionRecordList);
            model.put("dbSourceList", dbSourceList);

            List<ExportWordChart> chartList1 =
                chartList.stream().filter(s -> "基础评估分析-数据库安全基线达标率分布图".equals(s.getName())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(chartList1)) {
                model.put("dbAccordRateImg", getPictureStream(getPicture(chartList1)));
            }

            model.put("scanTaskExist", false);
            List<ScanTaskVO> scanTaskList = scanTaskService.queryScanTask(poVo.getOperationId());
            if (CollUtil.isNotEmpty(scanTaskList)){
                int pocNum = 0;
                int highNum = 0;
                int midNum = 0;
                int lowNum = 0;
                int pwNum = 0;
                List<Map<String, Object>> scanRiskList = new ArrayList<>();
                Map<String, Object> hostRisk = new HashMap<>(16);
                int hostSort = 1;
                for (ScanTaskVO scanTaskVO : scanTaskList){
                    hostRisk.put("sort", hostSort++);
                    ScanTaskInfoVO scanTaskInfoVO = scanTaskService.queryScanTaskDetail(scanTaskVO.getTaskId());
                    for (ScanTaskInfoVO.Task task : scanTaskInfoVO.getTask()){
                        pocNum += task.getPocRisk();
                        highNum += task.getHighRisk();
                        midNum += task.getMiddleRisk();
                        lowNum += task.getLowRisk();
                        pwNum += task.getPwNum();
                        hostRisk.put("host", task.getIp());
                        hostRisk.put("riskNum", String
                            .format("可入侵漏洞%d个,高风险漏洞%d个,中风险漏洞%d个,低风险漏洞%d个,弱口令%d个", task.getPocRisk(), task.getHighRisk(),
                                task.getMiddleRisk(), task.getLowRisk(), task.getPwNum()));
                    }
                    scanRiskList.add(hostRisk);
                }
                model.put("scanTaskExist", true);
                model.put("scanRiskList", scanRiskList);
                model.put("hostNum", scanTaskList.size());
                model.put("pocNum", pocNum);
                model.put("highNum", highNum);
                model.put("midNum", midNum);
                model.put("lowNum", lowNum);
                model.put("pwNum", pwNum);
            }
        }
    }

    protected void adviseRiskAnalysis(String operationId, Map<String, Object> model){
        AdviseRiskMapper adviseRiskMapper = SpringUtils.getBean(AdviseRiskMapper.class);
        DynamicProcessTreeMapper dynamicProcessTreeMapper = SpringUtils.getBean(DynamicProcessTreeMapper.class);
        pushVersion(operationId);
        // 合规分析
        List<AdviseRiskDTO> adviseRiskList = adviseRiskMapper.selectRiskContentByType(operationId, 1);
        List<TreeLabelDTO> treeLabelList = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
        Map<Long, String> busSystemMap = treeLabelList.stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId,TreeLabelDTO::getTreeName));
        List<Map<String, Object>> legalRiskList = new ArrayList<>();
        int sort = 1;
        for (AdviseRiskDTO adviseRiskDTO : adviseRiskList) {
            Map<String, Object> map = new HashMap<>();
            map.put("sort", sort++);
            map.put("itemTitle", adviseRiskDTO.getDescribe());
            map.put("content", adviseRiskDTO.getContent());
            map.put("riskLevel", adviseRiskDTO.getRiskLevel());
            String busSystem = StrUtil.split(adviseRiskDTO.getSystemId(), StrUtil.COMMA).stream()
                .map(systemId -> busSystemMap.get(Long.parseLong(systemId))).collect(Collectors.joining(StrUtil.COMMA));
            map.put("busSystem", busSystem);
            map.put("basis", adviseRiskDTO.getBasis());
            legalRiskList.add(map);
        }
        model.put("legalRiskList", legalRiskList);
    }

    protected void addWaterMarkAndPageHeader(XWPFDocument xwpfDocument){
        CoLicenseMapper coLicenseMapper = SpringUtils.getBean(CoLicenseMapper.class);
        String customName = coLicenseMapper.queryCustomName();
        DocxUtil.setWordWaterMark(xwpfDocument, customName, DocxUtil.DEFAULT_FONT_COLOR);
        DocxUtil.modifyPageHeader(xwpfDocument, customName);
    }
}
