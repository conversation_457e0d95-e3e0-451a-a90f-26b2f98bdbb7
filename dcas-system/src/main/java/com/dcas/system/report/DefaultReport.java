package com.dcas.system.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.*;
import com.dcas.common.utils.StringUtils;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.vo.*;
import com.dcas.common.mapper.*;
import com.dcas.common.utils.poi.DocxUtil;
import com.dcas.system.report.attachment.AttachmentReportFactory;
import com.dcas.system.service.CoVerificationService;
import com.dcas.system.service.IRiskAnalysisService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.math.BigDecimal;
import java.text.Collator;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 默认报告
 *
 * <AUTHOR>
 * @date 2024/01/08 16:00
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultReport extends AbstractReport implements ReportInterface {
    private final ModelFileMapper modelFileMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final CoLegalMapper coLegalMapper;
    private final FormConfigMapper formConfigMapper;
    private final CoConstantMapper coConstantMapper;
    private final IndicatorResultMapper indicatorResultMapper;
    private final CoVerificationService coVerificationService;
    private final IRiskAnalysisService iRiskAnalysisService;
    private final StandardItemMapper standardItemMapper;
    private final CoGapAnalysisMapper coGapAnalysisMapper;
    private final AdviseRiskMapper adviseRiskMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final CoSystemResultMapper coSystemResultMapper;

    @Value("${safety.profile}")
    private String basePath;

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception {
        List<String> filePathList = new ArrayList<>();
        CompletableFuture<String> future = CompletableFuture.allOf(CompletableFuture.supplyAsync(() -> {
            try {
                dto.setNeedExcludeContent(getNeedExcludeContent());
                return process(dto, poVo, modelId);
            } catch (IOException e) {
                log.error("导出报告失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v);
                filePathList.add(v);
            }
        }), CompletableFuture.supplyAsync(() -> {
            try {
                return AttachmentReportFactory.getAssetReportHandler(ReportTypeEnum.DEFAULT).exportWord( dto,
                    poVo);
            } catch (Exception e) {
                log.error("导出资产清单附件失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v);
                filePathList.add(v);
            }
        }), CompletableFuture.supplyAsync(() -> {
                try {
                    return  ReportFactory.getReportHandler(ReportTypeEnum.TEC_DETECTION).exportWord(dto, poVo);
                } catch (Exception e) {
                    log.error("导出技术检测报告失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            }))
        .thenRun(() -> addScanReportToPath(filePathList, dto.getOperationId()))
        .thenApply(v -> zip(filePathList, poVo.getOperationName(), basePath));
        return future.get();
    }

    @Override
    @SchemaSwitch(ExportWordDto.class)
    public String process(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws IOException {
        InputStream inputStream;
        ModelFile modelFile = modelFileMapper.selectOne(new QueryWrapper<ModelFile>().eq("model_id", modelId));
        if (modelFile == null) {
            //获取模板地址，注意k8s无法识别中文文件名，中文文件失效
            ClassPathResource classPathResource = new ClassPathResource("template/DCASReportExportTemplate.docx");
            inputStream = classPathResource.getInputStream();
        } else {
            inputStream = new FileInputStream(modelFile.getFilePath());
        }

        //数据模型
        Map<String, Object> model = new HashMap<>(16);

        //准备数据
        String operationId = dto.getOperationId();
        List<ExportWordChart> chartList = dto.getChartList();
        List<ExportWordChart> analysisChartList =
            chartList.stream().filter(s -> s.getName().contains("图") || s.getName().contains("表格"))
                .collect(Collectors.toList());

        // 报告公共部分
        putCommonInfo(poVo, model);

        //查询资产盘点表
        QueryWrapper<CoInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getOperationId());
        queryWrapper.orderByDesc("bus_system");
        List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper);
        List<String> coInventories =
            coInventoryList.stream().map(CoInventory::getBusSystem).distinct().collect(Collectors.toList());

        RequestModel<OperationIdDto> requestModel = new RequestModel<>();
        OperationIdDto operationIdDto = new OperationIdDto();
        operationIdDto.setOperationId(operationId);
        requestModel.setPrivator(operationIdDto);
        int titleIndex = 2;

        // 图片赋值
        putModelPicture(chartList, model);

        //评估内容
        List<Long> serviceContentList = JSON.parseArray("[" + poVo.getServiceContent() + "]", Long.class);

        //3.资产梳理
        //3.2.2 高危数据权限分析
        model.put("assetAnalysis", false);
        if (serviceContentList.contains(LabelEnum.ZCSL.getCode())) {
            model.put("assetAnalysis", true);
            titleIndex++;
            int titleThree = titleIndex;
            model.put("titleThree", titleThree);
            assetAnalysis(chartList, requestModel, model, coInventories, coInventoryList, serviceContentList);
        }

        //4.安全现状
        //4.1.1	评估概述
        model.put("currentSituationAnalysis", false);
        if (serviceContentList.contains(LabelEnum.AQXZ.getCode())) {
            model.put("currentSituationAnalysis", true);
            titleIndex++;
            int titleFour = titleIndex;
            model.put("titleFour", titleFour);
            currentSituationAnalysis(operationId, model,  analysisChartList);
        }

        //5.安全评估
        model.put("riskAssessment", false);
        if (serviceContentList.contains(LabelEnum.FXPG.getCode())) {
            model.put("riskAssessment", true);
            titleIndex++;
            int titleFive = titleIndex;
            model.put("titleFive", titleFive);
            riskAssessment(model, dto, coInventoryList, operationId, chartList,
                serviceContentList, modelId);
        }

        // 6 处置建议
        // 6.1合规风险
        model.put("processSuggestions", false);
        if (serviceContentList.contains(LabelEnum.CZJY.getCode())) {
            model.put("processSuggestions", true);
            titleIndex++;
            int titleSix = titleIndex;
            model.put("titleSix", titleSix);
            processSuggestions(operationId, model);
        }

        // 附件 资产列表
        if (model.containsKey("assetsCheck") && (Boolean)model.get("assetsCheck")) {
            AtomicInteger assetSort = new AtomicInteger(0);
            List<ReportAssetVO> assetList = coInventoryList.stream().map(
                co -> ReportAssetVO.builder().sort(assetSort.incrementAndGet()).schemaName(co.getSchemaName())
                    .dataAsset(co.getDataAsset()).assetComment(co.getAssetComment()).busSystem(co.getBusSystem())
                    .sensitiveLevel(co.getSensitiveLevel()).build()).collect(Collectors.toList());
            model.put("assetList", assetList);
        }

        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
        Configure config = Configure.builder().bind("userAssetAuthorityList", policy).bind("tabList", policy)
            .bind("loopholeByIPSystemList", policy).bind("legalList", policy).bind("assetList", policy)
            .bind("assetRiskHighLevelList", policy).bind("assetRiskMediumLevelList", policy)
            .bind("abilityAdviseContents", policy).bind("legalAdviseContents", policy)
            .bind("legalAdviseContents", policy).bind("detectionRecordList", policy)
            .bind("detectionPointDetails", policy).bind("tocContents", tocPolicy).bind("dataSaftyHighSuggests", policy)
            .bind("dataTecHighSuggests", policy).bind("dataSaftyMidSuggests", policy).bind("dataTecMidSuggests", policy)
            .bind("scanRiskList", policy).bind("busSystemAuthorityList",policy)
            .bind("dataCaptureList",policy)
            .bind("dataTransList",policy)
            .bind("dataStorageList",policy)
            .bind("dataProcessList",policy)
            .bind("dataExchangeList",policy)
            .bind("dataDestoryList",policy)
            .bind("legalRiskList",policy)
            .bind("commonList",policy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();
        // 添加水印和修改页眉内容
        addWaterMarkAndPageHeader(xwpfDocument);

        String realFileName = String.format("%s报告.docx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);

        //输出结果
        OutputStream out = new FileOutputStream(path);
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
        return path;
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.DEFAULT;
    }

    /**
     * 默认模板需排除的章节内容
     */
    @Override
    public Set<LabelEnum> getNeedExcludeContent() {
        return CollUtil.newHashSet();
    }

    /**
     * 5.风险评估
     */
    private void riskAssessment(Map<String, Object> model, ExportWordDto privator,
        List<CoInventory> coInventoryList, String operationId, List<ExportWordChart> chartList,
        List<Long> serviceContentList, Long modelId) {
        List<Map<String, Object>> fileRefList = Lists.newArrayList();
        AtomicInteger fileIndex = new AtomicInteger(1);

        // 5.1基础评估
        baseAssessment(model, chartList, serviceContentList, privator);

        // 5.2合规评估
        lawAssessment(model, privator, serviceContentList, operationId, fileRefList, fileIndex);

        // 5.3风险评估
        riskContentsAssessment(operationId, model, serviceContentList,  fileRefList, fileIndex, chartList, privator, coInventoryList, modelId);

    }

    private void riskContentsAssessment(String operationId, Map<String, Object> model, List<Long> serviceContentList,
        List<Map<String, Object>> fileRefList, AtomicInteger fileIndex, List<ExportWordChart> chartList,
        ExportWordDto privator, List<CoInventory> coInventoryList, Long modelId) {
        // 手动push知识库版本
        pushVersion(operationId);
        Set<String> modelFileList = new HashSet<>();
        List<String> templateFileList = coVerificationMapper.selectTemplateFileByOperationId(operationId);
        if (!CollectionUtils.isEmpty(templateFileList)) {
            Map<String, Object> fileMap = new HashMap<>(templateFileList.size());
            templateFileList.forEach(s -> {
                fileMap.put("sort", fileIndex.getAndIncrement());
                fileMap.put("name", s);
                fileRefList.add(fileMap);
                modelFileList.add(s);
            });
        }
        model.put("fileRefList", fileRefList);
        model.put("modelFileName",CollUtil.join(modelFileList, "、"));
        model.put("riskContentsAssessment", false);
        if (serviceContentList.contains(LabelEnum.SMZQ.getCode()) && !privator.getNeedExcludeContent()
            .contains(LabelEnum.SMZQ)) {
            model.put("riskContentsAssessment", true);

            //5.3.3.1	总体风险分析
            RiskAnalysisReportVO riskAnalysisReportVO = iRiskAnalysisService.queryRiskAnalysisReport(operationId, true);
            RiskAnalysisReportVO.AssetStatisticsChart assetStatisticsChart = riskAnalysisReportVO.getAssetStatisticsChart();
            int busSystemNum = riskAnalysisReportVO.getBusSystemRiskSortChart().getXAxisList().size();
            int riskTotal = riskAnalysisReportVO.getRiskTotal();
            // TODO 先固定指标 中风险 高风险 中高风险
            int mediumRiskAssetNum = 0;
            int highRiskAssetNum = 0;
            int moreThanMediumNum = 0;
            for (ReportConfigBaseDTO dto : assetStatisticsChart.getDataList()){
                if (dto.getConfigIndicator() == 76){
                    mediumRiskAssetNum = Integer.parseInt(String.valueOf(dto.getConfigIndicatorValue()));
                }
                if (dto.getConfigIndicator() == 77){
                    highRiskAssetNum = Integer.parseInt(String.valueOf(dto.getConfigIndicatorValue()));
                }
                if (dto.getConfigIndicator() == 78){
                    moreThanMediumNum = Integer.parseInt(String.valueOf(dto.getConfigIndicatorValue()));
                }
            }

            double highRiskAssetNumProportion = BigDecimal.valueOf((double)highRiskAssetNum)
                .divide(BigDecimal.valueOf((double)riskTotal), 4, BigDecimal.ROUND_HALF_DOWN)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
            double mediumRiskAssetNumProportion = BigDecimal.valueOf((double)mediumRiskAssetNum)
                .divide(BigDecimal.valueOf((double)riskTotal), 4, BigDecimal.ROUND_HALF_DOWN)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
            double moreThanMediumNumProportion = BigDecimal.valueOf((double)moreThanMediumNum)
                .divide(BigDecimal.valueOf((double)riskTotal), 4, BigDecimal.ROUND_HALF_DOWN)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
            model.put("busSystemNum", busSystemNum);
            model.put("highRiskAssetNum", highRiskAssetNum);
            model.put("mediumRiskAssetNum", mediumRiskAssetNum);
            model.put("highRiskAssetNumProportion", highRiskAssetNumProportion);
            model.put("mediumRiskAssetNumProportion", mediumRiskAssetNumProportion);
            model.put("moreThanMediumNumProportion", moreThanMediumNumProportion);

            //*******	总体生命周期风险分析
            List<RiskAnalysisReportVO.BusSystemRiskDetailChart> busSystemRiskDetailChartList = riskAnalysisReportVO.getBusSystemRiskDetailChartList();
            Map<Double, String> map = new HashMap<>(16);
            if (CollUtil.isNotEmpty(busSystemRiskDetailChartList)) {
                Optional<RiskAnalysisReportVO.BusSystemRiskDetailChart> optional =
                    busSystemRiskDetailChartList.stream().findFirst();
                if (optional.isPresent()) {
                    RiskAnalysisReportVO.BusSystemRiskDetailChart busSystemRiskDetailChart = optional.get();
                    for (ReportConfigBaseDTO dto : busSystemRiskDetailChart.getPieChart().getPieList()) {
                        map.put(Double.parseDouble(String.valueOf(dto.getConfigIndicatorValue())), dto.getConfigName());
                    }
                }

                Double max = Collections.max(map.keySet());
                String stageName = map.get(max);
                model.put("stageName", stageName);

                // 5.3.4 业务系统风险分析
                Map<String, String> commentMap = coInventoryList.stream().collect(Collectors
                    .toMap(co -> co.getBusSystem() + co.getDataAsset(),
                        co -> Objects.isNull(co.getAssetComment()) ? StrUtil.EMPTY : co.getAssetComment(), (k1, k2) -> k1));
                List<Map<String, Object>> assetRiskContents = new ArrayList<>();

                for (RiskAnalysisReportVO.BusSystemRiskDetailChart  busSystemRiskDetailChart : busSystemRiskDetailChartList) {
                    // 曲线图-风险等级
                    RiskAnalysisReportVO.HistogramChart histogramChart = busSystemRiskDetailChart.getHistogramChart();
                    // 饼图-风险系数
                    RiskAnalysisReportVO.PieChart pieChart = busSystemRiskDetailChart.getPieChart();

                    String busSystemName = "";
                    if (histogramChart != null){
                        if ("汇总分析".equals(histogramChart.getBusSystemName())) {
                            continue;
                        }
                        busSystemName = histogramChart.getBusSystemName();
                    } else if (pieChart != null) {
                        busSystemName = pieChart.getBusSystemName();
                    }

                    Map<String, Object> relateMap = new HashMap<>(16);
                    relateMap.put("assetRelatedSystem", busSystemName);
                    Map<Double, String> map1 = new HashMap<>(16);
                    pieChart.getPieList().forEach(riskValue -> {
                        map1.put(Double.parseDouble(String.valueOf(riskValue.getConfigIndicatorValue())), riskValue.getConfigName());
                    });
                    Optional<Double> max1 = map1.keySet().stream().max(Comparator.comparingDouble(Double::doubleValue));
                    if (max1.isPresent()) {
                        String stageName1 = map1.get(max1.get());
                        relateMap.put("assetRelateStage", stageName1);
                    }

                    String format = String.format("生命周期风险分析-%s", pieChart.getBusSystemName());
                    List<ExportWordChart> chartList2 =
                        chartList.stream().filter(s -> format.equals(s.getName()) && !"生命周期风险分析-汇总分析".equals(s.getName()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(chartList2)) {
                        relateMap.put("assetRelatedRiskImg", getPictureStream(getPicture(chartList2)));
                    }
                    List<ReportAssetRiskVO> reportAssetRiskVOS = queryAssetRisk(operationId,busSystemName,commentMap, modelId);
                    AtomicInteger highSort = new AtomicInteger(0);
                    List<ReportAssetRiskVO> assetRiskHighLevelList =
                        reportAssetRiskVOS.stream().filter(r -> "高风险".equals(r.getRisk()))
                            .peek(a -> a.setSort(highSort.incrementAndGet())).collect(Collectors.toList());
                    relateMap.put("assetRiskHighLevelList",
                        assetRiskHighLevelList.size() > 100 ? assetRiskHighLevelList.subList(0, 100) :
                            assetRiskHighLevelList);
                    relateMap.put("highRiskLevelNum", assetRiskHighLevelList.size());
                    AtomicInteger mediumSort = new AtomicInteger(0);
                    List<ReportAssetRiskVO> assetRiskMediumLevelList =
                        reportAssetRiskVOS.stream().filter(r -> "中风险".equals(r.getRisk()))
                            .peek(a -> a.setSort(mediumSort.incrementAndGet())).collect(Collectors.toList());
                    relateMap.put("assetRiskMediumLevelList",
                        assetRiskMediumLevelList.size() > 100 ? assetRiskMediumLevelList.subList(0, 100) :
                            assetRiskMediumLevelList);
                    relateMap.put("mediumRiskLevelNum", assetRiskMediumLevelList.size());
                    assetRiskContents.add(relateMap);
                    model.put("assetRiskContents", assetRiskContents);
                }
            }

            // 业务系统风险排序
            RiskAnalysisReportVO.BusSystemRiskSortChart busSystemRiskSortChart = riskAnalysisReportVO.getBusSystemRiskSortChart();
            if (Objects.nonNull(busSystemRiskSortChart)) {
                OptionalDouble singSystemFactorMax = busSystemRiskSortChart.getXAxisList().stream()
                    .mapToDouble(p -> Double.parseDouble(String.valueOf(p.getConfigIndicatorValue()))).max();
                if (singSystemFactorMax.isPresent()) {
                    model.put("singSystemFactorMax", singSystemFactorMax.getAsDouble());
                }
            }

            List<ExportWordChart> chartList1 =
                chartList.stream().filter(s -> "生命周期风险分析-汇总分析".equals(s.getName())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(chartList1)) {
                model.put("assetRelatedTotalRiskImg", getPictureStream(getPicture(chartList1)));
                model.put("assetRelatedTotalRiskImgExist", true);
            } else {
                model.put("assetRelatedTotalRiskImgExist", false);
            }

            // 5.3.2 定性风险分析
            List<Map<String, Object>> dataCaptureList = new ArrayList<>();
            List<Map<String, Object>> dataTransList = new ArrayList<>();
            List<Map<String, Object>> dataStorageList = new ArrayList<>();
            List<Map<String, Object>> dataProcessList = new ArrayList<>();
            List<Map<String, Object>> dataExchangeList = new ArrayList<>();
            List<Map<String, Object>> dataDestoryList = new ArrayList<>();
            List<Map<String, Object>> commonList = new ArrayList<>();
            // 手动push知识库版本
            pushVersion(operationId);
            List<AdviseRiskDTO> adviseRisks = adviseRiskMapper.selectRiskContent(operationId);
            List<StageItemDTO> stageItems = standardItemMapper.queryStageItemIds(operationId);
            List<TreeLabelDTO> treeLabelList = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
            Map<Long, String> busSystemMap = treeLabelList.stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId,TreeLabelDTO::getTreeName));
            if (CollUtil.isNotEmpty(adviseRisks)){
                stageItems.stream().collect(Collectors.groupingBy(StageItemDTO::getSplitStage)).forEach((stage, items) -> {
                    if (SearchStageEnum.dataCollection.getInfo().equals(stage)){
                        dataCaptureList.addAll(getRiskAnalysisList(items, adviseRisks, busSystemMap));
                    } else if (SearchStageEnum.dataDestruction.getInfo().equals(stage)) {
                        dataDestoryList.addAll(getRiskAnalysisList(items, adviseRisks, busSystemMap));
                    } else if (SearchStageEnum.dataExchange.getInfo().equals(stage)) {
                        dataExchangeList.addAll(getRiskAnalysisList(items, adviseRisks, busSystemMap));
                    } else if (SearchStageEnum.dataProcessing.getInfo().equals(stage)) {
                        dataProcessList.addAll(getRiskAnalysisList(items, adviseRisks, busSystemMap));
                    } else if (SearchStageEnum.dataStorage.getInfo().equals(stage)) {
                        dataStorageList.addAll(getRiskAnalysisList(items, adviseRisks, busSystemMap));
                    } else if (SearchStageEnum.dataTransmission.getInfo().equals(stage)) {
                        dataTransList.addAll(getRiskAnalysisList(items, adviseRisks, busSystemMap));
                    } else if (SearchStageEnum.common.getInfo().equals(stage)) {
                        commonList.addAll(getRiskAnalysisList(items, adviseRisks, busSystemMap));
                    }
                });
                model.put("dataCaptureList", dataCaptureList);
                model.put("dataTransList", dataTransList);
                model.put("dataStorageList", dataStorageList);
                model.put("dataProcessList", dataProcessList);
                model.put("dataExchangeList", dataExchangeList);
                model.put("dataDestoryList", dataDestoryList);
                model.put("commonList", commonList);
            }
        }
    }

    private List<Map<String, Object>> getRiskAnalysisList(List<StageItemDTO> items, List<AdviseRiskDTO> adviseRisks,
        Map<Long, String> busSystemMap) {
        List<Map<String, Object>> result = new ArrayList<>();
        Set<String> itemIdSet = items.stream().map(StageItemDTO::getItemId).collect(Collectors.toSet());
        AtomicInteger sort = new AtomicInteger(1);
        adviseRisks.stream().filter(adviseRiskDTO -> itemIdSet.contains(adviseRiskDTO.getItemId())).forEach(dto -> {
            Map<String, Object> map = new HashMap<>();
            map.put("sort", sort.getAndIncrement());
            map.put("content", dto.getContent());
            map.put("riskLevel", dto.getRiskLevel());
            map.put("riskType", dto.getRiskType());
            map.put("describe", dto.getDescribe());
            map.put("busSystem", getBusSystem(busSystemMap, dto.getSystemId()));
            result.add(map);
        });
        return result;
    }

    private String getBusSystem(Map<Long, String> busSystemMap, String systemId) {
        String[] systemIds = systemId.split(StrPool.COMMA);
        return Arrays.stream(systemIds).map(id -> busSystemMap.get(Long.parseLong(id))).collect(Collectors.joining(StrPool.COMMA));
    }

    /**
     * 合规风险分析--比例
     *
     * @param operationId request
     * @return * @return QueryViewLegalFactorVo
     * @Date 2022/9/20 17:11
     */
    private QueryViewLegalResultVo queryLegalProportion(String operationId) {
        /**
         * -------总对标文件--------------
         * 总对标(法律)文件：lawTotalNum = distinct lawName
         * 法律：article_code包含NL 统计 distinct lawName
         * 法规：article_code包含GL、DR、LL、LR、IR、RL 统计 distinct lawName
         * 标准：article_code包含 GS、NS、IS、LS、TS 统计 distinct lawName
         *--------总对标项----------------
         * 注：按item_num分组统计
         * 总对标项：itemTotalNum = group by item_num
         * 合格率：（“完全符合项”* 1+”部分符合项“*0.5）/（”总对标项“）*100%
         * -------完全符合项--------------
         * 注：按item_num分组统计
         * 完全符合项：aNum = 分组结果 = “完全符合”
         * 完全符合项占比 = aNum / itemTotalNum *100%
         * -------部分符合项--------------
         * 注：按item_num分组统计
         * -------不符合项---------
         * 注：按item_num分组统计
         * -------不涉及项（不适用）--------
         *
         */
        QueryLegalDTO retrieveLegal = new QueryLegalDTO();
        retrieveLegal.setOperationId(operationId);
        retrieveLegal.setLabelId(LabelEnum.HFHG.getCode());
        retrieveLegal.setLawName(LegalModelEnum.ZHMB.getInfo());
        return this.getLegalResultList(retrieveLegal);
    }

    private void lawAssessment(Map<String, Object> model, ExportWordDto privator, List<Long> serviceContentList,
        String operationId, List<Map<String, Object>> fileRefList, AtomicInteger fileIndex) {
        model.put("lawAssessment", false);
        if (serviceContentList.contains(LabelEnum.HFHG.getCode()) && !privator.getNeedExcludeContent()
            .contains(LabelEnum.HFHG)) {
            model.put("lawAssessment", true);

            QueryViewLegalResultVo legalResultVo = queryLegalProportion(operationId);
            model.put("lawTotalNum", legalResultVo.getLawTotalNum());
            model.put("lawDocNum", legalResultVo.getLawDocNum());
            model.put("ruleDocNum", legalResultVo.getRuleDocNum());
            model.put("standardDocNum", legalResultVo.getStandardDocNum());
            model.put("itemTotalNum", legalResultVo.getItemTotalNum());
            model.put("qualifiedProportion", legalResultVo.getQualifiedProportion());
            model.put("countANum", legalResultVo.getCountANum());
            model.put("countBNum", legalResultVo.getCountBNum());
            model.put("countCNum", legalResultVo.getCountCNum());
            model.put("countDNum", legalResultVo.getCountDNum());
            model.put("countAProportion", legalResultVo.getCountAProportion());
            model.put("countBProportion", legalResultVo.getCountBProportion());
            model.put("countCProportion", legalResultVo.getCountCProportion());
            model.put("countDProportion", legalResultVo.getCountDProportion());

            //5.2.3	具体评估结果 5.2.3.1	{{co_legal.law_name}}评估详情
            List<Map<String, Object>> lawEvaluation = Lists.newArrayList();
            QueryWrapper<CoLegal> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.eq("operation_id", privator.getOperationId());
            List<CoLegal> coLegalList = coLegalMapper.selectList(queryWrapper2);

            Collator collator = Collator.getInstance(Locale.CHINA);
            if (CollUtil.isNotEmpty(coLegalList)) {
                List<CoSystemResult> coSystemResultList = coSystemResultMapper.selectList(
                    new QueryWrapper<CoSystemResult>().eq("operation_id", privator.getOperationId()));
                List<TreeLabelDTO> treeLabelList = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
                Map<Long, String> busSystemMap = treeLabelList.stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId,TreeLabelDTO::getTreeName));
                Map<String, String> riskDescMap = new HashMap<>(16);
                coSystemResultList.stream().collect(Collectors.groupingBy(CoSystemResult::getRelId)).forEach((k,v)->{
                    String riskDesc = getRiskDesc(v, busSystemMap);
                    if(StrUtil.isEmpty(riskDesc)){
                        return;
                    }
                    riskDescMap.put(k, riskDesc);
                });
//                Map<String, String> descMap = getLawDescribe(operationId);
                Set<String> lawType = coLegalList.stream().map(CoLegal::getLawName).collect(Collectors.toSet());
                for (String law : lawType) {
                    AtomicInteger index = new AtomicInteger(1);
                    Map<String, Object> map = new HashMap<>(lawType.size());
                    Map<String, Object> fileMap = new HashMap<>(lawType.size());
                    map.put("lawName", law);
                    List<LawReportDTO> list = coLegalList.stream()
                        .filter(l -> Objects.equals(l.getLawName(), law) && !OptEnum.D.getInfo().equals(l.getResult()))
                        .map(
                        legal -> LawReportDTO.builder()
                            .itemNum(StrUtil.sub(legal.getItemContent(), 0, 10))
                            .itemContent(legal.getItemContent()).itemExplain(legal.getItemExplain())
                            .result(legal.getResult()).remark(legal.getDesc())
                            .content(riskDescMap.get(legal.getLegalId()))
                            .build()).sorted(Comparator.comparing(
                        i -> {
                            int sort = 9999;
                            try {
                                sort = NumberChineseFormatter.chineseToNumber(StrUtil.subBetween(i.getItemNum(), "第", "条"));
                            } catch (Exception e) {
                                if (StrUtil.containsAny(i.getItemNum(), StrUtil.DOT)) {
                                    try {
                                        sort = Integer.parseInt(StrUtil.subBefore(i.getItemNum(), StrUtil.DOT, false));
                                    } catch (Exception ignore){
                                    }
                                }
                            }
                            return sort;
                        }))
                        .peek(dt -> dt.setSort(index.getAndIncrement())).collect(Collectors.toList());
                    map.put("legalList", list);
                    fileMap.put("name", law);
                    fileMap.put("sort", fileIndex.getAndIncrement());
                    lawEvaluation.add(map);
                    fileRefList.add(fileMap);
                }
            }
            model.put("lawEvaluation", lawEvaluation);
        }

        // 合规分析
        adviseRiskAnalysis(operationId, model);
    }

    private String getRiskDesc(List<CoSystemResult> v, Map<Long, String> busSystemMap) {
        StringBuilder sb = new StringBuilder();
        for (CoSystemResult coSystemResult : v){
            String aiRiskDesc = coSystemResult.getAiRiskDesc();
            String riskDesc = coSystemResult.getRiskDesc();
            if (CharSequenceUtil.isNotEmpty(aiRiskDesc) ) {
                sb.append(busSystemMap.get(coSystemResult.getSystemId())).append(":");
                if (aiRiskDesc.startsWith(StrPool.LF)) {
                    aiRiskDesc = StrUtil.replaceFirst(aiRiskDesc, StrPool.LF, "");
                }
                if (aiRiskDesc.endsWith(StrPool.LF)) {
                    aiRiskDesc = StrUtil.replaceLast(aiRiskDesc, StrPool.LF, "");
                }
                if (StrUtil.isNotEmpty(aiRiskDesc)) {
                    sb.append(aiRiskDesc).append(StrPool.LF);
                }
            } else  if (CharSequenceUtil.isNotEmpty(riskDesc) ){
                sb.append(busSystemMap.get(coSystemResult.getSystemId())).append(":");
                if (riskDesc.startsWith(StrPool.LF)) {
                    riskDesc = StrUtil.replaceFirst(riskDesc, StrPool.LF, "");
                }
                if (riskDesc.endsWith(StrPool.LF)) {
                    riskDesc = StrUtil.replaceLast(riskDesc, StrPool.LF, "");
                }
                if (StrUtil.isNotEmpty(riskDesc)) {
                    sb.append(riskDesc).append(StrPool.LF);
                }
            }
        }
        String res = sb.toString();
        if (sb.lastIndexOf(StrPool.LF) > -1){
            res = sb.deleteCharAt(sb.lastIndexOf(StrPool.LF)).toString();
        }
        return res;
    }

    private List<ReportAssetRiskVO> queryAssetRisk(String operationId, String system, Map<String, String> commentMap,
        Long modelId) {
        // 手动push知识库版本
        pushVersion(operationId);
        QueryWrapper<IndicatorResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        queryWrapper.eq("label_id", LabelEnum.SMZQ.getCode());
        queryWrapper.eq("bus_system", system);
        List<IndicatorResult> indicatorResultList = indicatorResultMapper.selectList(queryWrapper);
        QueryWrapper<CoConstant> query = new QueryWrapper<>();
        query.eq("customer_id", coOperationMapper.selectCustomIdByOperationId(operationId));
        List<FormConfig> formConfigList =
            formConfigMapper.selectList(new QueryWrapper<FormConfig>().eq("model_id", modelId));
        Map<String, List<FormConfig>> formConfigMap = formConfigList.stream()
            .filter(formConfig -> formConfig.getParentId() != -1 && formConfig.getColFormat() != null)
            .collect(Collectors.groupingBy(FormConfig::getColFormat));
        List<FormConfig> percentageFormConfigs = formConfigMap.get(ColFormatEnum.PERCENTAGE.getValue());
        Map<Integer, String> parentMap = formConfigList.stream().filter(formConfig -> formConfig.getParentId() == -1)
            .collect(Collectors.toMap(FormConfig::getColId, FormConfig::getColName));
        return indicatorResultList.stream().map(p -> {
            Map<String, Object> stepResultMap = (Map<String, Object>)JSONUtil.toBean(p.getResult(), Map.class);
            Map<Double, String> factorMap = new HashMap<>(16);
            percentageFormConfigs.forEach(formConfig -> factorMap
                .put(Double.parseDouble((String)stepResultMap.get(String.valueOf(formConfig.getColFormula()))),
                    parentMap.get(formConfig.getParentId())));
            Double max = Collections.max(factorMap.keySet());
            final String riskFactor = factorMap.get(max);
            // 585	datalifecycle_risk_level1	DSMM数据资产生命周期综合风险等级
            String riskLevel = String.valueOf(stepResultMap.get("585"));
            return ReportAssetRiskVO.builder().assetName(p.getAssertsName())
                .assetComment(commentMap.get(p.getBusSystem() + p.getAssertsName())).risk(riskLevel)
                .riskFactor(riskFactor).build();
        }).collect(Collectors.toList());
    }

    /**
     * 4.现状分析
     */
    private void currentSituationAnalysis(String operationId, Map<String, Object> model,
        List<ExportWordChart> analysisChartList) {
        //查询现状核验表获取标签页
        QueryWrapper<CoVerification> query = new QueryWrapper<>();
        query.eq("operation_id", operationId);
        List<CoVerification> coVerifications = coVerificationMapper.selectList(query);
        if (CollectionUtils.isEmpty(coVerifications)) {
            return;
        }

        // 排序
        CoVerification coVerification1 = coVerifications.get(0);
        model.put("modelName", coVerification1.getModelName());
        model.put("level", StringUtils.isNotNull(coVerification1.getLevel()) ? coVerification1.getLevel().trim() :
            coVerification1.getLevel());

        //4.1.2.1	评估结果
        List<Map<String, Object>> analysisContents = new ArrayList<>();
        List<ExportWordChart> exportWordCharts = coGapAnalysisMapper.queryRemarkByOperationId(operationId);
        Map<String, String> remarkMap = new HashMap<>();
        if (CollUtil.isNotEmpty(exportWordCharts)){
            remarkMap =
                exportWordCharts.stream().filter(exportWordChart -> StrUtil.isNotEmpty(exportWordChart.getRemark()))
                    .collect(Collectors.toMap(ExportWordChart::getName, ExportWordChart::getRemark, (k1, k2) -> k1));
        }
        for (ExportWordChart chart : analysisChartList) {
            if (chart.getName().startsWith("基础评估分析-")) {
                continue;
            }
            Map<String, Object> content = new HashMap<>();
            content.put("chartName", chart.getName());
            List<Map<String, Object>> chartImages = new ArrayList<>();
            Map<String, Object> chartMap = new HashMap<>();

            Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));

            chartMap.put("chartImage", getPictureStream(imageMap));
            chartMap.put("chartImageName", chart.getName());
            chartMap.put("chartImageEvaluate", remarkMap.get(chart.getName()) == null ? "": StrUtil.fillBefore(remarkMap.get(chart.getName()), StrPool.C_TAB, 4));
            chartImages.add(chartMap);
            content.put("chartImages", chartImages);
            analysisContents.add(content);
        }
        model.put("analysisContents", analysisContents);

        List<Map<String, Object>> verification = new ArrayList<>();
        Set<String> tabNames = new HashSet<>();
        List<String> sortList = new ArrayList<>();
        // 根据能力项分组
        coVerifications.stream().collect(Collectors.groupingBy(CoVerification::getGpDimension)).forEach((k, list)->{
            AtomicInteger sort = new AtomicInteger(1);
            Map<String, Object> map = new HashMap<>();
            sortList.add(k);
            String tabName = k;
            if (StrUtil.split(k, StrPool.C_SPACE).size() > 1) {
                tabName = StrUtil.subAfter(k, StrUtil.C_SPACE, true);
            }
            tabNames.add(tabName);
            map.put("tabName", tabName);
            map.put("tabList", coVerifications.stream().filter(v -> Objects.equals(v.getGpDimension(), k))
                .sorted(Comparator.comparing(CoVerification::getBpCode)).peek(s -> {
                    s.setSort(sort.getAndIncrement());
                    s.setDescription(s.getDesc());
                })
                .collect(Collectors.toList()));
            verification.add(map);
        });
        List<String> sorts = sortList.stream().sorted(Comparator.comparing(s -> StrUtil.subBefore(s, StrUtil.C_SPACE, true)))
            .map(s->StrUtil.subAfter(s, StrUtil.C_SPACE, true)).collect(
            Collectors.toList());
        model.put("tabNames", CollUtil.join(tabNames, "、"));
        model.put("verification", verification.stream().sorted(Comparator.comparing(v-> sorts.indexOf(v.get("tabName")))).collect(
            Collectors.toList()));
    }

}
