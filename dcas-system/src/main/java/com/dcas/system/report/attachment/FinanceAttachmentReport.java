package com.dcas.system.report.attachment;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.CoModelAnalysisResult;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.excel.WaterMarkHandler;
import com.dcas.common.mapper.CoLicenseMapper;
import com.dcas.common.mapper.CoModelAnalysisResultMapper;
import com.dcas.common.mapper.CoOperationMapper;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.model.excel.InventoryAttachmentJrExcel;
import com.dcas.common.model.excel.InventoryAttachmentJsExcel;
import com.dcas.common.model.vo.FormConfigTreeVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.system.report.ReportTypeEnum;
import com.dcas.system.report.ReportUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 江苏专供资产清单
 *
 * <AUTHOR>
 * @date 2024/01/19 16:45
 **/
@RequiredArgsConstructor
@Component
@Slf4j
public class FinanceAttachmentReport implements AttachmentReportInterface {

    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;
    private final CoLicenseMapper coLicenseMapper;

    @Value("${safety.profile}")
    private String basePath;

    @SchemaSwitch(ExportWordDto.class)
    @Override
    public String exportWord( ExportWordDto dto, QueryProjectOperationExportVo vo) throws Exception {

        String path = String.join(File.separator, basePath, "temp", "附件：数据安全风险计算值.xlsx");
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        // 附件 资产列表
        List<InventoryAttachmentJrExcel> result = queryAssetRisk(dto.getOperationId());
        EasyExcel.write(out)
            .withTemplate(new ClassPathResource("classpath://template/inventoryAttachmentJrTemplate.xlsx").getStream())
            .registerWriteHandler(WaterMarkHandler.simple(coLicenseMapper.queryCustomName()))
            .sheet().doWrite(result);
        return path;
    }

    @Override
    public void exportWord(HttpServletResponse response, ExportWordDto dto, QueryProjectOperationExportVo vo)
        throws Exception {
        String path = exportWord(dto, vo);
        ReportUtil.output(response, path);
    }

    private List<InventoryAttachmentJrExcel> queryAssetRisk(String operationId) {
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        String titlesStr = "";
        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            titlesStr = coModelAnalysisResult.getTitles();
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
        //[{"title":"所属业务系统","dataIndex":"0","dataColumn":false,"children":[]},
        // {"title":"资产信息","dataIndex":"1","dataColumn":false,"children":[{"title":"资产名称","dataIndex":"200","width":140,"dataColumn":false,"children":[]},{"title":"敏感等级","dataIndex":"201","width":140,"dataColumn":false,"children":[]}]},
        // {"title":"数据生命周期安全安全防护","dataIndex":"2","dataColumn":false,"children":[
        // {"title":"风险值","dataIndex":"300","width":140,"dataColumn":true,"indicatorId":486,"colFormat":"NUMBER","children":[]},
        // {"title":"风险等级","dataIndex":"301","width":140,"dataColumn":true,"indicatorId":489,"colFormat":"STRING","children":[]}]},
        // {"title":"数据安全组织保障","dataIndex":"3","dataColumn":false,"children":[
        // {"title":"风险值","dataIndex":"400","width":140,"dataColumn":true,"indicatorId":487,"colFormat":"NUMBER","children":[]},
        // {"title":"风险等级","dataIndex":"401","width":140,"dataColumn":true,"indicatorId":490,"colFormat":"STRING","children":[]}]},
        // {"title":"信息系统运维保障","dataIndex":"4","dataColumn":false,"children":[
        // {"title":"风险值","dataIndex":"500","width":140,"dataColumn":true,"indicatorId":488,"colFormat":"NUMBER","children":[]},
        // {"title":"风险等级","dataIndex":"501","width":140,"dataColumn":true,"indicatorId":491,"colFormat":"STRING","children":[]}]},
        // {"title":"综合风险等级","dataIndex":"5","dataColumn":false,"children":[
        // {"title":"综合风险等级","dataIndex":"600","width":140,"dataColumn":true,"indicatorId":497,"colFormat":"STRING","children":[]}]}]
        List<String> indexList = titles.stream().flatMap(formConfigTreeVO -> formConfigTreeVO.getChildren().stream()).map(
            FormConfigTreeVO::getDataIndex).collect(Collectors.toList());
        indexList.addAll(titles.stream().map(FormConfigTreeVO::getDataIndex).collect(Collectors.toList()));
        AtomicInteger sortedIndex = new AtomicInteger(1);
        return resultList.stream().map(map -> InventoryAttachmentJrExcel.builder()
                .sort(sortedIndex.getAndIncrement())
            .busSystem(MapUtil.getStr(map, indexList.get(9)))
            .assetName(MapUtil.getStr(map, indexList.get(0)))
            .sensitiveLevel(MapUtil.getStr(map, indexList.get(1)))
            .lifecycleRiskValue(MapUtil.getStr(map, indexList.get(2)))
            .lifecycleRiskLevel(MapUtil.getStr(map, indexList.get(3)))
            .dataSafetyRiskValue(MapUtil.getStr(map, indexList.get(4)))
            .dataSafetyRiskLevel(MapUtil.getStr(map, indexList.get(5)))
            .maintenanceRiskValue(MapUtil.getStr(map, indexList.get(6)))
            .maintenanceRiskLevel(MapUtil.getStr(map, indexList.get(7)))
            .totalRiskLevel(MapUtil.getStr(map, indexList.get(8)))
            .build()).collect(Collectors.toList());
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.FINANCE;
    }
}
