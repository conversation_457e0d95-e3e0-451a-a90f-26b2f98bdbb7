package com.dcas.system.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.DataScope;
import com.dcas.common.core.domain.BaseEntity;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.SecurityContentType;
import com.dcas.common.enums.WorkPermissionType;
import com.dcas.common.model.dto.SSOAccountDTO;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.model.dto.QueryOperationOnGoingViewDto;
import com.dcas.common.model.dto.SecurityServiceContent;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.vo.ProjectWorkVO;
import com.dcas.common.model.vo.QueryOperationOnGoingView;
import com.dcas.common.model.vo.QueryOperationStatusVo;
import com.dcas.common.model.vo.QueryOperationViewInsider;
import com.dcas.common.mapper.*;
import com.dcas.system.service.CoProjectService;
import com.dcas.system.service.CoWorkbenchViewService;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工作台视图实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CoWorkbenchViewServiceImpl implements CoWorkbenchViewService {

    private final CoProjectMapper coProjectMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoProgressMapper coProgressMapper;
    private final CoProcessTreeMapper coProcessTreeMapper;
    private final SecurityOperationMapper securityOperationMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;

    private final CoProjectService coProjectService;

    /**
     * 查询作业状态
     */
    @Override
    @DataScope(deptAlias = "t", userAlias = "t", extra = "job")
    public QueryOperationStatusVo retrieveStatus(QueryOperationOnGoingViewDto dto) {
        QueryOperationStatusVo vo = new QueryOperationStatusVo();
        SSOAccountDTO user = SecurityUtils.getLoginUser();
        List<ProjectWorkVO> projectWorks = coProjectMapper.userWorks(dto).stream().filter(p ->
                Objects.nonNull(p.getStatus())).collect(Collectors.toList());
        // 评估作业
        List<ProjectWorkVO> accessWorks = projectWorks.stream().filter(p -> p.getType() == 1).collect(Collectors.toList());
        // 检查作业
        List<ProjectWorkVO> reviewWorks = projectWorks.stream().filter(p -> p.getType() == 2).collect(Collectors.toList());
        vo.setAccess(buildAccessWork(accessWorks, user));
        vo.setReview(buildReviewWork(reviewWorks, user));
        vo.setCreateNum(vo.getAccess().getCreate() + vo.getReview().getCreate());
        vo.setCompleteNum(vo.getAccess().getCompleted() + vo.getReview().getCompleted());
        return vo;
    }

    /**
     * 创建作业数量   ：作业创建人员 为当前登录用户
     * 执行中作业数量 ：状态为执行中 且 作业创建人员 或者 作业执行人员 为当前登录用户
     * 复核中作业数量 ：状态为复核中 且 作业创建人员 或者 作业复核人员 为当前登录用户
     * 已结项作业数量 ：状态为已结项 且 作业创建人员 为当前登录用户
     */
    QueryOperationStatusVo.WorkOverview buildAccessWork(List<ProjectWorkVO> accessWorks, SSOAccountDTO user) {
        QueryOperationStatusVo.WorkOverview access = new QueryOperationStatusVo.WorkOverview();
        access.setName("评估作业");
        List<ProjectWorkVO> createWorks = accessWorks.stream().filter(w -> coProjectService.hasPermission(user, w, WorkPermissionType.CREATE)).collect(Collectors.toList());
        access.setCreate(createWorks.size());
        access.setOnGoing((int) accessWorks.stream().filter(w -> w.getStatus() == 1).count());
        access.setOnReview((int) accessWorks.stream().filter(w -> w.getStatus() == 2).count());
        access.setCompleted((int) accessWorks.stream().filter(w -> w.getStatus() == 3).count());
        return access;
    }

    QueryOperationStatusVo.WorkOverview buildReviewWork(List<ProjectWorkVO> reviewWorks, SSOAccountDTO user) {
        QueryOperationStatusVo.WorkOverview review = new QueryOperationStatusVo.WorkOverview();
        review.setName("检查作业");
        List<ProjectWorkVO> createWorks = reviewWorks.stream().filter(w -> coProjectService.hasPermission(user, w, WorkPermissionType.CREATE)).collect(Collectors.toList());
        review.setCreate(createWorks.size());
        review.setOnGoing((int) reviewWorks.stream().filter(w -> w.getStatus() == 2).count());
        review.setOnReview(null);
        review.setCompleted((int) reviewWorks.stream().filter(w -> w.getStatus() == 3).count());
        return review;
    }

    /**
     * 查询执行中作业列表
     */
    @Override
    @DataScope(deptAlias = "t", userAlias = "t", extra = "job")
    public List<QueryOperationOnGoingView> selectOperation(RequestModel<QueryOperationOnGoingViewDto> dto) {
        List<ProjectWorkVO> projectWorks = coProjectMapper.userWorks(dto.getPrivator());
        return buildOperationView(projectWorks, true);
    }

    /**
     * @param working 是否只针对执行中的作业
     */
    List<QueryOperationOnGoingView> buildOperationView(List<ProjectWorkVO> projectWorks, boolean working) {
        Map<Long, CoProcessTree> oldTreeMap = coProcessTreeMapper.selectList(new QueryWrapper<>())
                .stream().collect(Collectors.toMap(CoProcessTree::getTreeId, Function.identity()));
        return projectWorks.stream().filter(p -> !working || (p.getType() == 1 && p.getStatus() == 1 &&
                        p.getProgress() != 100) || (p.getType() == 2 && p.getStatus() == 2 && p.getProgress() != 100))
                .map(p -> QueryOperationOnGoingView.builder()
                .operationId(p.getId())
                .operationName(p.getName())
                .projectName(p.getProjectName())
                .projectManager(p.getProjectManager())
                .progress(p.getProgress().toString())
                .type(p.getType())
                .status(p.getStatus())
                .createBy(p.getCreateBy())
                .createTime(p.getCreateTime())
                .executor(p.getExecutor())
                .executorAccount(p.getExecutorAccount())
                .serviceContentList(p.getType() == 1 ? buildAccessServiceContent(p, oldTreeMap) : buildReviewServiceContent(p))
                .build()).collect(Collectors.toList());
    }

    private List<QueryOperationViewInsider> buildAccessServiceContent(ProjectWorkVO projectWork, Map<Long, CoProcessTree> oldTreeMap) {
        QueryWrapper<DynamicProcessTree> dynamicProcessTreeQueryWrapper = new QueryWrapper<>();
        dynamicProcessTreeQueryWrapper.eq("operation_id", projectWork.getId());
        Map<Long, DynamicProcessTree> newTreeMap = dynamicProcessTreeMapper.selectList(dynamicProcessTreeQueryWrapper)
                .stream().collect(Collectors.toMap(DynamicProcessTree::getTreeId, Function.identity()));
        final boolean newWorkFlag = !newTreeMap.isEmpty();
        QueryWrapper<CoProgress> query = new QueryWrapper<>();
        query.eq("operation_id", projectWork.getId());
        Set<Long> processSet = coProgressMapper.selectList(query).stream().map(CoProgress::getLabelId).collect(Collectors.toSet());
        if (newWorkFlag) {
            List<DynamicProcessTree> processTrees = StrUtil.split(projectWork.getServiceContent(), StrUtil.COMMA)
                    .stream().mapToLong(Long::parseLong).boxed().sorted().map(newTreeMap::get).collect(Collectors.toList());
            return buildNewServiceContent(processTrees, processSet);
        } else {
            List<CoProcessTree> processTrees = StrUtil.split(projectWork.getServiceContent(), StrUtil.COMMA)
                    .stream().mapToLong(Long::parseLong).boxed().sorted().map(oldTreeMap::get).collect(Collectors.toList());
            return buildOldServiceContent(processTrees, processSet);
        }
    }

    private List<QueryOperationViewInsider> buildNewServiceContent(List<DynamicProcessTree> processTrees, Set<Long> processSet) {
        // 一级节点 eg: 问卷调研、资产梳理、安全现状、安全评估、安全建议
        List<DynamicProcessTree> parents = processTrees.stream().filter(t -> t!=null && t.getParentId() == 100L).collect(Collectors.toList());
        return parents.stream().map(p -> {
            List<DynamicProcessTree> children = processTrees.stream().filter(t -> t != null && p != null && Objects.equals(t.getParentId(), p.getTreeId()))
                    .sorted(Comparator.comparing(DynamicProcessTree::getOrderNum)).collect(Collectors.toList());
            List<QueryOperationViewInsider> insiders = buildChildrenInside(children, processSet);
            return QueryOperationViewInsider.builder()
                    .name(p.getTreeName())
                    .completed(insiders.stream().allMatch(QueryOperationViewInsider::getCompleted))
                    .children(insiders).build();
        }).collect(Collectors.toList());
    }

    private List<QueryOperationViewInsider> buildOldServiceContent(List<CoProcessTree> processTrees, Set<Long> processSet) {
// 一级节点 eg: 问卷调研、资产梳理、安全现状、安全评估、安全建议
        List<CoProcessTree> parents = processTrees.stream().filter(t -> t.getParentId() == 100L).collect(Collectors.toList());
        return parents.stream().map(p -> {
            List<CoProcessTree> children = processTrees.stream().filter(t -> Objects.equals(t.getParentId(), p.getTreeId())).collect(Collectors.toList());
            List<QueryOperationViewInsider> insiders = buildChildrenInside(children, processSet);
            return QueryOperationViewInsider.builder()
                    .name(p.getTreeName())
                    .completed(insiders.stream().allMatch(QueryOperationViewInsider::getCompleted))
                    .children(insiders).build();
        }).collect(Collectors.toList());
    }

    private List<QueryOperationViewInsider> buildChildrenInside(List<?> children, Set<Long> processSet) {
        return children.stream().map(c -> {
            if (c instanceof DynamicProcessTree) {
                return QueryOperationViewInsider.builder().name(((DynamicProcessTree) c).getTreeName()).completed(processSet.contains(((DynamicProcessTree) c).getTreeId())).build();
            } else {
                return QueryOperationViewInsider.builder().name(((CoProcessTree) c).getTreeName()).completed(processSet.contains(((CoProcessTree) c).getTreeId())).build();
            }
        }).collect(Collectors.toList());
    }

    private List<QueryOperationViewInsider> buildReviewServiceContent(ProjectWorkVO projectWork) {
        List<QueryOperationViewInsider> res = new ArrayList<>();
        List<SecurityServiceContent> contents = securityOperationMapper.selectServiceBySecurityId(Integer.parseInt(projectWork.getId()));
        Map<String, List<SecurityServiceContent>> modelMap = contents.stream().collect(Collectors.groupingBy(SecurityServiceContent::getModel));
        modelMap.forEach((k, children) -> {
            List<QueryOperationViewInsider> insiders = children.stream().collect(Collectors.groupingBy(SecurityServiceContent::getCategory))
                    .entrySet().stream().map(e -> QueryOperationViewInsider.builder()
                            .name(e.getKey())
                            .completed(e.getValue().stream().allMatch(SecurityServiceContent::getFinished))
                            .build())
                    .collect(Collectors.toList());
            res.add(QueryOperationViewInsider.builder()
                    .name(k)
                    .completed(insiders.stream().allMatch(QueryOperationViewInsider::getCompleted))
                    .children(insiders)
                    .build());
        });
        List<String> sortBy = Lists.newArrayList(SecurityContentType.SECURITY.getName(), SecurityContentType.SENSITIVE.getName());
        return res.stream().sorted(Comparator.comparing(o -> sortBy.indexOf(o.getName()))).collect(Collectors.toList());
    }

    /**
     * 查询待复核作业列表
     *
     */
    @Override
    @DataScope(deptAlias = "a", userAlias = "a", extra = "job-review")
    public List<QueryOperationOnGoingView> retrieveWaitReview(RequestModel<QueryOperationOnGoingViewDto> dto) {
        return coOperationMapper.queryOperationReviewing(dto.getPrivator());
    }

    /**
     * 作业分布图表
     */
    @Override
    @DataScope(deptAlias = "a", userAlias = "a", extra = "job-executor")
    public Map<String, Map<String, Double>> retrieveRelated(BaseEntity entity) {
        // 评估作业
        List<CoOperation> coOperationList = coOperationMapper.selectOperationList(entity);

        // 检查作业
        List<SecurityOperation> securityOperations = securityOperationMapper.selectOperationList(entity);

        //行业分布
        List<String> industryList = getList(coOperationList, CoOperation::getRelatedIndustry, securityOperations, SecurityOperation::getIndustry);

        //区域分布
        List<String> districtList = getList(coOperationList, CoOperation::getRelatedDistrict, securityOperations, SecurityOperation::getRegion);

       return ImmutableMap.of("行业分布", getFactor(industryList), "区域分布", getFactor(districtList));
    }

    private List<String> getList(List<CoOperation> coOperationList, Function<CoOperation, String> fun1,
                                 List<SecurityOperation> securityOperations, Function<SecurityOperation, String> fun2) {
        List<String> list = coOperationList.stream().map(fun1).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        list.addAll(securityOperations.stream().map(fun2).filter(StrUtil::isNotEmpty).collect(Collectors.toList()));
        return list;
    }

    /**
     * 元素占比
     */
    public Map<String, Double> getFactor(List<String> list) {
        List<String> result = list.stream()
                .flatMap(input -> Arrays.stream(input.split(";")))
                .flatMap(segment -> Arrays.stream(segment.split(StrUtil.COMMA)))
                .collect(Collectors.toList());
        final double size = result.size();
        Map<String, Double> map = result.stream().collect(Collectors.groupingBy(s -> s,
                Collectors.collectingAndThen(Collectors.counting(), count -> NumberUtil.div((double) count * 100, size, 2))));
        return map.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }
}
