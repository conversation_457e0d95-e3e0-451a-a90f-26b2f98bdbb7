package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.PathUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.CoLicenseMapper;
import com.dcas.common.model.dto.SecurityRuleDTO;
import com.dcas.common.model.other.SecurityRulePipeline;
import com.dcas.common.model.vo.LabelVO;
import com.dcas.common.utils.*;
import com.dcas.common.utils.file.FileUtils;
import com.dcas.common.domain.entity.SourceConfig;
import com.dcas.common.model.dto.DetectionResultUpdateDTO;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.domain.entity.DetectionResult;
import com.dcas.common.domain.entity.SecurityRules;
import com.dcas.common.model.excel.DetectionResultExcel;
import com.dcas.common.model.excel.RiskDetectionExcel;
import com.dcas.common.model.other.OptionSelect;
import com.dcas.common.model.param.DetectionQueryParam;
import com.dcas.common.model.vo.DetectionReportVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.common.utils.poi.DocxUtil;
import com.dcas.system.listener.DetectionResultListener;
import com.dcas.common.mapper.CoOperationMapper;
import com.dcas.common.mapper.DetectionResultMapper;
import com.dcas.common.mapper.SecurityRulesMapper;
import com.dcas.system.service.DetectionResultService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.mchz.mcdatasource.core.DatasourceConstant;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/10/12 15:58
 * @since 1.5.0
 */
@Service
@RequiredArgsConstructor
public class DetectionResultServiceImpl extends ServiceImpl<DetectionResultMapper, DetectionResult> implements DetectionResultService {

    private final SecurityRulesMapper securityRulesMapper;
    private final CoLicenseMapper coLicenseMapper;
    private final CoOperationMapper coOperationMapper;

    @Override
    public PageResult<DetectionResult> pageQuery(DetectionQueryParam param) {
        try (Page<Object> page = PageHelper.startPage(param.getCurrentPage(), param.getPageSize())) {
            List<DetectionResult> list = baseMapper.pageQuery(param);
            list.forEach(detectionResult -> {
                if (detectionResult.getDbConfig() == null){
                    detectionResult.setDbConfig(null);
                    return;
                }
                SourceConfig sourceConfig = JSON.parseObject(detectionResult.getDbConfig(),SourceConfig.class);
                detectionResult.setDbConfig(sourceConfig.getDbConfig(false));
                detectionResult.setConfigName(sourceConfig.getConfigName());
            });
            return new PageResult<>(page.getTotal(), list);
        }
    }

    @Override
    public void update(DetectionResultUpdateDTO dto) {
        DetectionResult bean = BeanUtil.copyProperties(dto, DetectionResult.class);
        baseMapper.updateById(bean);
    }

    @Override
    public Boolean canFinish(String operationId) {
        return baseMapper.canFinish(operationId) == 0;
    }

    @Override
    @SchemaSwitch
    public List<OptionSelect<Integer>> queryDbTemplate() {
        return baseMapper.queryDbType().stream().map(b -> {
            OptionSelect<Integer> option = new OptionSelect<>();
            option.setLabel(DataSourceType.getType(b).getName());
            option.setValue(b);
            return option;
        }).collect(Collectors.toList());
    }

    @Override
    @SneakyThrows
    @SchemaSwitch
    public void templateDownload(Integer dbType, HttpServletResponse response) {
        List<RiskDetectionExcel> excels = securityRulesMapper.queryTemplateByDbType(dbType).stream().peek(d ->
                d.setDbType(DataSourceType.getType(Long.parseLong(d.getDbType())).name())).collect(Collectors.toList());
        Func.responseSetting(response, String.format("数据库风险检测模板-%s.xlsx", excels.get(0).getDbType()));
        EasyExcel.write(response.getOutputStream()).withTemplate(new ClassPathResource("classpath://template/riskTemplate.xlsx").getStream()).sheet().doWrite(excels);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Throwable.class)
    @SchemaSwitch(value = String.class)
    public void templateImport(MultipartFile file, String operationId) {
        Map<Integer, Map<String, SecurityRuleDTO>> ruleGroup = securityRulesMapper.queryRuleDTO().stream()
                .collect(Collectors.groupingBy(
                        SecurityRuleDTO::getDbType, // 按 dbType 分组
                        Collectors.toMap(
                                rule -> rule.getOption() + rule.getContent(), // 生成 key: option + content
                                rule -> rule // 生成 value: 当前 bean
                        )
                ));
        baseMapper.delete(new QueryWrapper<DetectionResult>().eq("operation_id", operationId));
        EasyExcel.read(file.getInputStream(), RiskDetectionExcel.class, new DetectionResultListener(this, operationId, ruleGroup)).sheet().doRead();
    }

    @Override
    @SchemaSwitch
    public String getDefaultSuggest(Integer ruleId) {
        SecurityRules securityRules = securityRulesMapper.selectById(ruleId);
        return securityRules == null ? "" : securityRules.getUnqualified();
    }

    @Override
    public DetectionReportVO summary(String operationId) {
        DetectionReportVO reportVO = new DetectionReportVO();
        List<DetectionResult> list =
            baseMapper.selectList(new QueryWrapper<DetectionResult>().eq("operation_id", operationId));
        if (CollectionUtils.isEmpty(list)) {
            return reportVO;
        }
        Map<String, List<DetectionResult>> groupDbMap =
            list.stream().filter(detectionResult -> StrUtil.isNotEmpty(detectionResult.getDbConfig()))
                .collect(Collectors.groupingBy(DetectionResult::getDbConfig));
        List<DetectionReportVO.DbAccord> dbAccords = new ArrayList<>();
        List<DetectionReportVO.DbSourceStat> dbSourceStats = new ArrayList<>();

        if (groupDbMap.size() > 1) {
            reportVO.setOneMore(true);
            int allAccordTotalCount = 0;
            int allNoAccordTotalCount = 0;
            for (Map.Entry<String, List<DetectionResult>> entry : groupDbMap.entrySet()) {
                List<DetectionReportVO.DbAccordStat> dbAccordStats = new ArrayList<>();
                AtomicInteger accordTotalCount = new AtomicInteger(0);
                AtomicInteger noAccordTotalCount = new AtomicInteger(0);
                entry.getValue().stream().collect(Collectors.groupingBy(DetectionResult::getOption)).forEach((o,v)->{
                    DetectionReportVO.DbAccordStat dbAccordStat = new DetectionReportVO.DbAccordStat();
                    dbAccordStat.setName(o);
                    dbAccordStat.setAccordCount(Math.toIntExact(v.stream()
                        .filter(detectionResult -> detectionResult.getAccord() != null && detectionResult.getAccord())
                        .count()));
                    dbAccordStat.setTotal(v.size());
                    dbAccordStats.add(dbAccordStat);
                    accordTotalCount.addAndGet(Math.toIntExact(dbAccordStat.getAccordCount()));
                    noAccordTotalCount.addAndGet( v.size() - dbAccordStat.getAccordCount());
                });
                // 组装饼图符合数量汇总
                DetectionReportVO.DbAccordCountStat dbAccordCountStat = new DetectionReportVO.DbAccordCountStat();
                dbAccordCountStat.setAccordCount(accordTotalCount.get());
                dbAccordCountStat.setNoAccordCount(noAccordTotalCount.get());
                dbAccordCountStat.setTotalCount(dbAccordCountStat.getAccordCount() + dbAccordCountStat.getNoAccordCount());
                BigDecimal rate =
                    BigDecimal.valueOf(accordTotalCount.get()).divide(BigDecimal.valueOf(dbAccordCountStat.getTotalCount()), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                dbAccordCountStat.setAccordRate(rate.toString());
                allAccordTotalCount += accordTotalCount.get();
                allNoAccordTotalCount += noAccordTotalCount.get();

                SourceConfig sourceConfig = JSON.parseObject(entry.getKey(), SourceConfig.class);
                // 组装数据源符合情况
                DetectionReportVO.DbSourceStat dbSourceStat = new DetectionReportVO.DbSourceStat();
                dbSourceStat.setDbAccordCountStat(dbAccordCountStat);
                dbSourceStat.setDbAccordStats(dbAccordStats);
                dbSourceStat.setDbName(sourceConfig.getConfigName());
                dbSourceStats.add(dbSourceStat);

                // 汇总数据源符合率
                DetectionReportVO.DbAccord dbAccord = new DetectionReportVO.DbAccord();
                dbAccord.setName(sourceConfig.getConfigName());
                dbAccord.setAccordRate(rate.toString());
                dbAccords.add(dbAccord);
            }
            // 组装饼图符合数量汇总
            DetectionReportVO.DbAccordCountStat dbAccordCountStat = new DetectionReportVO.DbAccordCountStat();
            dbAccordCountStat.setAccordCount(allAccordTotalCount);
            dbAccordCountStat.setNoAccordCount(allNoAccordTotalCount);
            dbAccordCountStat.setTotalCount(allAccordTotalCount + allNoAccordTotalCount);
            BigDecimal rate =
                BigDecimal.valueOf(allAccordTotalCount).divide(BigDecimal.valueOf(dbAccordCountStat.getTotalCount()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            dbAccordCountStat.setAccordRate(rate.toString());

            reportVO.setDbAccords(dbAccords);
            reportVO.setDbSourceStats(dbSourceStats);
            reportVO.setDbAccordCountStat(dbAccordCountStat);
        } else {
            reportVO.setOneMore(false);
            SourceConfig sourceConfig = null;
            for (Map.Entry<String, List<DetectionResult>> entry : groupDbMap.entrySet()) {
                sourceConfig = JSON.parseObject(entry.getKey(), SourceConfig.class);
                List<DetectionReportVO.DbAccordStat> dbAccordStats = new ArrayList<>();
                AtomicInteger accordTotalCount = new AtomicInteger(0);
                AtomicInteger noAccordTotalCount = new AtomicInteger(0);
                entry.getValue().stream().collect(Collectors.groupingBy(DetectionResult::getOption)).forEach((o,v)->{
                    DetectionReportVO.DbAccordStat dbAccordStat = new DetectionReportVO.DbAccordStat();
                    dbAccordStat.setName(o);
                    dbAccordStat.setAccordCount(Math.toIntExact(v.stream()
                        .filter(detectionResult -> detectionResult.getAccord() != null && detectionResult.getAccord())
                        .count()));
                    dbAccordStat.setTotal(v.size());
                    dbAccordStats.add(dbAccordStat);
                    accordTotalCount.addAndGet(Math.toIntExact(dbAccordStat.getAccordCount()));
                    noAccordTotalCount.addAndGet( v.size() - dbAccordStat.getAccordCount());
                });

                // 组装饼图符合数量汇总
                DetectionReportVO.DbAccordCountStat dbAccordCountStat = new DetectionReportVO.DbAccordCountStat();
                dbAccordCountStat.setAccordCount(accordTotalCount.get());
                dbAccordCountStat.setNoAccordCount(noAccordTotalCount.get());
                dbAccordCountStat.setTotalCount(dbAccordCountStat.getAccordCount() + dbAccordCountStat.getNoAccordCount());
                BigDecimal rate =
                    BigDecimal.valueOf(accordTotalCount.get()).divide(BigDecimal.valueOf(dbAccordCountStat.getTotalCount()), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                dbAccordCountStat.setAccordRate(rate.toString());

                // 汇总数据源符合率
                DetectionReportVO.DbAccord dbAccord = new DetectionReportVO.DbAccord();
                dbAccord.setName(sourceConfig.getConfigName());
                dbAccord.setAccordRate(rate.toString());
                dbAccords.add(dbAccord);

                // 组装数据源汇总信息
                DetectionReportVO.DbSourceStat dbSourceStat = new DetectionReportVO.DbSourceStat();
                dbSourceStat.setDbAccordCountStat(dbAccordCountStat);
                dbSourceStat.setDbAccordStats(dbAccordStats);
                dbSourceStat.setDbName(sourceConfig.getConfigName());
                dbSourceStats.add(dbSourceStat);
                reportVO.setDbSourceStats(dbSourceStats);
                reportVO.setDbAccords(dbAccords);
                reportVO.setDbAccordCountStat(dbAccordCountStat);
            }
        }
        return reportVO;
    }

    @Override
    public void clear(String operationId) {
        baseMapper.delete(new QueryWrapper<DetectionResult>().eq("operation_id", operationId));
    }

    @Override
    public void export(HttpServletResponse response, String operationId) throws IOException {
        List<DetectionResult> detectionResults = baseMapper.selectList(new QueryWrapper<DetectionResult>().eq("operation_id", operationId));
        Func.responseSetting(response, "基础评估风险检测结果.xlsx");
        EasyExcel.write(response.getOutputStream()).withTemplate(new ClassPathResource("classpath://template/detectionResult.xlsx").getStream()).sheet().doWrite(() -> detectionResults.stream().map(result -> {
            DetectionResultExcel excel = new DetectionResultExcel();
            excel.setDataSource(result.getDbConfig());
            excel.setDatBaseType(DataSourceType.getType(result.getDbType()).getName());
            excel.setContent(StrUtil.join("-", result.getOption(), result.getContent()));
            excel.setDescribe(result.getDescribe());
            excel.setResult(getResult(result.getAccord()));
            excel.setSuggest(result.getUnqualified());
            return excel;
        }).collect(Collectors.toList()));
    }

    private String getResult(Boolean accord) {
        if (Objects.isNull(accord)) {
            return "人工校验";
        }
        return accord ? "符合" : "不符合";
    }

    @Override
    public void exportWordReport(HttpServletResponse response,  RequestModel<ExportWordDto> dto) throws IOException {

        List<DetectionResult> detectionResults = baseMapper
            .selectList(new QueryWrapper<DetectionResult>().eq("operation_id", dto.getPrivator().getOperationId()));
        if (CollUtil.isEmpty(detectionResults)){
            return;
        }
        //获取模板地址，注意k8s无法识别中文文件名，中文文件失效
        org.springframework.core.io.ClassPathResource
            classPathResource = new org.springframework.core.io.ClassPathResource(
            "template/DetectionReportExportTemplate.docx");
        InputStream inputStream = classPathResource.getInputStream();

        //数据模型
        Map<String, Object> model = new HashMap<>();

        //1、2阶段查询
        QueryProjectOperationExportVo poVo = coOperationMapper.queryOperationExport(dto.getPrivator().getOperationId());

        //文本
        model.put("reportCompany", "杭州美创科技股份有限公司");
        model.put("busSystem", StringUtils.replace(poVo.getRelatedSystem(),",","、"));
        model.put("projectName", poVo.getOperationName());
        model.put("date", LocalDate.now());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        model.put("completedDate", ObjectUtils.isEmpty(poVo.getCompletedDate()) ? null : simpleDateFormat.format(poVo.getCompletedDate()));

        Set<String> dbTypeSet = new HashSet<>();
        Map<String, List<DetectionResult>> dbConfigMap = new HashMap<>(16);
        int successCount = 0;
        int failCount = 0;
        for (DetectionResult detectionResult : detectionResults){
            dbTypeSet.add(DataSourceType.getType(detectionResult.getDbType()).getName());
            if (dbConfigMap.containsKey(detectionResult.getDbConfig())) {
                dbConfigMap.get(detectionResult.getDbConfig()).add(detectionResult);
            } else {
                List<DetectionResult> list = new ArrayList<>();
                list.add(detectionResult);
                dbConfigMap.put(detectionResult.getDbConfig(), list);
            }
            if (Boolean.TRUE.equals(detectionResult.getAccord())){
                successCount++;
            } else {
                failCount++;
            }
        }

        model.put("dbTypeStr", String.join("、", dbTypeSet));
        model.put("dbCount", dbConfigMap.size());
        model.put("totalCount", detectionResults.size());
        model.put("successCount", successCount);
        model.put("failCount", failCount);
        BigDecimal rate = BigDecimal.valueOf(successCount)
            .divide(BigDecimal.valueOf(detectionResults.size()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        model.put("successRate", rate.setScale(2, RoundingMode.HALF_UP));


        //整体检测记录
        List<Map<String, Object>> detectionRecordList = new ArrayList<>();
        List<String> projectManagerList = StrUtil.split(poVo.getProjectManager(), StrUtil.C_COMMA);
        List<String> executorList = StrUtil.split(poVo.getExecutorAccount(), StrUtil.C_COMMA);
        List<String> reviewerList = StrUtil.split(poVo.getReviewerAccount(), StrUtil.C_COMMA);
        model.put("executor", String.join("、", CollUtil.unionDistinct(projectManagerList, executorList, reviewerList)));
        model.put("createTime", simpleDateFormat.format(poVo.getCreateTime()));


        // 数据库检测详情
        List<Map<String, Object>> dbSourceList = new ArrayList<>();

        AtomicInteger sort = new AtomicInteger();
        dbConfigMap.forEach((k,v)->{
            Map<String, Object> recordMap = new HashMap<>(16);
            DetectionResult detectionResult = v.get(0);

            SourceConfig sourceConfig = JSON.parseObject(detectionResult.getDbConfig(), SourceConfig.class);
            String dbType = DataSourceType.getType(v.get(0).getDbType()).getName();
            long count = v.stream().filter(DetectionResult::getAccord).count();
            int total = v.size();
            recordMap.put("sort", sort.incrementAndGet());
            recordMap.put("dbName", sourceConfig == null ? null : sourceConfig.getConfigName());
            recordMap.put("dbType", dbType);
            recordMap.put("pointCount", total);
            BigDecimal pointRate = BigDecimal.valueOf(count).divide(BigDecimal.valueOf(total), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            recordMap.put("pointRate", pointRate.setScale(2,RoundingMode.HALF_UP));
            detectionRecordList.add(recordMap);

            Map<String, Object> dbSourceInfo = new HashMap<>(16);
            dbSourceInfo.put("dbSourceName", sourceConfig == null ? null : sourceConfig.getConfigName());
            dbSourceInfo.put("dbType", DataSourceType.getType(detectionResult.getDbType()).getName());
            dbSourceInfo.put("ip", sourceConfig == null ? null : sourceConfig.getHost());
            dbSourceInfo.put("port", sourceConfig == null ? null : sourceConfig.getPort());
            dbSourceInfo.put("dbName", sourceConfig == null ? null : sourceConfig.getDbName());
            dbSourceInfo.put("total", total);
            dbSourceInfo.put("sCount", count);
            dbSourceInfo.put("fCount", total-count);
            dbSourceInfo.put("rate", recordMap.get("pointRate"));

            // 检查项详情
            List<Map<String, Object>> detectionDetails = new ArrayList<>();
            Map<String, List<DetectionResult>> optionMap =
                v.stream().filter(result -> StrUtil.isNotEmpty(result.getOption()))
                    .collect(Collectors.groupingBy(DetectionResult::getOption));
            optionMap.forEach((optionName, pointList)->{
                Map<String, Object> optionInfo = new HashMap<>(16);
                optionInfo.put("detectionName", optionName);
                // 检查点详情
                List<Map<String, Object>> detectionPointDetails = new ArrayList<>();
                pointList.forEach(point -> {
                    Map<String, Object> pointInfo = new HashMap<>(16);
                    pointInfo.put("detectionPoint", point.getContent());
                    pointInfo.put("detectionResult", point.getAccord() ? "符合" : "不符合");
                    pointInfo.put("dbType", DataSourceType.getType(point.getDbType()).getName());
                    pointInfo.put("desc", point.getDescribe());
                    if (StrUtil.isNotEmpty(point.getResult())) {
                        JSONObject jsonObject = JSON.parseObject(point.getResult());
                        JSONArray jsonArray = jsonObject.getJSONArray("data");
                        StringBuilder sb = new StringBuilder();
                        jsonArray.forEach(o -> {
                            ((JSONObject)o)
                                .forEach((key, value) -> sb.append(key).append(":").append(value).append("\n"));
                        });
                        pointInfo.put("result", sb.length() > 0 ? sb.deleteCharAt(sb.lastIndexOf("\n")): sb);
                    }
                    // 不符合的情况下需展示建议措施
                    if (!point.getAccord()) {
                        pointInfo.put("suggest", point.getUnqualified());
                    }
                    detectionPointDetails.add(pointInfo);
                });
                optionInfo.put("detectionPointDetails", detectionPointDetails);
                detectionDetails.add(optionInfo);
            });
            dbSourceInfo.put("detectionDetails", detectionDetails);
            dbSourceList.add(dbSourceInfo);
        });
        model.put("detectionRecordList", detectionRecordList);
        model.put("dbSourceList", dbSourceList);

        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder().bind("detectionRecordList", policy)
            .build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        XWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        // 基础评估报告添加水印和页眉
        String customName = coLicenseMapper.queryCustomName();
        DocxUtil.setWordWaterMark(xwpfDocument, customName, DocxUtil.DEFAULT_FONT_COLOR);
        DocxUtil.modifyPageHeader(xwpfDocument, customName);

        String realFileName = String.format("%s基础评估报告书.docx", poVo.getOperationName());
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        FileUtils.setAttachmentResponseHeader(response, realFileName);

        //输出结果
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);

    }

    @SchemaSwitch
    @Override
    public void downloadOfflineTool(HttpServletResponse response) {
        // 获取用户目录（在某些情况下可以作为当前工作目录）
        String userDir = System.getProperty("user.dir");

        File checkDir = FileUtil.file(CharSequenceUtil.join(File.separator, userDir, "check"));
        FileUtil.mkdir(checkDir);
        File tempFile = FileUtil.file("tempFile");

        org.springframework.core.io.ClassPathResource
            jarPathResource = new org.springframework.core.io.ClassPathResource("tool/base/base-check-1.0-SNAPSHOT.jar");
        org.springframework.core.io.ClassPathResource
            readmePathResource = new org.springframework.core.io.ClassPathResource("tool/base/README.md");
        org.springframework.core.io.ClassPathResource
            jrePathResource = new org.springframework.core.io.ClassPathResource("tool/jre.zip");
        try {
            // 复制jar文件
            File jarFile = FileUtil.file(CharSequenceUtil.join(File.separator, checkDir.getPath(), jarPathResource.getFilename()));
            FileUtil.touch(jarFile);
            Files.copy(jarPathResource.getInputStream(), jarFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            // 复制redme文件
            File readmeFile = FileUtil.file(CharSequenceUtil.join(File.separator, checkDir.getPath(), readmePathResource.getFilename()));
            FileUtil.touch(readmeFile);
            Files.copy(readmePathResource.getInputStream(), readmeFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            // 复制jre文件
            File jreFile = FileUtil.file(CharSequenceUtil.join(File.separator, checkDir.getPath(), jrePathResource.getFilename()));
            FileUtil.touch(jreFile);
            Files.copy(jrePathResource.getInputStream(), jreFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            // 获取统一数源包
            String path = System.getProperty(DatasourceConstant.MCDATASOURCE_HOME);
            PathUtil.copy(FileUtil.file(path).toPath(), checkDir.toPath(), StandardCopyOption.REPLACE_EXISTING);

            // 构建基础检测pipeline
            File confDir = FileUtil.file(CharSequenceUtil.join(File.separator, userDir, "check", "conf"));
            FileUtil.mkdir(confDir);

            Map<Integer, List<SecurityRules>> securityRulesMap =
                securityRulesMapper.selectList(null).stream().collect(Collectors.groupingBy(SecurityRules::getDbType));
            securityRulesMap.forEach((dbType, rules) -> {
                List<SecurityRulePipeline> securityRulePipelines = buildPipeline(rules);
                File file = FileUtil.file(CharSequenceUtil.join(File.separator, confDir,
                    CharSequenceUtil.concat(true, DataSourceType.getType(dbType).name(), ".json")));
                FileUtil.writeString(JSONUtil.toJsonStr(securityRulePipelines), file, StandardCharsets.UTF_8);
            });

            File zipPath = ZipUtil.zip(tempFile, true, checkDir);
            Func.write(response, zipPath, "check.zip");

        } catch (IOException e) {
            log.error("下载离线工具失败", e);
            throw new ServiceException("下载离线工具失败!");
        } finally {
            org.apache.commons.io.FileUtils.deleteQuietly(tempFile);
            org.apache.commons.io.FileUtils.deleteQuietly(checkDir);
        }
    }

    private List<SecurityRulePipeline> buildPipeline(List<SecurityRules> rules) {
        // 子节点Map
        final Map<Integer, SecurityRules> rulesMap = rules.stream().filter(r ->
            Objects.isNull(r.getTermId())).collect(Collectors.toMap(SecurityRules::getId, Function.identity()));
        List<SecurityRules> pRules = rules.stream().filter(r -> Objects.nonNull(r.getTermId())).collect(Collectors.toList());
        List<Integer> ruleIds = pRules.stream().map(SecurityRules::getId).collect(Collectors.toList());
        // 查询root规则对应的检查点，填充
        Map<Integer, LabelVO> ruleTermMap = securityRulesMapper.queryRuleTermMap(ruleIds).stream().collect(Collectors.toMap(l -> l.getId().intValue(), Function.identity()));
        return pRules.stream().map(r -> buildPipelineNode(r, rulesMap, ruleTermMap)).collect(Collectors.toList());
    }

    private SecurityRulePipeline buildPipelineNode(SecurityRules rule, Map<Integer, SecurityRules> rulesMap, Map<Integer, LabelVO> ruleTermMap) {
        SecurityRulePipeline root = BeanUtil.copyProperties(rule, SecurityRulePipeline.class);
        root.setRuleId(rule.getId());
        root.setStatement(SecurityUtils.encryptAes(root.getStatement()));
        if (ruleTermMap.containsKey(rule.getId())) {
            LabelVO labelVO = ruleTermMap.get(rule.getId());
            String content = labelVO.getName();
            String name = StrUtil.subBefore(content, StrUtil.AT, true);
            String describe = StrUtil.subAfter(content, StrUtil.AT, true);
            root.setContent(StrUtil.subAfter(name, StrUtil.DASHED, true));
            root.setOption(StrUtil.subBefore(name, StrUtil.DASHED, true));
            root.setDescribe(describe);
            root.setAbility(labelVO.getAbility());
        }
        if (Objects.nonNull(rule.getConformNode())) {
            root.setConform(buildPipelineNode(rulesMap.get(rule.getConformNode()), rulesMap, ruleTermMap));
        }
        if (Objects.nonNull(rule.getDiscrepancyNode())) {
            root.setDiscrepancy(buildPipelineNode(rulesMap.get(rule.getDiscrepancyNode()), rulesMap, ruleTermMap));
        }
        return root;
    }
}
