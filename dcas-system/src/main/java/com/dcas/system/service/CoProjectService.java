package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.core.domain.model.LoginUser;
import com.dcas.common.enums.WorkPermissionType;
import com.dcas.common.model.vo.ProjectDetailVO;
import com.dcas.common.model.vo.ProjectWorkVO;
import com.github.pagehelper.PageInfo;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.CoCustomer;
import com.dcas.common.domain.entity.CoProject;
import com.dcas.common.model.vo.QueryProjectVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【project】的数据库操作Service
 * @createDate 2022-05-16 11:22:02
 */
public interface CoProjectService extends IService<CoProject> {
    /**
     * 查询项目记录
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:27
     */
    PageInfo<QueryProjectVo> query(RequestModel<QueryProjectDTO> dto);

    /**
     * 新增项目记录
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 14:51
     */
    int add(RequestModel<AddProjectDto> dto);

    /**
     * 删除项目记录
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:39
     */
    int remove(RequestModel<PrimaryKeyListDTO> dto);

    /**
     * 更新项目
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:46
     */
    boolean edit(RequestModel<UpdateProjectDto> dto);

    /**
     * 查询客户记录
     *
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:27
     */
    List<CoCustomer> queryCustomer(RequestModel<QueryCustomerDto> dto);

    /**
     * 根据作业编号查询项目信息
     *
     * @param operationId 作业id
     * @return 项目信息
     */
    CoProject queryProject(String operationId);

    /**
     * 查看项目作业详情
     *
     * @param projectId 项目id
     */
    ProjectDetailVO projectWorks(String projectId);

    boolean hasPermission(SSOAccountDTO user, ProjectWorkVO vo, WorkPermissionType type);

    void fileDownload(String projectId, HttpServletResponse response);
}
