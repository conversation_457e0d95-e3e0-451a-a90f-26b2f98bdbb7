package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.api.MetaConst;
import com.dcas.common.core.param.DataSourceParam;
import com.dcas.common.enums.*;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.TreeLabelDTO;
import com.dcas.common.model.other.*;
import com.dcas.common.model.query.CompareColumnQuery;
import com.dcas.common.model.req.*;
import com.dcas.common.model.vo.*;
import com.dcas.common.utils.DatabaseOptionUtil;
import com.dcas.common.utils.Func;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.file.FileUtils;
import com.dcas.common.domain.entity.SourceConfig;
import com.dcas.common.domain.entity.SourceConfigAttach;
import com.dcas.discovery.factory.DatabaseFactory;
import com.dcas.common.model.param.DiscoveryJobParam;
import com.dcas.common.model.dto.SchemaTableDTO;
import com.dcas.discovery.service.DiscoveryJobService;
import com.dcas.discovery.service.impl.SourceConfigAttachServiceImpl;
import com.dcas.discovery.service.impl.SourceConfigServiceImpl;
import com.dcas.common.model.dto.ScanTaskAddDTO;
import com.dcas.common.domain.entity.*;
import com.dcas.market.app.service.IAppService;
import com.dcas.system.domain.resp.TestConnectionResponse;
import com.dcas.system.service.DatabaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.mchz.datasource.cli.DatasourceDatabaseCli;
import com.mchz.datasource.cli.RsResult;
import com.mchz.mcdatasource.api.model.CustomMutableSchema;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.mcdatasource.core.DatasourceConstant;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.metamodel.schema.Column;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @Date 2022/12/23 9:38
 * @Version 1.0
 * @Description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DatabaseServiceImpl implements DatabaseService {
    private final Map<String, Map<Thread, DatasourceDatabaseCli>> cliCache = new ConcurrentHashMap<>();
    private final DiscoveryJobService discoveryJobServiceImpl;
    private final DiscoveryJobMapper discoveryJobMapper;
    private final SourceConfigMapper sourceConfigMapper;
    private final DbSqlConfigMapper dbSqlConfigMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final ScreenDatasourceMapper screenDatasourceMapper;
    private final CoPermissionMapper coPermissionMapper;
    private final ScanSourceConfigMapper scanSourceConfigMapper;

    private Boolean enabled;
    private final IAppService appService;
    private final DatabaseFactory databaseFactory;
    private final MkAppJobServiceImpl mkAppJobService;
    private final SourceConfigServiceImpl sourceConfigService;
    private final PreSourceConfigServiceImpl preSourceConfigService;
    private final SourceConfigAttachServiceImpl sourceConfigAttachService;
    private final PreSourceConfigAttachServiceImpl preSourceConfigAttachService;

    private final CoOperationMapper coOperationMapper;
    private final SecurityOperationMapper securityOperationMapper;
    private final ScanTaskServiceImpl scanTaskService;
    private final CoDbSecurityServiceImpl coDbSecurityService;

    @PostConstruct
    public void init() {
        enabled = System.getProperty("enable") != null && "true".equals(System.getProperty("enable"));
        String path = System.getProperty(DatasourceConstant.MCDATASOURCE_HOME);
        if (!enabled) {
            log.warn("The mcDatasource Directory does not exist ({})", path);
        } else {
            log.info("mcDatasource Resource Directory is {}", path);
        }
    }

    @Override
    @SchemaSwitch
    public TestConnectionResponse testConnection(DataSourceParam config) {
        TestConnectionResponse response = new TestConnectionResponse();
        response.setSuccess(true);
        try {
            updateConfig(config, Boolean.TRUE);
            databaseFactory.testConnection(config);
        } catch (Exception e) {
            Throwable cause = Func.getCause(e);
            String msg = cause.getMessage();
            if(msg == null){
                msg = Func.getMessage(e);
            }
            response.setSuccess(false);
            response.setMessage(msg);
        }
        if (!response.isSuccess())
            return response;
        if (Objects.isNull(config.getNeedCheck()) || Objects.equals(Boolean.FALSE, config.getNeedCheck())) {
            return response;
        }
        // 最小权限检测
        DbSqlConfig dbSqlConfig = dbSqlConfigMapper.selectOne(
                new QueryWrapper<DbSqlConfig>().eq("type", 2).eq("db_type", config.getConfigType().intValue()));
        if (Objects.isNull(dbSqlConfig))
            return response;
        else {
            return permissionDetection(config, dbSqlConfig, response);
        }
    }

    public TestConnectionResponse permissionDetection(DataSourceParam config, DbSqlConfig dbSqlConfig, TestConnectionResponse response) {
        try {
            final Set<Boolean> resultSet = new HashSet<>();
            String sql = StrUtil.replace(dbSqlConfig.getSql(), "${username}", config.getUsername());
            RsResult result = openQuery(config, sql);
            result.getIterator().forEachRemaining(objects -> {
                Boolean f = Objects.equals(objects[0].toString(), "1");
                resultSet.add(f);
            });
            boolean b = resultSet.size() == 1 && resultSet.contains(Boolean.TRUE);
            response.setSuccess(b);
            if (!b) {
                String s = StrUtil.replace(dbSqlConfig.getDescription(), "${username}", config.getUsername());
                response.setMessage(String.format("最小权限校验失败，请按照以下要求授权数据库用户最小权限：%s", s));
            }
        } catch (Exception e) {
            Throwable cause = Func.getCause(e);
            String msg = cause.getMessage();
            if(msg == null){
                msg = Func.getMessage(e);
            }
            response.setSuccess(false);
            response.setMessage("执行最小权限检测语句失败：" + msg);
        }
        return response;
    }

    @Override
    public RsResult openQuery(DataSourceParam param, String sql) throws Exception {
        if (StringUtils.isBlank(sql)) {
            throw new ServiceException("查询SQL不能为空");
        }
        DatasourceDatabaseCli cli = null;
        try {
            String key = buildKey(param);
            Map<Thread, DatasourceDatabaseCli> map = cliCache.get(key);
            if (Objects.nonNull(map)) {
                cli = map.get(Thread.currentThread());
            } else {
                map = new ConcurrentHashMap<>();
                cliCache.put(key, map);
            }

            if (Objects.isNull(cli)) {
                DatabaseServiceImpl.DatasourceParam source = newDatasourceParam(param);
                cli = new DatasourceDatabaseCli(source.getType(), source.getHost(), source.getDb(), source.getPort(),
                        source.getUser(), source.getPass(), true, source.getAdvanced());
                cli.connect(false);
                map.put(Thread.currentThread(), cli);
            }
            return cli.openQuery(sql, 1000);
        } catch (Exception e) {
            log.error("openQuery执行失败", e);
            throw e;
        }
    }

    @Override
    public synchronized void close(String key) {
        Map<Thread, DatasourceDatabaseCli> map = cliCache.get(key);
        if (Objects.isNull(map)) {
            return;
        }
        Thread thread = Thread.currentThread();
        DatasourceDatabaseCli cli = map.get(thread);
        if (Objects.isNull(cli))
            return;
        if (map.size() > 1) {
            map.remove(thread);
            return;
        }
        try {
            cli.close();
            log.info("【统一数据源】主动关闭连接 线程{} 数据源{} ", thread.getName(), key);
        } catch (Exception e) {
            log.error("【统一数据源】主动关闭连接(%d)失败", e);
            map.remove(thread);
        }
        if (map.isEmpty())
            cliCache.remove(key);
    }

    @Override
    public void updateConfig(DataSourceParam config, boolean needDecryptPwd) {
        config.setHost(SecurityUtils.decryptAes(config.getHost()));
        config.setPort(SecurityUtils.decryptAes(config.getPort()));
        config.setUsername(SecurityUtils.decryptAes(config.getUsername()));
        if (needDecryptPwd) {
            config.setPassword(SecurityUtils.decryptAes(config.getPassword()));
        }
        config.setDbName(SecurityUtils.decryptAes(config.getDbName()));
        DatabaseOptionUtil.updateConfigOption(null, config, DatabaseOptionUtil.Scheme.VIEW_USABLE, getFileParsingFun());
        addProperties(config);
        config.setDataBaseType(DataSourceType.getType(config.getConfigType()).getDataBaseType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addSourceConfig(SourceConfigAddReq request) {
        DataSourceParam dbConfig = request.getDbConfig();
        String configName = request.getConfigName();
        String operationId = request.getOperationId();
        if (ConfigScanType.contains(request.getConfigScan(), ConfigScanType.DB))
            updateConfig(dbConfig, Boolean.FALSE);
        int count = preSourceConfigService.count(new QueryWrapper<PreSourceConfig>().eq("operation_id", operationId)
                .eq("config_name", request.getConfigName()));
        if (count > 0)
            throw new ServiceException("当前作业数据源名称已存在");

        // 作业内新增数据源
        Integer sourceId = null;
        // 添加到全局数据源
        if (request.getIsGlobal()) {
            PreSourceConfig bean = PreSourceConfig.builder()
                    .operationId("-1")
                    .configName(configName)
                    .configType(request.getConfigType())
                    .configScan(request.getConfigScan())
                    .host(dbConfig.getHost())
                    .port(dbConfig.getPort())
                    .username(dbConfig.getUsername())
                    .password(dbConfig.getPassword())
                    .dbName(dbConfig.getDbName())
                    .userAccount(SecurityUtils.getAccount())
                    .systemId(null)
                    .build();
            int count2 = checkPreConfig(bean);
            if (count2 > 0)
                throw new ServiceException("全局已存在当前数据源配置");
            preSourceConfigService.save(bean);
            List<PreSourceConfigAttach> attachList = dbConfig.getAttachment().entrySet().stream().map(entry ->
                    PreSourceConfigAttach.builder()
                            .configId(bean.getId())
                            .name(entry.getKey())
                            .value(entry.getValue())
                            .build()
            ).collect(Collectors.toList());
            preSourceConfigAttachService.saveBatch(attachList);
            sourceId = bean.getId();
        }

        if (!Objects.equals("-1", operationId)) {
            PreSourceConfig sourceConfig = PreSourceConfig.builder()
                    .operationId(operationId)
                    .configName(configName)
                    .configType(request.getConfigType())
                    .configScan(request.getConfigScan())
                    .host(dbConfig.getHost())
                    .port(dbConfig.getPort())
                    .username(dbConfig.getUsername())
                    .password(dbConfig.getPassword())
                    .dbName(dbConfig.getDbName())
                    .userAccount(SecurityUtils.getAccount())
                    .systemId(null)
                    .build();
            preSourceConfigService.save(sourceConfig);
            List<PreSourceConfigAttach> attachList = dbConfig.getAttachment().entrySet().stream().map(entry ->
                    PreSourceConfigAttach.builder()
                            .configId(sourceConfig.getId())
                            .name(entry.getKey())
                            .value(entry.getValue())
                            .build()
            ).collect(Collectors.toList());
            preSourceConfigAttachService.saveBatch(attachList);
            sourceId = sourceConfig.getId();
        }

        // 修改扫描数据源状态为被使用
        if (Objects.nonNull(request.getScanSourceId())) {
            ScanSourceConfig scanSource = scanSourceConfigMapper.selectById(request.getScanSourceId());
            if (Objects.nonNull(scanSource)) {
                scanSource.setConfigType(request.getConfigType());
                scanSource.setStatus(Boolean.TRUE);
                scanSourceConfigMapper.updateById(scanSource);
            }
        }
        return sourceId;
    }

    private int checkPreConfig(PreSourceConfig config) {
        QueryWrapper<PreSourceConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_type", config.getConfigType());
        queryWrapper.eq("host", config.getHost());
        queryWrapper.eq("port", config.getPort());
        if (config.getDbName() == null){
            queryWrapper.isNull( "db_name");
        } else {
            queryWrapper.eq(StrUtil.isNotEmpty(config.getDbName()), "db_name", config.getDbName());
        }
        queryWrapper.eq("username", config.getUsername());
        queryWrapper.eq("operation_id", config.getOperationId());
        return preSourceConfigService.getBaseMapper().selectCount(queryWrapper);
    }

    @Override
    public void deleteSourceConfig(Integer id) {
        PreSourceConfig sourceConfig = preSourceConfigService.getById(id);
        if (Objects.isNull(sourceConfig))
            return;
        validateSourceInUsed(id);
        preSourceConfigService.removeById(id);
        preSourceConfigAttachService.remove(new QueryWrapper<PreSourceConfigAttach>().eq("config_id", id));
        int[] appIds = StrUtil.splitToInt(sourceConfig.getAppIds(), StrUtil.COMMA);
        List<Integer> delIds = Arrays.stream(appIds)
                .boxed()
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(delIds))
            mkAppJobService.removeByIds(delIds);
        // 删除全局数据源 修改扫描数据源中相关扫描数据源的 status
        if (Objects.equals(sourceConfig.getOperationId(), "-1")) {
            UpdateWrapper<ScanSourceConfig> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("status", Boolean.FALSE).eq("config_type", sourceConfig.getConfigType())
                    .eq("ip", sourceConfig.getHost()).eq("port", sourceConfig.getPort());
            scanSourceConfigMapper.update(null, updateWrapper);
        }
    }

    @Override
    public void batchDeleteSourceConfig(IdsReq ids) {
        ids.getIds().forEach(id -> {
            // 校验数源是否被引用，引用则无法删除
            validateSourceInUsed(id);
            // 单个删除
            deleteSourceConfig(id);
        });
    }

    private void validateSourceInUsed(Integer id) {
        int count = screenDatasourceMapper.selectCount(new QueryWrapper<ScreenDatasource>().eq("source_id", id));
        int countA = preSourceConfigService.count(new QueryWrapper<PreSourceConfig>().eq("id", id).eq("current_step", 4));
        if (countA > 0){
            PreSourceConfig preSourceConfig = preSourceConfigService.getById(id);
            throw new ServiceException("该数源【"+preSourceConfig.getConfigName()+"】正在被作业调用，无法删除！");
        }
        if (count > 0){
            PreSourceConfig preSourceConfig = preSourceConfigService.getById(id);
            throw new ServiceException("该数源【"+preSourceConfig.getConfigName()+"】正在被数据大屏调用，无法删除！");
        }
    }

    @Override
    public SourceConfigResultVO applySourceConfig(SourcePageConfirmReq req) {
        if (Objects.equals("-1", req.getOperationId()))
            throw new ServiceException("作业ID异常");
        List<Long> taskIds = new ArrayList<>();
        SourceConfigResultVO vo= new SourceConfigResultVO();
        QueryWrapper<PreSourceConfig> configQueryWrapper = new QueryWrapper<>();
        configQueryWrapper.eq("operation_id", req.getOperationId());
        configQueryWrapper.eq("current_step", 3);
        if (Objects.nonNull(req.getSourceId()))
            configQueryWrapper.eq("id", req.getSourceId());
        List<PreSourceConfig> preSourceConfigList = preSourceConfigService.list(configQueryWrapper);
        if (CollUtil.isEmpty(preSourceConfigList)) {
            log.info("作业{}无已配置数据源，无需创建作业", req.getOperationId());
            return vo;
        }

        List<MkAppJob> jobConfigList =
            mkAppJobService.list(new QueryWrapper<MkAppJob>().eq("operation_id", req.getOperationId()));

        StringBuilder sb = new StringBuilder();
        for (PreSourceConfig preSourceConfig : preSourceConfigList) {
            List<Integer> appIds = StrUtil.isEmpty(preSourceConfig.getAppIds()) ? new ArrayList<>()
                : StrUtil.split(preSourceConfig.getAppIds(), StrUtil.COMMA).stream().mapToInt(Integer::parseInt).boxed()
                    .collect(Collectors.toList());
            Map<Integer, MkAppJob> jobConfigMap = jobConfigList.stream().filter(j -> appIds.contains(j.getId()))
                .collect(Collectors.toMap(MkAppJob::getType, Function.identity()));

            try {
                // 创建数据库作业 资产评估、权限查询、基础评估
                if (ConfigScanType.contains(preSourceConfig.getConfigScan(), ConfigScanType.DB)) {
                    taskIds.addAll(startDbJobTask(req.getOperationId(), preSourceConfig, jobConfigMap));
                }
            } catch (Exception e) {
                log.error("应用数据源失败", e);
                sb.append("应用数据源【").append(preSourceConfig.getConfigName()).append("】失败!创建资产评估、权限查询或者基础评估任务执行失败!\n");
            }

            try {
                // 创建主机漏扫作业
                if (ConfigScanType.contains(preSourceConfig.getConfigScan(), ConfigScanType.HOST)) {
                    QueryWrapper<ScanTask> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("operation_id", req.getOperationId());
                    queryWrapper.eq("targets", preSourceConfig.getHost());
                    ScanTask scanTask = scanTaskService.getBaseMapper().selectOne(queryWrapper);
                    if (Objects.nonNull(scanTask)) {
                        scanTaskService.startScanTask(scanTask.getId());
                    }
                }
            } catch (Exception e) {
                log.error("应用数据源失败", e);
                sb.append("应用数据源【").append(preSourceConfig.getConfigName()).append("】失败!创建主机漏扫作业执行失败!\n");
            }

            try {
                // 创建能力市场作业
                if (Objects.nonNull(preSourceConfig.getAbilityModule()) && preSourceConfig.getAbilityModule() > 0) {
                    List<MkAppJob> mkAppJobs = appService.createJob(preSourceConfig.getAbilityModule(), jobConfigMap);
                    mkAppJobService.updateBatchById(mkAppJobs);
                    PreSourceConfig update = new PreSourceConfig();
                    update.setId(preSourceConfig.getId());
                    update.setDbName(preSourceConfig.getDbName());
                    update.setSystemId(preSourceConfig.getSystemId());
                    update.setCurrentStep(4);
                    preSourceConfigService.updateById(update);
                }
            } catch (Exception e) {
                log.error("应用数据源失败", e);
                sb.append("应用数据源【").append(preSourceConfig.getConfigName()).append("】失败!创建能力市场作业执行失败!\n");
            }
        }
        vo.setTaskIds(taskIds);
        vo.setErrorMessage(sb.toString());
        return vo;
    }

    private List<Long> startDbJobTask(String operationId, PreSourceConfig preSourceConfig, Map<Integer, MkAppJob> jobConfigMap) {
        List<Long> res = new ArrayList<>();
        SourceConfig sourceConfig = sourceConfigMapper.selectById(preSourceConfig.getConfigId());
        if (Objects.isNull(sourceConfig))
            throw new ServiceException("当前作业不存在该数据源配置！");

        if (AssessModuleType.contains(preSourceConfig.getAccessModule(), AssessModuleType.ASSET)) {
            String operationName;
            if (operationId.length() < 10) {
                SecurityOperation securityOperation = securityOperationMapper.selectById(Integer.parseInt(operationId));
                operationName = securityOperation.getName();
            } else {
                CoOperation coOperation = coOperationMapper.selectById(operationId);
                operationName = coOperation.getOperationName();
            }
            List<String> schemas = JSONUtil.toList(preSourceConfig.getSchemas(), String.class);
            MkAppJob mkAppJob = jobConfigMap.get(~AssessModuleType.ASSET.getCode() + 1);
            DiscoveryJobParam param = JSONUtil.toBean(JSONUtil.toJsonStr(mkAppJob.getParamIn()), DiscoveryJobParam.class);
            param.setSystemId(preSourceConfig.getSystemId());
            param.setSourceId(sourceConfig.getId().toString());
            param.setName(String.format("%s-%s-资产发现作业-%s", operationName, sourceConfig.getConfigName(), DateUtil.millisecond(new Date())));
            param.setSchemas(schemas);
            param.setJobType(JobType.DISCOVERY.getCode());
            discoveryJobServiceImpl.saveConfig(param);
            preSourceConfig.setCurrentStep(4);
            preSourceConfigService.updateById(preSourceConfig);
        }

        if (AssessModuleType.contains(preSourceConfig.getAccessModule(), AssessModuleType.AUTH)) {
            int count = coDbSecurityService.count(new QueryWrapper<CoDbSecurity>().eq("operation_id", operationId).eq("job_type",
                    DatabaseJobType.PERMISSION.getCode()).eq("config_id", preSourceConfig.getConfigId()));
            if (count == 0) {
                List<Long> jobIds = getJobIds(Collections.singletonList(sourceConfig), operationId, LabelEnum.SJQX);
                res.addAll(jobIds);
            }
            if (preSourceConfig.getCurrentStep() != 4) {
                preSourceConfig.setCurrentStep(4);
                preSourceConfigService.updateById(preSourceConfig);
            }
        }

        if (AssessModuleType.contains(preSourceConfig.getAccessModule(), AssessModuleType.BASIC)) {
            boolean nonExist = checkExistJob(preSourceConfig, operationId, DatabaseJobType.RISK);
            if (nonExist) {
                List<Long> jobIds = getJobIds(Collections.singletonList(sourceConfig), operationId, LabelEnum.JCHJ);
                res.addAll(jobIds);
            }
            if (preSourceConfig.getCurrentStep() != 4) {
                preSourceConfig.setCurrentStep(4);
                preSourceConfigService.updateById(preSourceConfig);
            }
        }

        return res;
    }

    private boolean checkExistJob(PreSourceConfig preSourceConfig, String operationId, DatabaseJobType jobType) {
        List<SourceConfig> sourceConfigs = coDbSecurityService.getBaseMapper().querySourceConfig(operationId, jobType.getCode());
        return sourceConfigs.stream().noneMatch(s -> Objects.equals(preSourceConfig.getHost(), s.getHost())
                && Objects.equals(preSourceConfig.getPort(), s.getPort()) && Objects.equals(preSourceConfig.getConfigType(), s.getConfigType()));
    }

    @Override
    public List<SchemaTableDTO> listSourceSchema(Integer id, String operationId) {
        PreSourceConfig preSourceConfig = preSourceConfigService.getById(id);
        if (Objects.isNull(preSourceConfig))
            throw new ServiceException("数据源配置不存在");
        DataSourceParam param = toSourceConfigUsable(preSourceConfig);
        List<String> schemas = databaseFactory.crawlSchema(param);
        if (CollUtil.isEmpty(schemas))
            return new ArrayList<>();
        List<String> usedSchemas = JSONUtil.toList(preSourceConfig.getSchemas(), String.class);
        return schemas.stream().map(s -> {
            int used = 0;
            if (Objects.nonNull(preSourceConfig.getSystemId())) {
                if (CollUtil.isEmpty(usedSchemas) || usedSchemas.contains(s))
                    used = 1;
            }
            return SchemaTableDTO.builder().schemaName(s).used(used).build();
        }).sorted(Comparator.comparing(SchemaTableDTO::getSchemaName)).collect(Collectors.toList());
    }

    @Override
    public List<SchemaTableDTO> listSourceTable(Integer id, String schema, Boolean isPreSource) {
        DataSourceParam param;
        if (isPreSource) {
            PreSourceConfig preSourceConfig = preSourceConfigService.getById(id);
            if (Objects.isNull(preSourceConfig))
                throw new ServiceException("数据源配置不存在");
            param = toSourceConfigUsable(preSourceConfig);
            if (StrUtil.isNotEmpty(schema))
                param.getAttachment().put("schema", schema);
        } else {
            SourceConfig sourceConfig = sourceConfigMapper.selectById(id);
            if (Objects.isNull(sourceConfig))
                throw new ServiceException("数据源配置不存在");
            param = toSourceConfigParam(sourceConfig);
            if (StrUtil.isNotEmpty(schema))
                param.getAttachment().put("schema", schema);
        }
        param.setConfigTypeName(DataSourceType.getType(param.getConfigType()).getName());
        List<TableVO> tables = databaseFactory.getTables(param);
        if (CollUtil.isEmpty(tables))
            return new ArrayList<>();
        return tables.stream().map(this::newSourceMetaTableDTO).sorted(Comparator.comparing(SchemaTableDTO::getSchemaAndTableName))
                .collect(Collectors.toList());
    }

    @Override
    public SourceConfigUpdateReq querySourceConfigById(Integer id) {
        SourceConfigUpdateReq req = new SourceConfigUpdateReq();
        PreSourceConfig preSourceConfig = preSourceConfigService.getById(id);
        if (Objects.isNull(preSourceConfig))
            throw new ServiceException("数据源不存在，id:{}", id);
        Map<String, String> map = new HashMap<>();
        List<PreSourceConfigAttach> attaches = preSourceConfigAttachService.list(new QueryWrapper<PreSourceConfigAttach>().eq("config_id", id));
        attaches.forEach(a -> map.put(a.getName(), a.getValue()));
        DataSourceParam dataSource = Func.toBean(preSourceConfig, DataSourceParam.class);
        dataSource.setAttachment(map);
        if (Objects.nonNull(dataSource.getConfigType()))
            dataSource.setSourceType(DataSourceType.getType(dataSource.getConfigType().toString()).getType());
        req.setId(id);
        req.setConfigType(preSourceConfig.getConfigType());
        req.setConfigName(preSourceConfig.getConfigName());
        req.setConfigScan(preSourceConfig.getConfigScan());
        req.setUserAccount(preSourceConfig.getUserAccount());
        req.setDbConfig(dataSource);
        return req;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmAssessRange(SourceRangeConfirmReq req) {
        if (req.getOperationId().equals("-1"))
            throw new ServiceException("作业ID异常");
        PreSourceConfig preSource = preSourceConfigService.getById(req.getSourceId());
        if (Objects.isNull(preSource))
            throw new ServiceException("数据源配置不存在id:{}", req.getSourceId());

        List<PreSourceConfigAttach> preSourceConfigAttaches = preSourceConfigAttachService.list(
                new QueryWrapper<PreSourceConfigAttach>().eq("config_id", req.getSourceId()));

        preSource.setSystemId(preSource.getSystemId());
        preSource.setCurrentStep(2);
        preSourceConfigService.updateById(preSource);

        if (ConfigScanType.contains(preSource.getConfigScan(), ConfigScanType.DB)) {
            SourceConfig sourceConfig = Func.toBean(preSource, SourceConfig.class);
            sourceConfig.setId(null);
            sourceConfig.setOperationId(req.getOperationId());
            Func.beforeInsert(sourceConfig);
            sourceConfigService.save(sourceConfig);
            List<SourceConfigAttach> sourceConfigAttaches = preSourceConfigAttaches.stream().map(a -> {
                SourceConfigAttach bean = Func.toBean(a, SourceConfigAttach.class);
                bean.setId(null);
                bean.setConfigId(sourceConfig.getId());
                return bean;
            }).collect(Collectors.toList());
            UpdateWrapper<PreSourceConfig> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("config_id", sourceConfig.getId());
            updateWrapper.set("current_step", 2);
            updateWrapper.set("system_id", preSource.getSystemId());
            updateWrapper.eq("id", preSource.getId());
            preSourceConfigService.update(updateWrapper);
            sourceConfigAttachService.saveBatch(sourceConfigAttaches);
        }

        if (ConfigScanType.contains(preSource.getConfigScan(), ConfigScanType.HOST)) {
            boolean containsBasic;
            if (req.getOperationId().length() > 10) {
                containsBasic = coOperationMapper.labelIsExist(req.getOperationId(), LabelEnum.JCHJ.getCode()) > 0;
            } else {
                containsBasic = securityOperationMapper.labelIsExist(Integer.parseInt(req.getOperationId()), LabelEnum.JCHJ.getCode()) > 0;
            }
            if (containsBasic) {
                String host = preSource.getHost();
                QueryWrapper<ScanTask> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("operation_id", req.getOperationId());
                queryWrapper.eq("targets", host);
                int count = scanTaskService.count(queryWrapper);
                if (count > 0)
                    log.info("当前主机【{}】存在漏扫作业，忽略", host);
                String taskName = String.format("主机【%s】漏洞扫描任务", host);
                scanTaskService.addScanTask(new ScanTaskAddDTO(req.getOperationId(),taskName, host));
            }
        }
    }

    @Override
    //@DataScope(userAlias = "s", extra = "source") // 20250115 产品暂时去掉数据源权限过滤
    public List<PreSourceConfigVO> queryPreSourceConfigList(PreSourceConfigRequest request) {
        if (Objects.isNull(request.getPageNum()))
            request.setPageNum(1);
        if (Objects.isNull(request.getPageSize()))
            request.setPageSize(100);
        try (Page<Object> ignore = PageHelper.startPage(request.getPageNum(), request.getPageSize())) {
            Map<Long, String> systemMap = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(request.getOperationId(),
                    LabelEnum.XTDY.getCode()).stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId, TreeLabelDTO::getTreeName));
            List<PreSourceConfig> list = preSourceConfigService.getBaseMapper().listByPermission(request);
            return list.stream().map(s -> {
                PreSourceConfigVO bean = Func.toBean(s, PreSourceConfigVO.class);
                String dataSourceName = Objects.isNull(s.getConfigType()) ? StrUtil.EMPTY : DataSourceType.getType(s.getConfigType()).getName();
                bean.setConfigType(dataSourceName);
                bean.setModuleName(getModuleName(s.getAccessModule(), s.getAbilityModule()));
                bean.setAssetConfig(buildAssetDetail(s));
                if (Objects.nonNull(s.getAbilityModule()) && StrUtil.isNotEmpty(s.getAppIds())) {
                    List<Integer> appIds = StrUtil.split(s.getAppIds(), StrUtil.COMMA).stream().mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
                    Map<Integer, MkAppJob> mkAppMap = mkAppJobService.list(new QueryWrapper<MkAppJob>()
                            .in("id", appIds)).stream().collect(Collectors.toMap(MkAppJob::getType, Function.identity()));
                    bean.setMaskingConfig(buildMkCompareConfig(mkAppMap.get(AbilityType.DESENSITIZATION.getCode())));
                    bean.setEncryptConfig(buildMkCompareConfig(mkAppMap.get(AbilityType.ENCRYPT.getCode())));
                    bean.setNetworkConfig(buildPcapConfig(mkAppMap.get(AbilityType.PCAP.getCode())));
                }
                bean.setDescription(s.getDbConfig(Boolean.TRUE));
                bean.setDescription(bean.getDescription()
                        + "；数据源类型：" + dataSourceName);
                if (StrUtil.isNotEmpty(systemMap.get(s.getSystemId())))
                    bean.setDescription(bean.getDescription() + "；业务系统：" + systemMap.get(s.getSystemId()));
                return bean;
            }).collect(Collectors.toList());
        }
    }

    private PcapConfig buildPcapConfig(MkAppJob mkAppJob) {
        if (Objects.isNull(mkAppJob))
            return null;
        return JSONUtil.toBean(JSONUtil.toJsonStr(mkAppJob.getParamIn()), PcapConfig.class);
    }

    private DataCompareConfigVO buildMkCompareConfig(MkAppJob mkAppJob) {
        if (Objects.isNull(mkAppJob))
            return null;
        MkCompareConfig compareConfig = JSONUtil.toBean(JSONUtil.toJsonStr(mkAppJob.getParamIn()), MkCompareConfig.class);
        if (Objects.isNull(compareConfig))
            return null;
        DataCompareConfigVO vo = new DataCompareConfigVO();
        SourceDatabase targetDatabase = compareConfig.getPlanBasic().getTargetDatabase();
        if (Objects.isNull(targetDatabase))
            return vo;
        vo.setConfigType(targetDatabase.getType());
        vo.setIp(targetDatabase.getHost());
        vo.setPort(targetDatabase.getPort());
        vo.setUsername(targetDatabase.getUsername());
        if (Objects.nonNull(compareConfig.getCompareContent())
                && CollUtil.isNotEmpty(compareConfig.getCompareContent().getSchemaSelectList())) {
            List<SchemaSelect> schemaSelectList = compareConfig.getCompareContent().getSchemaSelectList();
            vo.setDbName(schemaSelectList.stream().map(SchemaSelect::getTargetSchemaName).collect(Collectors.joining("、")));
        }
        return vo;
    }

    private AssetConfigVO buildAssetDetail(PreSourceConfig s) {
        AssetConfigVO vo = new AssetConfigVO();
        vo.setSystemId(s.getSystemId());
        vo.setSchemaList(JSONUtil.toList(s.getSchemas(), String.class));
        return vo;
    }

    private String getModuleName(String accessType, Integer abilityType) {
        StringBuilder sb = new StringBuilder();
        if (Objects.nonNull(accessType)) {
            for (String s : StrUtil.split(accessType, StrUtil.COMMA)) {
                String name = LabelEnum.getNameByCode(Long.parseLong(s));
                sb.append(name).append("、");
            }
        }
        if (Objects.nonNull(abilityType)) {
            for (AbilityType type : AbilityType.values()) {
                if (AbilityType.contains(abilityType, type)) {
                    sb.append(type.getDescription()).append("、");
                }
            }
        }
        if (sb.toString().endsWith("、"))
            sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSourceConfig(SourceConfigUpdateReq req) {
        PreSourceConfig preSourceConfig = preSourceConfigService.getById(req.getId());
        if (Objects.isNull(preSourceConfig))
            throw new ServiceException("数据源配置不存在");

        DataSourceParam dbConfig = req.getDbConfig();
        preSourceConfig.setConfigScan(req.getConfigScan());
        preSourceConfig.setConfigName(req.getConfigName());
        preSourceConfig.setConfigType(req.getConfigType());
        preSourceConfig.setUserAccount(req.getUserAccount());
        if (Objects.nonNull(dbConfig.getConfigType()))
            updateConfig(dbConfig, Boolean.FALSE);
        preSourceConfig.setHost(dbConfig.getHost());
        preSourceConfig.setPort(dbConfig.getPort());
        preSourceConfig.setUsername(dbConfig.getUsername());
        preSourceConfig.setPassword(dbConfig.getPassword());
        preSourceConfig.setDbName(dbConfig.getDbName());
        preSourceConfigService.updateById(preSourceConfig);

        preSourceConfigAttachService.remove(new QueryWrapper<PreSourceConfigAttach>().eq("config_id", req.getId()));
        List<PreSourceConfigAttach> attachList = dbConfig.getAttachment().entrySet().stream().map(entry ->
                PreSourceConfigAttach.builder()
                        .configId(preSourceConfig.getId())
                        .name(entry.getKey())
                        .value(entry.getValue())
                        .build()
        ).collect(Collectors.toList());
        preSourceConfigAttachService.saveBatch(attachList);
    }

    @Override
    public ConnectionTaskVO queryConnectionTask(String operationId) {
        ConnectionTaskVO vo = new ConnectionTaskVO();
        // 资产任务
        List<ConnectionTask> jobTaskInfos = sourceConfigService.querySourceTaskList(operationId).stream().map(t -> {
            ConnectionTask bean = Func.toBean(t, ConnectionTask.class);
            bean.setStatusDesc();
            return bean;
        }).collect(Collectors.toList());
        vo.setAssetTasks(jobTaskInfos);

        // 数据权限、风险检测作业
        Map<AssessModuleType, List<ConnectionTask>> coDbSecurityMap = coDbSecurityService.getBaseMapper()
                .queryConnectionTask(operationId).stream().peek(t -> {
            t.setType(Objects.equals(DatabaseJobType.PERMISSION.getCode(), t.getJobType()) ? AssessModuleType.AUTH : AssessModuleType.BASIC);
            t.setStatusDesc();
        }).collect(Collectors.groupingBy(ConnectionTask::getType));
        vo.setAuthorityTasks(coDbSecurityMap.getOrDefault(AssessModuleType.AUTH, new ArrayList<>()));
        vo.setBasicTasks(coDbSecurityMap.getOrDefault(AssessModuleType.BASIC, new ArrayList<>()));

        // 主机漏扫作业
        List<ConnectionTask> scanTasks = scanTaskService.list(new QueryWrapper<ScanTask>().eq("operation_id", operationId)).stream().map(t -> {
            ConnectionTask bean = Func.toBean(t, ConnectionTask.class);
            bean.setHost(t.getTargets());
            bean.setStatusDesc();
            bean.setName(t.getTaskName());
            return bean;
        }).collect(Collectors.toList());
        vo.setScanTasks(scanTasks);

        // 能力市场作业
        List<MkAppJob> appJobList = mkAppJobService.list(new QueryWrapper<MkAppJob>().eq("operation_id", operationId).gt("type", 0));
        vo.setMaskingTasks(buildConnectionTaskList(appJobList, t -> Objects.equals(t.getType(), AbilityType.DESENSITIZATION.getCode())));
        vo.setEncryptTasks(buildConnectionTaskList(appJobList, t -> Objects.equals(t.getType(), AbilityType.ENCRYPT.getCode())));
        vo.setNetworkTasks(buildConnectionTaskList(appJobList, t -> Objects.equals(t.getType(), AbilityType.PCAP.getCode())));
        vo.setAccessTasks(buildAccessTaskList(operationId));

        return vo;
    }

    private List<ConnectionTask> buildAccessTaskList(String operationId) {
        List<CoPermission> permissions = coPermissionMapper.selectList(new QueryWrapper<CoPermission>().eq("operation_id", operationId));
        return permissions.stream().map(t -> {
            ConnectionTask bean = new ConnectionTask();
            bean.setId((long)(int)t.getId());
            bean.setAbilityType(AbilityType.AUTHORITY);
            bean.setName(t.getName());
            bean.setStatus(2);
            bean.setDesc("完成");
            // 设置跳转路径
            bean.setHost(String.format("/#/accessPermissions/%s", t.getId()));
            return bean;
        }).collect(Collectors.toList());
    }

    private List<ConnectionTask> buildConnectionTaskList(List<MkAppJob> appJobList, Predicate<MkAppJob> predicate) {
        return appJobList.stream().filter(predicate).map(t -> {
            ConnectionTask bean = new ConnectionTask();
            bean.setId(Long.parseLong(String.valueOf(t.getId())));
            bean.setAbilityType(AbilityType.getByCode(t.getType()));
            bean.setName(t.getPlanName());
            bean.setStatus(t.getStatus());
            bean.setAppStatusDesc();
            bean.setDetail(null);
            // 设置跳转路径
            bean.setHost(buildJumpPath(t));
            return bean;
        }).collect(Collectors.toList());
    }

    private String buildJumpPath(MkAppJob job) {
        switch (job.getType()) {
            case 1:
            case 2:
                return String.format("/dcas-compare/#/comparisonDetection/detail?id=%s", job.getPlanId());
            case 4:
                return String.format("/pcap/#/discoverable/assetDetails/%s?title=%s", job.getPlanId(), job.getPlanName());
        }
        return null;
    }

    @Override
    public void startAssetJob(Long id) {
        DiscoveryJob discoveryJob = discoveryJobMapper.selectById(id);
        if (Objects.isNull(discoveryJob))
            throw new ServiceException("作业不存在");
        discoveryJobServiceImpl.startJob(discoveryJob.getPlanId());
    }

    @Override
    public void stopAssetJob(Long id) {
        DiscoveryJob discoveryJob = discoveryJobMapper.selectById(id);
        if (Objects.isNull(discoveryJob))
            throw new ServiceException("作业不存在");
        discoveryJobServiceImpl.stopJob(id);
    }

    @Override
    public void fillWorkConfig(WorkConfigFillReq req) {
        if (req.getOperationId().equals("-1"))
            throw new ServiceException("作业ID异常");
        PreSourceConfig preSource = preSourceConfigService.getById(req.getSourceId());
        if (Objects.isNull(preSource))
            throw new ServiceException("数据源配置不存在id:{}", req.getSourceId());
        List<MkAppJob> mkAppJobList = new ArrayList<>();
        String operationName;
        if (req.getOperationId().length() > 10) {
            CoOperation coOperation = coOperationMapper.selectById(req.getOperationId());
            operationName = coOperation.getOperationName();
        } else {
            SecurityOperation securityOperation = securityOperationMapper.selectById(Integer.parseInt(req.getOperationId()));
            operationName = securityOperation.getName();
        }

        if (AssessModuleType.contains(req.getAssessModule(), AssessModuleType.ASSET)) {
            if (Objects.nonNull(req.getAssetConfig())) {
                mkAppJobList.add(MkAppJob.builder()
                        .operationId(req.getOperationId())
                        .type(~AssessModuleType.ASSET.getCode() + 1)
                        .planName(String.format("%s-%s作业-%s", operationName, AssessModuleType.ASSET.name(), System.currentTimeMillis()))
                        .paramIn(req.getAssetConfig())
                        .status(0)
                        .systemId(req.getSystemId())
                        .build());
            } else
                throw new ServiceException("资产配置不能为空");
        }
        if (AbilityType.contains(req.getAbilityModule(), AbilityType.DESENSITIZATION)) {
            if (Objects.nonNull(req.getMaskConfig())) {
                String name = String.format("%s-%s作业-%s", operationName, AbilityType.DESENSITIZATION.getName(), System.currentTimeMillis());
                mkAppJobList.add(MkAppJob.builder()
                        .operationId(req.getOperationId())
                        .type(AbilityType.DESENSITIZATION.getCode())
                        .planName(name)
                        .paramIn(buildMkCompareConfig(req.getMaskConfig(), name))
                        .status(0)
                        .systemId(req.getSystemId())
                        .build());
            } else
                throw new ServiceException("脱敏配置不能为空");
        }
        if (AbilityType.contains(req.getAbilityModule(), AbilityType.ENCRYPT)) {
            if (Objects.nonNull(req.getEncryptConfig())) {
                String name = String.format("%s-%s作业-%s", operationName, AbilityType.ENCRYPT.getName(), System.currentTimeMillis());
                mkAppJobList.add(MkAppJob.builder()
                        .operationId(req.getOperationId())
                        .type(AbilityType.ENCRYPT.getCode())
                        .planName(name)
                        .paramIn(buildMkCompareConfig(req.getEncryptConfig(), name))
                        .status(0)
                        .systemId(req.getSystemId())
                        .build());
            } else
                throw new ServiceException("加密配置不能为空");
        }
        if (AbilityType.contains(req.getAbilityModule(), AbilityType.PCAP)) {
            PcapConfig networkConfig = req.getNetworkConfig();
            if (Objects.nonNull(networkConfig)) {
                String planName = String.format("%s-%s作业-%s", operationName, AbilityType.PCAP.getName(), DateUtil.currentSeconds());
                networkConfig.setName(planName);
                mkAppJobList.add(MkAppJob.builder()
                        .operationId(req.getOperationId())
                        .type(AbilityType.PCAP.getCode())
                        .planName(planName)
                        .paramIn(networkConfig)
                        .status(0)
                        .systemId(req.getSystemId())
                        .build());
            } else
                throw new ServiceException("网络配置不能为空");
        }
        if (CollUtil.isNotEmpty(mkAppJobList))
            mkAppJobService.saveBatch(mkAppJobList);
        // 更新数据源配置
        PreSourceConfig update = new PreSourceConfig();
        update.setId(preSource.getId());
        update.setSystemId(preSource.getSystemId());
        update.setDbName(preSource.getDbName());
        update.setAbilityModule(req.getAbilityModule());
        update.setAccessModule(req.getAssessModule());
        update.setCurrentStep(3);
        update.setAppIds(mkAppJobList.stream().map(m -> m.getId().toString()).collect(Collectors.joining(StrUtil.COMMA)));
        preSourceConfigService.updateById(update);
    }

    private MkCompareConfig buildMkCompareConfig(MkCompareConfig config, String name) {
        PlanBasic planBasic = config.getPlanBasic();
        planBasic.setName(name);
        Integer sourceId = planBasic.getSourceId();
        planBasic.setSourceDatabase(buildMkSourceDataBase(sourceId, "SOURCE_DATABASE"));
        Integer targetId = planBasic.getTargetId();
        planBasic.setTargetDatabase(buildMkSourceDataBase(targetId, "TARGET_DATABASE"));
        return config;
    }

    private SourceDatabase buildMkSourceDataBase(Integer sourceId, String sourceType) {
        PreSourceConfig sourceConfig = preSourceConfigService.getById(sourceId);
        if (Objects.isNull(sourceConfig))
            throw new ServiceException("全局数据源配置不存在id:{}", sourceId);
        SourceDatabase db = new SourceDatabase();
        db.setSourceType(sourceType);
        db.setType(DataSourceType.getType(sourceConfig.getConfigType()).getDataBaseType().pluginId);
        db.setHost(sourceConfig.getHost());
        db.setPort(sourceConfig.getPort());
        db.setInstance(sourceConfig.getDbName());
        db.setDatabaseName(sourceConfig.getDbName());
        db.setUsername(sourceConfig.getUsername());
        db.setPassword(sourceConfig.getPassword());
        List<SourceDatabase.AdvancedItem> attachList = preSourceConfigAttachService.list(new QueryWrapper<PreSourceConfigAttach>().eq("config_id", sourceId)).stream().map(a -> {
            SourceDatabase.AdvancedItem item = new SourceDatabase.AdvancedItem();
            item.setCode(a.getName());
            item.setValue(a.getValue());
            return item;
        }).collect(Collectors.toList());
        db.setAdvancedItems(attachList);
        return db;
    }

    @Override
    public Integer copyGlobalSourceConfig(Integer sourceId, String operationId) {
        PreSourceConfig sourceConfig = preSourceConfigService.getById(sourceId);
        if (Objects.isNull(sourceConfig))
            throw new ServiceException("全局数据源配置不存在id:{}", sourceId);
        sourceConfig.setId(null);
        sourceConfig.setOperationId(operationId);
        preSourceConfigService.save(sourceConfig);
        List<PreSourceConfigAttach> attachList = preSourceConfigAttachService.list(new QueryWrapper<PreSourceConfigAttach>().eq("config_id", sourceId));
        if (CollUtil.isNotEmpty(attachList)) {
            for (PreSourceConfigAttach attach : attachList) {
                attach.setId(null);
                attach.setConfigId(sourceConfig.getId());
            }
        }
        preSourceConfigAttachService.saveBatch(attachList);
        return sourceConfig.getId();
    }

    @Override
    public List<MkCompareColumnVO> listSourceTableColumn(CompareColumnQuery query) {
        List<MkCompareColumnVO> result = new ArrayList<>();
        List<CustomMutableSchema> sourceColumnList = queryMutableColumn(query.getSourceId(), query.getSourceSchema(), query.getSourceTable());
        if (CollUtil.isEmpty(sourceColumnList))
            return result;
        List<Column> sourceColumns = sourceColumnList.get(0).getTable(0).getColumns();
        List<CustomMutableSchema> targetColumnList = queryMutableColumn(query.getTargetId(), query.getTargetSchema(), query.getTargetTable());
        List<Column> targetColumns = new ArrayList<>();
        if (CollUtil.isNotEmpty(targetColumnList)) {
            targetColumns = targetColumnList.get(0).getTables().get(0).getColumns();
        }
        for (int i = 0; i < sourceColumns.size(); i++) {
            MkCompareColumnVO vo = new MkCompareColumnVO();
            Column sourceColumn = sourceColumns.get(i);
            Column targetColumn = null;
            if (i < targetColumns.size())
                targetColumn = targetColumns.get(i);
            vo.setName(sourceColumn.getName());
            vo.setSourceUniqueKey(sourceColumn.isPrimaryKey());
            if (Objects.nonNull(targetColumn)) {
                vo.setTargetName(targetColumn.getName());
                vo.setTargetUniqueKey(targetColumn.isPrimaryKey());
            }
            result.add(vo);
        }
        return result;
    }

    @Override
    public List<ColumnVO> listColumns(Integer id, String schema, String table) {
        List<CustomMutableSchema> customMutableSchemas = queryMutableColumn(id, schema, table);
        if (CollUtil.isEmpty(customMutableSchemas))
            return new ArrayList<>();
        return customMutableSchemas.get(0).getTables().get(0).getColumns().stream().map(c -> {
            ColumnVO columnVO = new ColumnVO();
            columnVO.setSchema(schema);
            columnVO.setTable(table);
            columnVO.setColumn(c.getName());
            columnVO.setIsPrimaryKey(c.isPrimaryKey());
            return columnVO;
        }).collect(Collectors.toList());
    }

    private List<CustomMutableSchema> queryMutableColumn(Integer sourceId, String schema, String table) {
        PreSourceConfig preSourceConfig = preSourceConfigService.getById(sourceId);
        if (Objects.isNull(preSourceConfig))
            throw new ServiceException("数据源配置不存在id:{}", sourceId);
        DataSourceParam param = toSourceConfigUsable(preSourceConfig);
        param.getAttachment().put("schema", schema);
        param.getAttachment().put("table", table);
        return databaseFactory.getColumns(param);
    }

    private SchemaTableDTO newSourceMetaTableDTO(TableVO table) {
        String schemaAndTableName = table.getSchema() + StrPool.DOT + table.getTableName();
        return SchemaTableDTO.builder().schemaAndTableName(schemaAndTableName).schemaName(table.getSchema())
                .tableName(table.getTableName()).used(0).build();
    }

    @Override
    public DataSourceParam toSourceConfigUsable(PreSourceConfig config) {
        DataSourceParam param = toSourceConfigParam(config);
        param.setPassword(SecurityUtils.decryptAes(param.getPassword()));
        DatabaseOptionUtil.updateConfigOption(null, param, DatabaseOptionUtil.Scheme.STORAGE_USABLE, getFileParsingFun());
        return param;
    }

    private DataSourceParam toSourceConfigParam(PreSourceConfig config) {
        DataSourceParam param = Func.toBean(config, DataSourceParam.class);
        DataSourceType type = DataSourceType.getType(config.getConfigType());
        param.setSourceType(type.getType());
        param.setDataBaseType(type.getDataBaseType());
        param.setAttachment(getSourceConfigAttachment(type, config));
        return param;
    }

    private Map<String, String> getSourceConfigAttachment(DataSourceType type, PreSourceConfig config) {
        PreSourceConfigAttach attachDO = new PreSourceConfigAttach();
        attachDO.setConfigId(config.getId());
        List<PreSourceConfigAttach> list = preSourceConfigAttachService.list(new QueryWrapper<PreSourceConfigAttach>().eq("config_id", config.getId()));
        if (CollUtil.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream().collect(Collectors.toMap(PreSourceConfigAttach::getName, PreSourceConfigAttach::getValue));
    }

    private DataSourceParam toSourceConfigUsable(SourceConfig config) {
        DataSourceParam param = toSourceConfigParam(config);
        param.setPassword(SecurityUtils.decryptAes(param.getPassword()));
        DatabaseOptionUtil.updateConfigOption(null, param, DatabaseOptionUtil.Scheme.STORAGE_USABLE, getFileParsingFun());
        return param;
    }

    private DataSourceParam toSourceConfigParam(SourceConfig config) {
        DataSourceParam param = Func.toBean(config, DataSourceParam.class);
        DataSourceType type = DataSourceType.getType(config.getConfigType());
        param.setSourceType(type.getType());
        param.setDataBaseType(type.getDataBaseType());
        param.setAttachment(getSourceConfigAttachment(type, config));
        param.setPassword(SecurityUtils.decryptAes(param.getPassword()));
        return param;
    }

    private Map<String, String> getSourceConfigAttachment(DataSourceType type, SourceConfig config) {
        SourceConfigAttach attachDO = new SourceConfigAttach();
        attachDO.setConfigId(config.getId());
        List<SourceConfigAttach> list = sourceConfigAttachService.list(new QueryWrapper<SourceConfigAttach>().eq("config_id", config.getId()));
        if (CollUtil.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream().collect(Collectors.toMap(SourceConfigAttach::getName, SourceConfigAttach::getValue));
    }

    private List<Long> getJobIds(List<SourceConfig> sourceConfig, String operationId, LabelEnum labelEnum) {
        List<Long> ids = new ArrayList<>();
        // 1 - 数据库权限查询作业； 2 - 数据库风险检测作业
        DatabaseJobType jobType = labelEnum == LabelEnum.SJQX ? DatabaseJobType.PERMISSION : DatabaseJobType.RISK;
        if (operationId.length() < 10) {
            SecurityOperation securityOperation = securityOperationMapper.selectById(Integer.parseInt(operationId));
            if (Objects.isNull(securityOperation))
                throw new ServiceException("作业不存在");
        } else {
            // 作业是否选择对应模块
            CoOperation coOperation = coOperationMapper.selectById(operationId);
            if (Objects.isNull(coOperation))
                throw new ServiceException("作业不存在");
            List<Long> serviceIds = StrUtil.split(coOperation.getServiceContent(), StrUtil.COMMA)
                    .stream().mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            if (!serviceIds.contains(labelEnum.getCode()))
                return ids;
        }
        List<CoDbSecurity> beans = new ArrayList<>();
        // 查询未启动的作业重新执行
        Map<Integer, List<CoDbSecurity>> configJobMap = coDbSecurityService.list(new QueryWrapper<CoDbSecurity>()
                .eq("operation_id", operationId).eq("status", 0)).stream().collect(Collectors.groupingBy(CoDbSecurity::getConfigId));
        for (SourceConfig config : sourceConfig) {
            if (configJobMap.containsKey(config.getId())) {
                List<CoDbSecurity> coDbSecurity = configJobMap.get(config.getId());
                // 如果当前数据源连接已经存在对应模块的作业，则不再创建，收集作业id用于重新启动
                Optional<CoDbSecurity> optional = coDbSecurity.stream().filter(c -> Objects.equals(c.getJobType(), jobType.getCode())).findFirst();
                if (optional.isPresent()) {
                    ids.add(optional.get().getId());
                    continue;
                }
            }
            // 数据源连接新建对应模块作业
            beans.add(CoDbSecurity.builder()
                    .name(jobType.getName() + "-" + config.getConfigName())
                    .operationId(operationId)
                    .configId(config.getId())
                    .jobType(jobType.getCode())
                    .dbType(config.getConfigType())
                    .status(JobStatusEnum.UN_START.getCode())
                    .createBy(SecurityUtils.getAccount())
                    .createAt(DateUtil.date())
                    .build());
        }
        coDbSecurityService.saveBatch(beans);
        ids.addAll(beans.stream().map(CoDbSecurity::getId).collect(Collectors.toList()));
        return ids;
    }

    public DatasourceParam newDatasourceParam(DataSourceParam param) {
        if (!enabled) {
            log.warn("未设置 mcdatasource 程序位置");
            return null;
        }
        Properties properties = new Properties();
        Map<String, String> attachment = param.getAttachment();
        if (CollUtil.isNotEmpty(attachment)) {
            String auth = MapUtil.getStr(attachment, MetaConst.AUTH);
            if (Authentication.KERBEROS.name().equalsIgnoreCase(auth)) {
                properties.setProperty(DatasourceConstant.KEY_DB_LOGIN_KEYTAB_PATH,
                        MapUtil.getStr(attachment, MetaConst.KEYTAB_PATH));
                properties.setProperty(DatasourceConstant.KEY_DB_LOGIN_PRINCIPAL, MapUtil.getStr(attachment, MetaConst.PRINCIPAL));
                properties.setProperty(DatasourceConstant.KEY_DB_KRB5_PATH, MapUtil.getStr(attachment, MetaConst.KRB5_PATH));
                properties.setProperty(DatasourceConstant.KEY_DB_HIVE_PRINCIPAL,
                        MapUtil.getStr(attachment, MetaConst.SERVICE_PRINCIPAL_NAME));
                if (DataBaseType.HBASE.equals(param.getDataBaseType())) {
                    properties.setProperty(DatasourceConstant.KEY_DB_KRB5_HBASE_MASTER_KERBEROS_PRINCIPAL,
                            MapUtil.getStr(attachment, MetaConst.HABSE_MASTER));
                    properties.setProperty(DatasourceConstant.KEY_DB_KRB5_HBASE_REGIONSERVER_KERBEROS_PRINCIPAL,
                            MapUtil.getStr(attachment, MetaConst.HABSE_REGIONSERVER));
                }
            }
            if (Authentication.SSL.name().equalsIgnoreCase(auth)) {
                properties.setProperty(DatasourceConstant.SSL, DatasourceConstant.SSL_TRUE);
                properties.setProperty(DatasourceConstant.SSL_TRUST_STORE_PATH,
                        MapUtil.getStr(attachment, MetaConst.CLIENT_CERTIFICATE));
                properties.setProperty(DatasourceConstant.SSL_TRUST_STORE_PASSWORD,
                        MapUtil.getStr(attachment, MetaConst.CLIENT_KEY));
            }

            for (Map.Entry<String, String> entry : attachment.entrySet()) {
                properties.setProperty(entry.getKey(), entry.getValue());
            }
        }

        return DatasourceParam.builder().type(param.getDataBaseType().pluginId).host(param.getHost())
                .port(String.valueOf(param.getPort())).db(ObjectUtil.defaultIfNull(param.getDbName(), StrUtil.EMPTY))
                .user(param.getUsername()).pass(param.getPassword()).sql(param.getSql()).advanced(properties).build();
    }

    private void addProperties(DataSourceParam param) {
        Long configType = param.getConfigType();
        if (Objects.equals(DataSourceType.MYSQL.getCode(), configType) //
            || Objects.equals(DataSourceType.MYSQL5.getCode(), configType)) {
            if (!param.getAttachment().containsKey("useUnicode")) {
                param.getAttachment().put("useUnicode", "true");
            }
            if (!param.getAttachment().containsKey("characterEncoding")) {
                param.getAttachment().put("characterEncoding", "UTF-8");
            }
            if (!param.getAttachment().containsKey("yearIsDateType")) {
                param.getAttachment().put("yearIsDateType", "false");
            }
            if (!param.getAttachment().containsKey("zeroDateTimeBehavior")) {
                param.getAttachment().put("zeroDateTimeBehavior", "convertToNull");
            }
            if (!param.getAttachment().containsKey("serverTimezone")) {
                param.getAttachment().put("serverTimezone", "GMT+8");
            }
        } else if (Objects.equals(DataSourceType.ODPS.getCode(), configType)) {
            if (!param.getAttachment().containsKey("project_name")) {
                param.getAttachment().put("project_name", param.getDbName());
            }
        }
    }

    private Function<String, String> getFileParsingFun() {
        return FileUtils::readFile;
    }

    @Override
    public String buildKey(DataSourceParam param) {
        return buildKey(param.getHost(), param.getPort(), param.getUsername());
    }

    private String buildKey(Object... str) {
        return StrUtil.join(StrUtil.COMMA, str);
    }

    @Data
    @Builder
    static class DatasourceParam {
        private String type;
        private String host;
        private String db;
        private String port;
        private String user;
        private String pass;
        private String sql;
        private Properties advanced;
    }
}
