package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.hash.MurmurHash;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.DataScope;
import com.dcas.common.annotation.DataValidator;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.entity.SysUser;
import com.dcas.common.enums.*;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.exception.file.InvalidExtensionException;
import com.dcas.common.model.req.SecurityReportCreateReq;
import com.dcas.common.utils.*;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.param.FileRelationParam;
import com.dcas.common.model.param.SecurityEditParam;
import com.dcas.common.model.param.SecurityWorkParam;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.SecurityReportReq;
import com.dcas.common.model.req.SecuritySearchReq;
import com.dcas.common.model.vo.*;
import com.dcas.system.factory.SecurityReportCreateFactory;
import com.dcas.system.manager.DynamicSchemaManager;
import com.dcas.common.mapper.*;
import com.dcas.system.service.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.mchz.dcas.client.enums.NodeType;
import com.mchz.dcas.client.model.dto.*;
import com.mchz.dcas.client.model.treeNode.CheckBranch;
import com.mchz.dcas.client.model.treeNode.CheckLeaf;
import com.mchz.dcas.client.model.treeNode.CheckNode;
import com.mchz.dcas.client.util.ScoreCalculateContext;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/3 11:08
 * @since 1.4.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecurityOperationServiceImpl implements SecurityOperationService {
    private static final String TITLE = "以上均不符合";
    private static final String NO = "不符合";
    private static final String YES = "符合";
    private static final String PART = "部分符合";
    private static final String NOT_APPLICABLE = "不适用";

    private final SecurityTemplateMapper securityTemplateMapper;
    private final SecurityOperationMapper securityOperationMapper;
    private final CommonService commonService;
    private final ISysUpgradeService sysUpgradeService;

    private final CoFileServiceImpl coFileService;
    private final QuestionnaireServiceImpl questionnaireService;
    private final DynamicProcessTreeServiceImpl dynamicProcessTreeService;
    private final SecurityProcessLabelServiceImpl securityProcessLabelService;
    private final SecurityQuestionnaireServiceImpl securityQuestionnaireService;
    private final TagMapper tagMapper;
    private final ItemMapper itemMapper;
    private final SysUserMapper sysUserMapper;
    private final ISysConfigService sysConfigService;
    private final DetectionResultMapper detectionResultMapper;
    private final SecurityQuestionnaireMapper securityQuestionnaireMapper;
    private final CoSystemMapper coSystemMapper;

    @Override
    @SchemaSwitch
    public Integer create(SecurityWorkParam param) {
        // 重名作业校验
        QueryWrapper<SecurityOperation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", param.getName());
        SecurityOperation securityOperation = securityOperationMapper.selectOne(queryWrapper);
        if (Objects.nonNull(securityOperation))
            throw new ServiceException("作业名称已存在");
        SecurityTemplate securityTemplate = securityTemplateMapper.selectById(param.getTemplateId());
        if (Objects.isNull(securityTemplate))
            throw new ServiceException("调研模板不存在");
        String version = sysUpgradeService.needSync().getVersion();
        SecurityOperation operation = SecurityOperation.builder()
                .name(param.getName())
                .projectId(param.getProjectId())
                .region(param.getRegion())
                .industry(param.getIndustry())
                .templateId(param.getTemplateId())
                .templateName(securityTemplate.getName())
                .serviceContent(param.getServiceContent())
                .progress(0)
                .status((byte) 1)
                .executorAccount(param.getExecutorAccount())
                .version(version)
                .build();
        Func.beforeInsert(operation);
        operation.setTemplateScore(securityTemplate.getScore());
        securityOperationMapper.insert(operation);
        DynamicSchemaManager.addVersion(version, operation.getId().toString());
        return operation.getId();
    }

    @Override
    @DataScope(deptAlias ="t1", userAlias = "t1", extra = "job-executor")
    public PageResult<SecurityWorkVO> pageQuery(SecuritySearchReq req) {
        try (Page<Object> page = PageHelper.startPage(req.getCurrentPage(), req.getPageSize())) {
            List<SecurityWorkVO> securityWorks = securityOperationMapper.pageQuery(req);
            if (CollUtil.isEmpty(securityWorks))
                return PageResult.ofEmpty();
            List<String> securityIds = securityWorks.stream().map(s -> s.getId().toString()).collect(Collectors.toList());
            Map<String, List<DynamicProcessTree>> labelGroup = dynamicProcessTreeService.list(
                    new QueryWrapper<DynamicProcessTree>().in("operation_id", securityIds)).stream().collect(Collectors.groupingBy(DynamicProcessTree::getOperationId));
            for (SecurityWorkVO work : securityWorks) {
                work.setBusSystem(labelGroup.getOrDefault(work.getId().toString(), new ArrayList<>()).stream().sorted(
                        Comparator.comparing(DynamicProcessTree::getOrderNum)).map(DynamicProcessTree::getTreeName).collect(Collectors.joining(StrUtil.COMMA)));
            }
            return PageResult.ofPage(page.getTotal(), securityWorks);
        }
    }

    @Override
    public void edit(SecurityEditParam param) {
        SecurityOperation securityOperation = securityOperationMapper.selectById(param.getId());
        if (Objects.isNull(securityOperation))
            throw new ServiceException("作业不存在");
        // 重名作业校验
        QueryWrapper<SecurityOperation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", param.getName());
        queryWrapper.ne("id", param.getId());
        SecurityOperation operation = securityOperationMapper.selectOne(queryWrapper);
        if (Objects.nonNull(operation))
            throw new ServiceException("作业名称已存在");
        SecurityOperation bean = BeanUtil.copyProperties(param, SecurityOperation.class);
        Func.beforeUpdate(bean);
        securityOperationMapper.updateById(bean);
    }

    @Override
    public void delete(IdsReq req) {
        securityOperationMapper.updateByBatchIds(req.getIds());
        // 删除检查过程清单
        QueryWrapper<SecurityProcessLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("security_id", req.getIds());
        securityProcessLabelService.remove(queryWrapper);
        // 删除检查内容
        QueryWrapper<SecurityQuestionnaire> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.in("security_id", req.getIds());
        securityQuestionnaireService.remove(queryWrapper1);
        req.getIds().stream().map(String::valueOf).forEach(DynamicSchemaManager::removeVersionWork);
    }

    @Override
    public List<SecurityLabelVO> execute(Integer id) {
        List<SecurityLabelVO> res = new ArrayList<>();
        QueryWrapper<SecurityProcessLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("security_id", id);
        List<SecurityProcessLabel> securityProcessLabels = securityProcessLabelService.list(queryWrapper);
        if (CollUtil.isEmpty(securityProcessLabels))
            return res;
        securityProcessLabels.stream().collect(Collectors.groupingBy(SecurityProcessLabel::getModel))
                .forEach((modelName, categories) -> {
                    SecurityLabelVO vo = SecurityLabelVO.builder()
                            .model(modelName)
                            .categories(categories.stream().collect(Collectors.groupingBy(s -> {
                                        SecurityCategoryDTO securityCategoryDTO = BeanUtil.copyProperties(s, SecurityCategoryDTO.class);
                                        securityCategoryDTO.setCategorySort(s.getSort());
                                        return securityCategoryDTO;
                                    })).entrySet().stream().map(e -> SecurityLabelVO.Category.builder()
                                            .categoryName(e.getKey().getCategory())
                                            .sort(e.getKey().getCategorySort())
                                            .finished(e.getValue().stream().allMatch(SecurityProcessLabel::getFinished))
                                            .build()).sorted(Comparator.comparing(SecurityLabelVO.Category::getSort))
                                    .collect(Collectors.toList()))
                            .build();
                    res.add(vo);
                });
        List<String> sortList = Lists.newArrayList(SecurityContentType.SECURITY.getName(), SecurityContentType.SENSITIVE.getName(), SecurityContentType.BASIC.getName());
        res.sort(Comparator.comparing(o -> sortList.indexOf(o.getModel())));

        SecurityLabelVO dataSecurityCheck = res.stream()
                .filter(vo -> "数据安全检查".equals(vo.getModel()))
                .findFirst()
                .orElse(null);

        if (dataSecurityCheck != null) {
            // 从其 categories 中找到名称为 "数据安全管理" 的 Category
            SecurityLabelVO.Category dataSecurityCategory = dataSecurityCheck.getCategories().stream()
                    .filter(category -> "数据安全管理".equals(category.getCategoryName()))
                    .findFirst()
                    .orElse(null);

            if (dataSecurityCategory != null) {
                // 找到名称为 "基础安全检查" 的 SecurityLabelVO
                SecurityLabelVO baseSecurityCheck = res.stream()
                        .filter(vo -> SecurityContentType.BASIC.getName().equals(vo.getModel()))
                        .findFirst()
                        .orElse(null);

                if (baseSecurityCheck != null) {
                    // 添加到基础安全检查的 categories 中
                    baseSecurityCheck.getCategories().add(dataSecurityCategory);

                    // 从原来的列表中删除数据安全管理的 Category
                    dataSecurityCheck.getCategories().removeIf(
                            category -> "数据安全管理".equals(category.getCategoryName())
                    );
                }
            }
        }

        return res;
    }

    @Override
    @SchemaSwitch(Integer.class)
    public List<SecurityContentVO> content(Integer id, String categoryName) {
        List<SecurityContentVO> res = new ArrayList<>();
        QueryWrapper<SecurityProcessLabel> labelQueryWrapper = new QueryWrapper<>();
        labelQueryWrapper.eq("security_id", id);
        labelQueryWrapper.eq("category", categoryName);
        List<SecurityProcessLabel> contentLabel = securityProcessLabelService.list(labelQueryWrapper);
        if (CollUtil.isEmpty(contentLabel))
            return res;
        // 核查项能力标签
        final Map<String, String> abilityItemMap = itemMapper.selectList(new QueryWrapper<Item>().eq("status", 0))
                .stream().filter(i -> StrUtil.isNotEmpty(i.getAbility())).collect(Collectors.toMap(Item::getId, Item::getAbility));
        QueryWrapper<SecurityQuestionnaire> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("label_id", contentLabel.stream().map(SecurityProcessLabel::getId).collect(Collectors.toList()));
        List<SecurityQuestionnaire> securityQuestionnaires = securityQuestionnaireService.list(queryWrapper);
        SecurityOperation securityOperation = securityOperationMapper.selectById(id);
        Map<String, Set<FileLocateDTO>> stringListMap = questionnaireService.itemFileRelation(
                new FileRelationParam(id.toString(), securityOperation.getRegion(), securityOperation.getIndustry(), Boolean.TRUE));

        for (SecurityProcessLabel label : contentLabel) {
            SecurityContentVO vo = SecurityContentVO.builder()
                    .contentId(label.getId())
                    .contentName(label.getContent())
                    .needUpload(label.getNeedUpload())
                    .question(securityQuestionnaires.stream().filter(q -> Objects.equals(q.getLabelId(), label.getId()))
                            .collect(Collectors.groupingBy(s -> BeanUtil.copyProperties(s, SecurityContentVO.Question.class)))
                            .entrySet().stream().map(e -> SecurityContentVO.Question.builder()
                                    .problem(e.getKey().getProblem())
                                    .point(e.getKey().getPoint())
                                    .type(e.getKey().getType())
                                    .sort(e.getKey().getSort())
                                    .remark(e.getKey().getRemark())
                                    .inapplicable(e.getValue().stream().allMatch(SecurityQuestionnaire::getInapplicable))
                                    // 不同分组下可能会存在相同检查项，前台只展示一遍
                                    .item(e.getValue().stream().collect(Collectors.groupingBy(i ->
                                            BeanUtil.copyProperties(i, SecurityContentVO.ItemBase.class))).entrySet().stream().map(en -> {
                                        Set<FileLocateDTO> fileLocateDTOS = stringListMap.get(en.getKey().getItemId());
                                        Set<String> reference = new HashSet<>();
                                        if (Objects.nonNull(fileLocateDTOS) && !fileLocateDTOS.isEmpty())
                                            reference = fileLocateDTOS.stream().map(FileLocateDTO::getFileName).collect(Collectors.toSet());
                                        return SecurityContentVO.Item.builder()
                                                // 返回检查项的id集合，如果没有相同的检查项就只有一个元素，多个检查项有各自的id
                                                .ids(en.getValue().stream().map(SecurityQuestionnaire::getId).collect(Collectors.toList()))
                                                .itemId(en.getKey().getItemId())
                                                .itemTitle(en.getKey().getItemTitle())
                                                .itemDesc(en.getKey().getItemDesc())
                                                .referenceFile(reference)
                                                .checked(en.getKey().getChecked())
                                                .inapplicable(en.getKey().getInapplicable())
                                                .abilityIds(abilityItemMap.get(en.getKey().getItemId()))
                                                .build();
                                    }).sorted(Comparator.comparing(SecurityContentVO.Item::getItemId).reversed()).collect(Collectors.toList()))
                                    .build())
                            .collect(Collectors.toList()))
                    .build();
            res.add(vo);
        }

        // 将res根据question#sort排序
        res.sort(Comparator.comparing(o -> o.getQuestion().get(0).getSort()));
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveContent(SecurityContentDTO dto) {
        saveAndCommit(dto, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editProgress(SecurityContentDTO dto) {
        saveAndCommit(dto, 2);
        // 计算作业进度
        QueryWrapper<SecurityProcessLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("security_id", dto.getOperationId());
        List<SecurityProcessLabel> securityProcessLabels = securityProcessLabelService.list(queryWrapper);
        Map<String, List<SecurityProcessLabel>> map = securityProcessLabels.stream().collect(Collectors.groupingBy(s -> s.getModel() + s.getCategory()));
        // map value List中所有finished为true则加1，否则不加，计数
        long finishedCount = map.values().stream().filter(l -> l.stream().allMatch(SecurityProcessLabel::getFinished)).count();
        SecurityOperation update = new SecurityOperation();
        update.setId(dto.getOperationId());
        int progress = (int) (finishedCount * 100 / map.size());
        update.setProgress(progress);
        Func.beforeUpdate(update);
        securityOperationMapper.updateById(update);
    }

    /**
     * type 1 - 保存；2 - 提交
     * 针对type的不同，SecurityProcessLabel的finished字段不同
     * 保存取决于SecurityProcessLabel的finished字段
     * 提交为TRUE
     */
    private void saveAndCommit(SecurityContentDTO dto, int type) {
        QueryWrapper<SecurityProcessLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("security_id", dto.getOperationId());
        queryWrapper.eq("category", dto.getCategoryName());
        List<SecurityProcessLabel> securityProcessLabel = securityProcessLabelService.list(queryWrapper);
        if (CollUtil.isEmpty(securityProcessLabel))
            throw new ServiceException("检查类别内容不存在");
        if (type == 2) {
            List<SecurityProcessLabel> updateList = securityProcessLabel.stream().map(s -> SecurityProcessLabel.builder()
                            .id(s.getId())
                            .finished(Boolean.TRUE)
                            .build())
                    .collect(Collectors.toList());
            securityProcessLabelService.updateBatchById(updateList);
        }
        List<SecurityQuestionnaire> questionnaires = dto.getContent().stream().flatMap(o ->
                o.getQuestion().stream().flatMap(
                        q -> q.getItem().stream().flatMap(i ->
                            i.getIds().stream().map(id -> SecurityQuestionnaire.builder()
                                    .id(id)
                                    .remark(q.getRemark())
                                    .checked(i.getChecked())
                                    .inapplicable(q.getInapplicable())
                                    .build())
                                    .collect(Collectors.toList()).stream()
                        )))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(questionnaires))
            securityQuestionnaireService.saveOrUpdateBatch(questionnaires);
        SecurityOperation securityOperation = securityOperationMapper.selectById(dto.getOperationId());
        if (securityOperation.getStatus() == 1) {
            securityOperationMapper.updateStateById(securityOperation.getId(), (byte) 2);
        }
    }

    @DataValidator(type = TemplateTypeEnum.SECURITY_TEMPLATE)
    @Override
    @SchemaSwitch
    public List<SecurityTemplate> getSecurityTemplateList(TemplateQueryDTO dto) {
        QueryWrapper<SecurityTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable", Boolean.TRUE);
        List<SecurityTemplate> securityTemplates = securityTemplateMapper.selectList(queryWrapper);
        List<String> industryList = StrUtil.split(dto.getIndustry(), StrUtil.COMMA);
        final Map<String, String> industryMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("type_id", 2)
                .eq("status", 0)).stream().collect(Collectors.toMap(t -> t.getId().toString(), Tag::getName));
        return securityTemplates.stream().filter(t -> CoOperationServiceImpl.templateFilter(
                dto.getRegion(), industryList, t.getTags(), t.getRegionCode(), t.getIndustryCode(), industryMap)).collect(Collectors.toList());
    }

    @Override
    @SneakyThrows
    @SchemaSwitch(value = SecurityReportReq.class)
    public void exportWord(HttpServletResponse response, SecurityReportReq req) {
        Integer operationId = req.getOperationId();
        SecurityOperation securityOperation = securityOperationMapper.selectById(operationId);
        if (Objects.isNull(securityOperation))
            throw new ServiceException("安全检查作业不存在");

        SecurityCategoryResultVO result = result(req.getOperationId(), null);
        SecurityReportCreateFactory.create(new SecurityReportCreateReq(securityOperation, req.getChartList(), response, result));
    }

    @Override
    public void fileDownload(Integer id, HttpServletResponse response) {
        List<String> fileIds = securityOperationMapper.selectFileIdsBySecurityId(id);
        coFileService.fileDownload(fileIds, "安全检查作业文件压缩包.zip", response);
    }

    @Override
    public List<ContentFileVO> contentFile(Integer id, String categoryName) {
        QueryWrapper<SecurityProcessLabel> labelQueryWrapper = new QueryWrapper<>();
        labelQueryWrapper.eq("security_id", id);
        labelQueryWrapper.eq("category", categoryName);
        labelQueryWrapper.eq("need_upload", Boolean.TRUE);
        List<SecurityProcessLabel> contentLabel = securityProcessLabelService.list(labelQueryWrapper);
        if (CollUtil.isEmpty(contentLabel))
            return null;
        List<String> fileIds = contentLabel.stream().filter(c -> StrUtil.isNotEmpty(c.getFileIds()))
                .flatMap(c -> StrUtil.split(c.getFileIds(), StrUtil.COMMA).stream()).collect(Collectors.toList());
        Map<String, CoFile> fileMap = new HashMap<>();
        if (CollUtil.isNotEmpty(fileIds))
            fileMap = coFileService.listByIds(fileIds).stream().collect(Collectors.toMap(CoFile::getFileId, f -> f));
        final Map<String, CoFile> finalFileMap = fileMap;
        return contentLabel.stream().map(c -> ContentFileVO.builder()
                .contentId(c.getId())
                .contentName(c.getContent())
                .files(StrUtil.split(c.getFileIds(), StrUtil.COMMA).stream().map(f -> {
                    ContentFileVO.File file = BeanUtil.copyProperties(finalFileMap.get(f), ContentFileVO.File.class);
                    file.setName(file.getOriginalFileName());
                    return file;
                }).collect(Collectors.toList()))
                .build())
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> uploadContentFile(Integer contentId, MultipartFile file) throws IOException, InvalidExtensionException {
        SecurityProcessLabel securityProcessLabel = securityProcessLabelService.selectById(contentId);
        if (Objects.isNull(securityProcessLabel))
            throw new ServiceException("检查类别内容不存在");
        Map<String, Object> map = commonService.uploadFile(file, securityProcessLabel.getSecurityId().toString());
        String fileId = MapUtil.getStr(map, "fileId");
        SecurityProcessLabel update = new SecurityProcessLabel();
        update.setId(contentId);
        update.setFileIds(StrUtil.isEmpty(securityProcessLabel.getFileIds()) ? fileId : securityProcessLabel.getFileIds().concat(StrUtil.COMMA).concat(fileId));
        securityProcessLabelService.updateById(update);
        return map;
    }

    @Override
    public void deleteContentFile(Integer contentId, String fileId) {
        SecurityProcessLabel securityProcessLabel = securityProcessLabelService.selectById(contentId);
        if (Objects.isNull(securityProcessLabel))
            throw new ServiceException("检查类别内容不存在");
        PrimaryKeyDTO dto = new PrimaryKeyDTO();
        dto.setId(fileId);
        commonService.deleteFile(new RequestModel<>(dto));
        // 删除fileIds中的fileId
        String fileIds = securityProcessLabel.getFileIds();
        if (StrUtil.isEmpty(fileIds))
            return;
        List<String> fileIdList = StrUtil.split(fileIds, StrUtil.COMMA);
        fileIdList.remove(fileId);
        SecurityProcessLabel update = new SecurityProcessLabel();
        update.setId(contentId);
        update.setFileIds(StrUtil.join(StrUtil.COMMA, fileIdList));
        securityProcessLabelService.updateById(update);
    }

    @Override
    @SchemaSwitch(Integer.class)
    public SecurityCategoryResultVO result(Integer operationId, String categoryName) {
        SecurityOperation securityOperation = securityOperationMapper.selectById(operationId);
        SecurityTemplate securityTemplate = securityTemplateMapper.selectById(securityOperation.getTemplateId());
        List<ItemDTO> items = securityQuestionnaireService.selectByOperationId(operationId, categoryName, Boolean.FALSE);
        // 不适用的核查项
        List<ItemDTO> inInapplicableItem = securityQuestionnaireService.selectByOperationId(operationId, categoryName, Boolean.TRUE);
        if (Objects.nonNull(securityTemplate.getAttachType()) && securityTemplate.getAttachType() == 1) {
            // 不适用默认当成符合逻辑处理
            List<ItemDTO> collect = inInapplicableItem.stream().peek(i -> i.setCheckable(Boolean.TRUE)).collect(Collectors.toList());
            items.addAll(collect);
        } else if (Objects.nonNull(securityTemplate.getAttachType()) && securityTemplate.getAttachType() == 2) {
            // 默认不符合当成不符合处理
            List<ItemDTO> collect = inInapplicableItem.stream().peek(i -> i.setCheckable(Boolean.FALSE)).collect(Collectors.toList());
            items.addAll(collect);
        }
        if (CollUtil.isEmpty(items)) {
            // 这段的逻辑一定是attachType = 3 且 检查项全部为不适用
            List<ItemDTO> collect = inInapplicableItem.stream().peek(i -> i.setCheckable(Boolean.FALSE)).collect(Collectors.toList());
            CheckBranch root = (CheckBranch) ScoreCalculateContext.createScoreTreeByQuestionnaire(collect);
            List<CheckBranch> categoryNodes = new ArrayList<>();
            // 收集所有category节点
            collectCategoryNodes(root, categoryNodes);
            return buildInapplicableResult(operationId, categoryNodes);
        }
        CheckBranch root = (CheckBranch) ScoreCalculateContext.createScoreTreeByQuestionnaire(items);
        if (Objects.isNull(securityTemplate.getAttachType()) || securityTemplate.getAttachType() == 3) {
            ScoreCalculateContext.addNodesToRoot(root, inInapplicableItem);
        }
        Map<String, String> itemRiskMap = securityTemplateMapper.selectRiskItem(securityTemplate.getId(), categoryName)
                .stream().collect(Collectors.toMap(ItemRiskDTO::getTitle, ItemRiskDTO::getDescribe));
        List<CheckBranch> categoryNodes = new ArrayList<>();
        // 收集所有category节点
        collectCategoryNodes(root, categoryNodes);
        // 报告中的顺序与执行作业中的标签顺序一致
        List<String> sortNameList = execute(operationId).get(0).getCategories().stream().map(SecurityLabelVO.Category::getCategoryName).collect(Collectors.toList());
        List<SecurityCategoryResultVO.CategoryResult> categoryResult = categoryNodes.stream().map(c -> SecurityCategoryResultVO.CategoryResult.builder()
                        .categoryName(c.getName())
                        .categoryScore(Objects.isNull(c.getCalculateScore()) ? BigDecimal.ZERO : c.getCalculateScore().setScale(1, RoundingMode.HALF_UP))
                        .categoryTotalScore(Objects.isNull(c.getConfigScore()) ? BigDecimal.ZERO : c.getConfigScore().setScale(0, RoundingMode.HALF_UP))
                        .completeRate(Objects.isNull(c.getConfigScore()) ? BigDecimal.valueOf(100) : c.getCalculateScore().divide(c.getConfigScore(), 1, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)))
                        .contentResults(buildCategoryContent(c.getItems(), itemRiskMap))
                        .build())
                .sorted(Comparator.comparing(c -> sortNameList.indexOf(c.getCategoryName()))).collect(Collectors.toList());
        List<SecurityCategoryResultVO.ContentResult> contentResults = categoryResult.stream().flatMap(c -> c.getContentResults().stream()).collect(Collectors.toList());
        long noCount = contentResults.stream().filter(c -> Objects.equals(c.getResult(), NO)).count();
        long yesCount = contentResults.stream().filter(c -> Objects.equals(c.getResult(), YES)).count();
        long partCount = contentResults.stream().filter(c -> Objects.equals(c.getResult(), PART)).count();
        SecurityCategoryResultVO.ContentRate contentRate = SecurityCategoryResultVO.ContentRate.builder()
                .total(contentResults.size())
                .non((int) noCount)
                .full((int) yesCount)
                .part((int) partCount)
                .nonRate(NumberUtil.round(BigDecimal.valueOf(noCount).divide(BigDecimal.valueOf(contentResults.size()), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue(), 1).doubleValue())
                .fullRate(NumberUtil.round(BigDecimal.valueOf(yesCount).divide(BigDecimal.valueOf(contentResults.size()), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue(), 1).doubleValue())
                .partRate(NumberUtil.round(BigDecimal.valueOf(partCount).divide(BigDecimal.valueOf(contentResults.size()), 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue(), 1).doubleValue())
                .build();
        BigDecimal score = categoryResult.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.getCategoryScore()), BigDecimal::add).setScale(1, RoundingMode.HALF_UP);
        BigDecimal totalScore = categoryResult.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.getCategoryTotalScore()), BigDecimal::add).setScale(1, RoundingMode.HALF_UP);
        SecurityCategoryResultVO vo = new SecurityCategoryResultVO();
        // score 保留两位小数
        vo.setScore(score);
        vo.setTotalScore(totalScore);
        vo.setContentRate(contentRate);
        vo.setCategoryResults(categoryResult);
        return vo;
    }

    private SecurityCategoryResultVO buildInapplicableResult(Integer operationId, List<CheckBranch> categoryNodes) {
        List<String> sortNameList = execute(operationId).get(0).getCategories().stream().map(SecurityLabelVO.Category::getCategoryName).collect(Collectors.toList());
        List<SecurityCategoryResultVO.CategoryResult> categoryResult = categoryNodes.stream().map(c -> SecurityCategoryResultVO.CategoryResult.builder()
                        .categoryName(c.getName())
                        .categoryScore(BigDecimal.ZERO)
                        .categoryTotalScore(BigDecimal.ZERO)
                        .completeRate(BigDecimal.valueOf(100))
                        .contentResults(buildInapplicableCategoryContent(c.getItems()))
                        .build())
                .sorted(Comparator.comparing(c -> sortNameList.indexOf(c.getCategoryName()))).collect(Collectors.toList());
        List<SecurityCategoryResultVO.ContentResult> contentResults = categoryResult.stream().flatMap(c -> c.getContentResults().stream()).collect(Collectors.toList());
        SecurityCategoryResultVO.ContentRate contentRate = SecurityCategoryResultVO.ContentRate.builder()
                .total(contentResults.size())
                .non(0)
                .full(0)
                .part(0)
                .nonRate(0.0)
                .fullRate(0.0)
                .partRate(0.0)
                .build();
        SecurityCategoryResultVO vo = new SecurityCategoryResultVO();
        // score 保留两位小数
        vo.setScore(BigDecimal.ZERO);
        vo.setTotalScore(BigDecimal.ZERO);
        vo.setContentRate(contentRate);
        vo.setCategoryResults(categoryResult);
        return vo;
    }

    @Override
    @SchemaSwitch(Integer.class)
    public SecurityDetailVO detail(Integer id) {
        return securityOperationMapper.selectDetailById(id);
    }

    @Override
    public void editStatus(Integer securityId) {
        SecurityOperation securityOperation = securityOperationMapper.selectById(securityId);
        if (Objects.isNull(securityOperation))
            throw new ServiceException("检查作业不存在");
        if (securityOperation.getStatus() == 2 && securityOperation.getProgress() == 100) {
            SecurityOperation update = new SecurityOperation();
            update.setId(securityId);
            update.setStatus((byte) 3);
            Func.beforeUpdate(update);
            securityOperationMapper.updateById(update);
        }
    }

    private List<SecurityCategoryResultVO.ContentResult> buildCategoryContent(List<CheckNode> items, Map<String, String> itemRiskMap) {
        List<SecurityCategoryResultVO.ContentResult> res = new ArrayList<>();
        Map<String, List<CheckNode>> contentMap = items.stream().collect(Collectors.groupingBy(p -> StrUtil.split(p.getName(), StrUtil.AT).get(0)));
        contentMap.forEach((contentName, v) -> {
            BigDecimal calculateScore = v.stream().map(CheckNode::getCalculateScore).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
            BigDecimal configScore = v.stream().map(CheckNode::getConfigScore).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
            String result = getResult(calculateScore, configScore);
            boolean flag = Objects.equals(YES, result) || Objects.equals(NOT_APPLICABLE, result);
            SecurityCategoryResultVO.ContentResult contentResult = SecurityCategoryResultVO.ContentResult.builder()
                    .contentName(contentName)
                    .contentScore(NumberUtil.round(calculateScore, 1))
                    .contentTotalScore(NumberUtil.round(calculateScore, 1))
                    .result(result)
                    .trouble(flag ? null : getTrouble(v.stream().flatMap(c -> ((CheckBranch) c).getItems().stream()).collect(Collectors.toList()), itemRiskMap))
                    .build();
            res.add(contentResult);
        });
        return res;
    }

    private List<SecurityCategoryResultVO.ContentResult> buildInapplicableCategoryContent(List<CheckNode> items) {
        List<SecurityCategoryResultVO.ContentResult> res = new ArrayList<>();
        Map<String, List<CheckNode>> contentMap = items.stream().collect(Collectors.groupingBy(p -> StrUtil.split(p.getName(), StrUtil.AT).get(0)));
        contentMap.forEach((contentName, v) -> {
            SecurityCategoryResultVO.ContentResult contentResult = SecurityCategoryResultVO.ContentResult.builder()
                    .contentName(contentName)
                    .contentScore(BigDecimal.ZERO)
                    .contentTotalScore(BigDecimal.ZERO)
                    .result(NOT_APPLICABLE)
                    .trouble(null)
                    .build();
            res.add(contentResult);
        });
        return res;
    }

    private String getTrouble(List<CheckNode> nodes, Map<String, String> itemRiskMap) {
        // node instanceof packet
        List<CheckLeaf> leaves = nodes.stream().flatMap(n ->
                ((CheckBranch) n).getItems().stream()).map(i -> (CheckLeaf) i).collect(Collectors.toList());
        return leaves.stream().filter(l -> !l.getCheckable() && !l.getName().contains(TITLE))
                .map(l -> itemRiskMap.get(l.getName())).filter(Objects::nonNull).collect(Collectors.joining("；"));
    }

    private String getResult(BigDecimal calculateScore, BigDecimal configScore) {
        if (configScore.doubleValue() == 0)
            return NOT_APPLICABLE;
        if (calculateScore.doubleValue() == 0)
            return NO;
        if (calculateScore.doubleValue() == configScore.doubleValue())
            return YES;
        return PART;
    }

    private void collectCategoryNodes(CheckNode node, List<CheckBranch> categoryNodes) {
        if (node instanceof CheckLeaf)
            return;
        CheckBranch branch = (CheckBranch) node;
        if (branch.getNodeType() == NodeType.CATEGORY) {
            categoryNodes.add(branch);
        } else {
            branch.getItems().forEach(item -> collectCategoryNodes(item, categoryNodes));
        }
    }


    /*
     * 根据模板id生成检查过程清单
     */
    @Override
    @SchemaSwitch(value = SecurityProduceDTO.class)
    public void produce(SecurityProduceDTO dto) {
        List<SecurityQuestionnaireDTO> securityProcessLabels = securityTemplateMapper.selectByTemplateId(dto.getTemplateId());
        if (CollUtil.isEmpty(securityProcessLabels))
            return;
        List<CheckNode> leaves = new ArrayList<>();
        int operationId = Integer.parseInt(dto.getOperationId());
        List<ItemDTO> items = securityTemplateMapper.queryScoreParamById(dto.getTemplateId());
        CheckNode checkNode = ScoreCalculateContext.createScoreTreeByTemplate(items);
        collectLeaves(checkNode, leaves);
        Map<Integer, CheckNode> nodeMap = leaves.stream().collect(Collectors.toMap(CheckNode::getHash, l -> l));
        Set<SecurityQuestionnaire> res = new HashSet<>();
        securityProcessLabels.stream().collect(Collectors.groupingBy(l -> BeanUtil.toBean(l, SecurityLabelDTO.class)))
                .forEach((k, v) -> {
                    SecurityProcessLabel bean = BeanUtil.toBean(k, SecurityProcessLabel.class);
                    bean.setSort(k.getCategorySort());
                    bean.setModel(SecurityContentType.SECURITY.getName());
                    bean.setSecurityId(operationId);
                    bean.setNeedUpload(needUpload(v));
                    bean.setFinished(Boolean.FALSE);
                    securityProcessLabelService.save(bean);
                    List<SecurityQuestionnaire> questionnaires = v.stream().map(va -> SecurityQuestionnaire.builder()
                            .securityId(operationId)
                            .labelId(bean.getId())
                            .problem(va.getProblem())
                            .relation(va.getRelation())
                            .point(va.getPoint())
                            .type(va.getType())
                            .sort(va.getContentSort())
                            .itemId(va.getItemId())
                            .itemTitle(va.getItemTitle())
                            .packet(va.getPacket())
                            .score(nodeMap.get(MurmurHash.hash32(va.getCategory() + va.getContent() + StrUtil.AT + va.getProblem() + va.getPacket() + va.getItemTitle())).getConfigScore().doubleValue())
                            .itemDesc(va.getItemDesc())
                            .checked(Boolean.FALSE)
                            .build()
                    ).collect(Collectors.toList());
                    res.addAll(questionnaires);
                });
        List<SecurityProcessLabel> result = Lists.newArrayList();
        List<TemplateServiceContentDTO> serviceContentList = securityOperationMapper.selectServiceContentByType(1);
        Map<Integer, TemplateServiceContentDTO> parentMap = serviceContentList.stream().filter(s -> s.getParentId() != 0)
            .collect(Collectors.toMap(TemplateServiceContentDTO::getKey, Function.identity()));
        Map<Integer, String> parentIdNameMap = serviceContentList.stream().filter(p -> p.getParentId() == 0)
            .collect(Collectors.toMap(TemplateServiceContentDTO::getId, TemplateServiceContentDTO::getValue));
        List<String> selectedContentList = StrUtil.split(dto.getServiceContent(), StrUtil.COMMA);
        for (String la : selectedContentList) {
            // 能力市场技术能力不展示在过程清单
            if (AbilityType.DESENSITIZATION.getCode() == Integer.parseInt(
                la) || AbilityType.ENCRYPT.getCode() == Integer.parseInt(
                la) || AbilityType.PCAP.getCode() == Integer.parseInt(
                la) || AbilityType.AUTHORITY.getCode() == Integer.parseInt(la)) {
                continue;
            }
            if (parentMap.containsKey(Integer.parseInt(la))) {
                TemplateServiceContentDTO templateServiceContentDTO = parentMap.get(Integer.parseInt(la));
                result.add(buildWithLabel(operationId, parentIdNameMap.get(templateServiceContentDTO.getParentId()),
                    templateServiceContentDTO.getValue(), templateServiceContentDTO.getSort()));
            }
        }

        // 创建检查作业业务系统
        if (StrUtil.isNotEmpty(dto.getBusSystem())) {
            Map<String, Integer> systemMap = coSystemMapper.selectList(
                    new QueryWrapper<>()).stream().collect(Collectors.toMap(CoSystem::getName, CoSystem::getId));
            List<DynamicProcessTree> labelList = new ArrayList<>();
            List<String> systemList = StrUtil.split(dto.getBusSystem(), StrUtil.COMMA);
            AtomicLong index = new AtomicLong(1);
            for (String bus : systemList) {
                DynamicProcessTree label = new DynamicProcessTree();
                label.setOperationId(dto.getOperationId());
                label.setTreeId((long)(int)systemMap.get(bus));
                label.setParentId(0L);
                label.setAncestors("0");
                label.setTreeName(bus);
                label.setOrderNum((int)index.getAndIncrement());
                label.setSrcTreeId(LabelEnum.XTDY.getCode());
                labelList.add(label);
            }
            PartitionUtils.part(labelList, dynamicProcessTreeService::saveBatch);
        }
        PartitionUtils.part(result, securityProcessLabelService::saveBatch);

        securityQuestionnaireService.saveBatch(res);
    }

    private void collectLeaves(CheckNode node, List<CheckNode> nodeList) {
        CheckBranch branch = (CheckBranch) node;
        if (branch.getNodeType().getParentNode() == NodeType.PROBLEM
                && CollUtil.isNotEmpty(branch.getItems())) {
            nodeList.addAll(branch.getItems());
        } else {
            branch.getItems().forEach(item -> collectLeaves(item, nodeList));
        }
    }

    private SecurityProcessLabel buildWithLabel(Integer operationId, String parentName, String label, int sort) {
        return SecurityProcessLabel.builder()
                .securityId(operationId)
                .model(parentName)
                .category(label)
                .content(label)
                .sort(sort)
                .needUpload(Boolean.FALSE)
                .finished(Boolean.FALSE)
                .build();
    }

    private boolean needUpload(List<SecurityQuestionnaireDTO> list) {
        return list.stream().anyMatch(l -> (l.getType() & 2) == 2);
    }

    @Override
    public void deleteByProjectIds(List<String> projectIds) {
        projectIds.forEach(projectId -> {
           List<Integer> ids = securityOperationMapper.selectOperationIdsByProjectId(projectId);
           if (CollUtil.isEmpty(ids)){
               return;
           }
           IdsReq idsReq = new IdsReq();
           idsReq.setIds(ids);
           delete(idsReq);
        });
    }

    @Override
    public void flushBasic(Integer id) {
        List<SecurityBasicVO> securityBasicVOList = securityProcessLabelService.getBaseMapper().queryBasicList(id, "数据安全管理");
        if (CollUtil.isEmpty(securityBasicVOList)) {
            log.warn("无基础安全检测模块");
            return;
        }
        Map<String, Map<String, Integer>> result = securityBasicVOList.stream()
                .collect(Collectors.groupingBy(
                        SecurityBasicVO::getContent,
                        Collectors.toMap(
                                SecurityBasicVO::getItemTitle,
                                SecurityBasicVO::getQuestionnaireId,
                                (existing, replacement) -> existing
                        )
                ));

        List<DetectionResult> detectionResults = detectionResultMapper.selectList(new QueryWrapper<DetectionResult>().eq("operation_id", id.toString()));
        Map<String, List<DetectionResult>> detectionResultGroup = detectionResults.stream().collect(Collectors.groupingBy(DetectionResult::getOption));
        List<SecurityQuestionnaire> res = new ArrayList<>();
        for (Map.Entry<String, Map<String, Integer>> entry : result.entrySet()) {
            String content = entry.getKey();
            Map<String, Integer> value = entry.getValue();
            List<DetectionResult> detectionResultList = detectionResultGroup.get(content);
            SecurityQuestionnaire matched = new SecurityQuestionnaire();
            SecurityQuestionnaire partMatched = new SecurityQuestionnaire();
            SecurityQuestionnaire nonMatched = new SecurityQuestionnaire();
            if (CollUtil.isEmpty(detectionResultList)) {
                matched.setId(value.get("符合"));
                matched.setChecked(false);
                partMatched.setId(value.get("部分符合"));
                partMatched.setChecked(false);
                nonMatched.setId(value.get("以上均不符合"));
                nonMatched.setChecked(true);
            } else {
                Set<Boolean> accordSet = detectionResultList.stream().map(DetectionResult::getAccord).collect(Collectors.toSet());
                if (accordSet.size() == 1) {
                    if (accordSet.contains(true)) {
                        matched.setId(value.get("符合"));
                        matched.setChecked(true);
                        partMatched.setId(value.get("部分符合"));
                        partMatched.setChecked(false);
                        nonMatched.setId(value.get("以上均不符合"));
                        nonMatched.setChecked(false);
                    } else {
                        matched.setId(value.get("符合"));
                        matched.setChecked(false);
                        partMatched.setId(value.get("部分符合"));
                        partMatched.setChecked(false);
                        nonMatched.setId(value.get("以上均不符合"));
                        nonMatched.setChecked(true);
                    }
                } else {
                    matched.setId(value.get("符合"));
                    matched.setChecked(false);
                    partMatched.setId(value.get("部分符合"));
                    partMatched.setChecked(true);
                    nonMatched.setId(value.get("以上均不符合"));
                    nonMatched.setChecked(false);
                }
            }
            res.add(matched);
            res.add(partMatched);
            res.add(nonMatched);
        }
        PartitionUtils.part(res, securityQuestionnaireService::updateBatchById);
    }

    @SchemaSwitch
    @Override
    public List<Map<Integer, String>> listServiceContent() {
        return securityOperationMapper.selectServiceContent(1,1);
    }

    @SchemaSwitch
    @Override
    public Object pdfDownload(Integer templateId, HttpServletRequest request) {
        String pdfPath = securityTemplateMapper.selectPdfPathByTemplateId(templateId);
        return Download.download(pdfPath);
    }
}
