package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.enums.TemplateTypeEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.LibraryTemplateConfigMapper;
import com.dcas.common.utils.PageResult;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.domain.entity.AnalysisTemplate;
import com.dcas.common.domain.entity.Standard;
import com.dcas.common.domain.entity.StandardTemplateRelevance;
import com.dcas.common.model.param.AnalysisTempParam;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.TemplateSearchReq;
import com.dcas.common.model.vo.AnalysisTempVO;
import com.dcas.common.mapper.AnalysisTemplateMapper;
import com.dcas.common.mapper.StandardTemplateRelevanceMapper;
import com.dcas.system.service.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/6 16:49
 * @since 1.2.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnalysisTemplateServiceImpl extends ServiceImpl<AnalysisTemplateMapper, AnalysisTemplate> implements AnalysisTemplateService {

    private final AnalysisTemplateMapper analysisTemplateMapper;
    private final StandardTemplateRelevanceMapper standardTemplateRelevanceMapper;

    private final StandardService standardService;
    private final StandardTempRelevanceService standardTempRelevanceService;
    private final CommonService commonService;
    private final LibraryTemplateConfigService libraryTemplateConfigService;

    @SchemaSwitch
    @Override
    public PageResult<AnalysisTemplate> list(Integer currentPage, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(currentPage, pageSize);
        QueryWrapper<AnalysisTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("id");
        List<AnalysisTemplate> analysisTemplates = analysisTemplateMapper.selectList(queryWrapper);
        return PageResult.ofPage(page.getTotal(), analysisTemplates);
    }

    @Override
    public void add(AnalysisTempParam param) {
        AnalysisTemplate template = addAnalysisTemplate(param);
        saveRelevance(param, template);
    }

    private AnalysisTemplate addAnalysisTemplate(AnalysisTempParam param) {
        if (templateExists(param.getName()))
            throw new ServiceException("模板已存在，请勿重复添加");
        AnalysisTemplate template = AnalysisTemplate.builder()
                .name(param.getName())
                .introduce(param.getIntroduce())
                .tags(param.getTags())
                .type(param.getType())
                .version(param.getVersion())
                .understand(Boolean.FALSE)
                .enable(Boolean.TRUE)
                .createTime(new Date())
                .createBy(SecurityUtils.getAccount())
                .build();
        analysisTemplateMapper.insert(template);
        return template;
    }

    private void saveRelevance(AnalysisTempParam param, AnalysisTemplate template) {
        List<StandardTemplateRelevance> list = param.getExist().stream().map(s ->
                StandardTemplateRelevance.builder()
                        .templateId(template.getId())
                        .standardId(s)
                        .build()
        ).collect(Collectors.toList());
        standardTempRelevanceService.saveList(list);
    }

    @Override
    public void update(AnalysisTempParam param) {
        AnalysisTemplate template = BeanUtil.copyProperties(param, AnalysisTemplate.class);
        template.setUpdateTime(new Date());
        template.setUpdateBy(SecurityUtils.getAccount());
        analysisTemplateMapper.updateById(template);
        QueryWrapper<StandardTemplateRelevance> delete = new QueryWrapper<>();
        delete.eq("template_id", template.getId());
        standardTemplateRelevanceMapper.delete(delete);
        saveRelevance(param, template);
    }

    @Override
    public void delete(IdsReq req) {
        QueryWrapper<AnalysisTemplate> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("understand", Boolean.FALSE);
        deleteWrapper.in("id", req.getIds());
        analysisTemplateMapper.delete(deleteWrapper);
        QueryWrapper<StandardTemplateRelevance> delete = new QueryWrapper<>();
        delete.in("template_id", req.getIds());
        standardTemplateRelevanceMapper.delete(delete);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefault(Integer id) {
        int count = standardTemplateRelevanceMapper.selectFileByTemplateId(id);
        if (count == 0)
            throw new ServiceException("当前模板没有关联标准文件，无法设置为默认模板");
        analysisTemplateMapper.updateStatus();
        AnalysisTemplate build = AnalysisTemplate.builder().id(id).understand(Boolean.TRUE).enable(Boolean.TRUE).build();
        analysisTemplateMapper.updateById(build);
    }

    @Override
    @SchemaSwitch
    public void enable(Integer id) {
        AnalysisTemplate template = analysisTemplateMapper.selectById(id);
        if (Objects.nonNull(template) && Boolean.TRUE.equals(template.getUnderstand()))
            throw new ServiceException("默认模板无法禁用");
        AnalysisTemplate build = AnalysisTemplate.builder().id(id).enable(!template.getEnable()).build();
        analysisTemplateMapper.updateById(build);
        //更新关联表状态
        libraryTemplateConfigService.updateStatus(id, !template.getEnable(), TemplateTypeEnum.ANALYSIS_TEMPLATE.getType());
    }

    @Override
    public void createBak(AnalysisTempParam param) {
        AnalysisTemplate template = addAnalysisTemplate(param);
        QueryWrapper<StandardTemplateRelevance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", param.getId());
        List<StandardTemplateRelevance> standardTemplateRelevance = standardTemplateRelevanceMapper.selectList(queryWrapper);
        standardTemplateRelevance.forEach(s -> s.setTemplateId(template.getId()));
        standardTempRelevanceService.saveList(standardTemplateRelevance);
    }

    @Override
    public AnalysisTempVO details(Integer id) {
        List<Standard> standards = standardService.listAll();
        List<Standard> exist = Lists.newArrayList();
        List<Standard> exclude = Lists.newArrayList();
        AnalysisTemplate analysisTemplate = analysisTemplateMapper.selectById(id);
        QueryWrapper<StandardTemplateRelevance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", id);
        List<Integer> existStandards = standardTemplateRelevanceMapper.selectList(queryWrapper)
                .stream().map(StandardTemplateRelevance::getStandardId).collect(Collectors.toList());
        AnalysisTempVO param = BeanUtil.copyProperties(analysisTemplate, AnalysisTempVO.class);
        standards.forEach(s -> {
            Standard standard = Standard.builder().id(s.getId()).name(s.getName()).build();
            if (existStandards.contains(s.getId()))
                exist.add(standard);
            else
                exclude.add(standard);
        });
        param.setExist(exist);
        param.setExclude(exclude);
        return param;
    }

    @Override
    public List<AnalysisTemplate> search(TemplateSearchReq req) {
        return analysisTemplateMapper.search(req);
    }

    private boolean templateExists(String name) {
        QueryWrapper<AnalysisTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        return analysisTemplateMapper.selectCount(queryWrapper) > 0;
    }
}
