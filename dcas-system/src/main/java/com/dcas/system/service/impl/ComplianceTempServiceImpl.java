package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.enums.TemplateTypeEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.PageResult;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.domain.entity.ComplianceTemplate;
import com.dcas.common.domain.entity.Law;
import com.dcas.common.domain.entity.LawTemplateRelevance;
import com.dcas.common.model.param.ComplianceTemParam;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.TemplateSearchReq;
import com.dcas.common.model.vo.ComplianceTemVO;
import com.dcas.common.mapper.ComplianceTemplateMapper;
import com.dcas.common.mapper.LawTemplateRelevanceMapper;
import com.dcas.system.service.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/2 17:09
 * @since 1.2.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ComplianceTempServiceImpl extends ServiceImpl<ComplianceTemplateMapper, ComplianceTemplate> implements ComplianceTempService {

    private final LawService lawService;
    private final LawTemplateRelevanceService lawTemplateRelevanceService;

    private final LawTemplateRelevanceMapper lawTemplateRelevanceMapper;
    private final ComplianceTemplateMapper complianceTemplateMapper;
    private final CommonService commonService;
    private final LibraryTemplateConfigService libraryTemplateConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCompliance(ComplianceTemParam param) {
        ComplianceTemplate template = add(param);
        saveLawRelevance(param, template);
    }

    private ComplianceTemplate add(ComplianceTemParam req) {
        int count = complianceTemplateMapper.selectCountByName(req.getName());
        if (count != 0)
            throw new ServiceException("合规模板已存在");
        Date date = new Date();
        String username = SecurityUtils.getAccount();
        ComplianceTemplate template = ComplianceTemplate.builder()
                .name(req.getName())
                .introduce(req.getIntroduce())
                .tags(req.getTags())
                .version(req.getVersion())
                .understand(Boolean.FALSE)
                .enable(Boolean.TRUE)
                .createTime(date)
                .createBy(username)
                .updateTime(date)
                .updateBy(username)
                .build();
        complianceTemplateMapper.insert(template);
        return template;
    }

    @SchemaSwitch
    @Override
    public PageResult<ComplianceTemplate> list(Integer currentPage, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(currentPage, pageSize);
        QueryWrapper<ComplianceTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("id");
        List<ComplianceTemplate> list = complianceTemplateMapper.selectList(queryWrapper);
        return PageResult.ofPage(page.getTotal(), list);
    }

    @Override
    public void updateCompliance(ComplianceTemParam param) {
        ComplianceTemplate template = BeanUtil.copyProperties(param, ComplianceTemplate.class);
        template.setUpdateTime(new Date());
        template.setUpdateBy(SecurityUtils.getAccount());
        complianceTemplateMapper.updateById(template);
        QueryWrapper<LawTemplateRelevance> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("template_id", template.getId());
        lawTemplateRelevanceMapper.delete(deleteWrapper);
        saveLawRelevance(param, template);
    }

    private void saveLawRelevance(ComplianceTemParam param, ComplianceTemplate template) {
        List<LawTemplateRelevance> lawTemplateRelevance = param.getExist().stream().map(r ->
                LawTemplateRelevance.builder()
                        .templateId(template.getId())
                        .docId(r)
                        .build()
        ).collect(Collectors.toList());
        lawTemplateRelevanceService.saveList(lawTemplateRelevance);
    }

    @Override
    public void deleteCompliance(IdsReq req) {
        QueryWrapper<ComplianceTemplate> delete = new QueryWrapper<>();
        delete.eq("understand", Boolean.FALSE);
        delete.in("id", req.getIds());
        complianceTemplateMapper.delete(delete);
        QueryWrapper<LawTemplateRelevance> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.in("template_id", req.getIds());
        lawTemplateRelevanceMapper.delete(deleteWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefault(Integer templateId) {
        int count = complianceTemplateMapper.selectFileByTemplateId(templateId);
        if (count == 0)
            throw new ServiceException("当前模板没有关联法规文件，无法设置为默认模板");
        complianceTemplateMapper.updateStatus();
        ComplianceTemplate template = ComplianceTemplate.builder()
                .id(templateId).understand(Boolean.TRUE).enable(Boolean.TRUE).build();
        complianceTemplateMapper.updateById(template);
    }

    @Override
    @SchemaSwitch
    public void enableCompliance(Integer templateId) {
        ComplianceTemplate template = complianceTemplateMapper.selectById(templateId);
        if (Objects.nonNull(template) && template.getUnderstand())
            throw new ServiceException("默认模板无法禁用");
        ComplianceTemplate build = ComplianceTemplate.builder().id(templateId).enable(!template.getEnable()).build();
        complianceTemplateMapper.updateById(build);
        //更新关联表状态
        libraryTemplateConfigService.updateStatus(templateId, !template.getEnable(), TemplateTypeEnum.LAW_TEMPLATE.getType());
    }

    @Override
    public void createBak(ComplianceTemParam req) {
        ComplianceTemplate template = add(req);
        QueryWrapper<LawTemplateRelevance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", req.getId());
        List<LawTemplateRelevance> lawTemplateRelevance = lawTemplateRelevanceMapper.selectList(queryWrapper);
        lawTemplateRelevance.forEach(l -> l.setTemplateId(template.getId()));
        lawTemplateRelevanceService.saveList(lawTemplateRelevance);
    }

    @Override
    public ComplianceTemVO details(Integer templateId) {
        List<Law> laws = lawService.listAll();
        List<Law> exist = Lists.newArrayList();
        List<Law> exclude = Lists.newArrayList();
        ComplianceTemplate template = complianceTemplateMapper.selectById(templateId);
        QueryWrapper<LawTemplateRelevance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templateId);
        List<Integer> existLawIds = lawTemplateRelevanceMapper.selectList(queryWrapper)
                .stream().map(LawTemplateRelevance::getDocId).collect(Collectors.toList());
        ComplianceTemVO param = BeanUtil.copyProperties(template, ComplianceTemVO.class);
        laws.forEach(l -> {
            Law build = Law.builder().id(l.getId()).name(l.getName()).build();
            if (existLawIds.contains(l.getId()))
                exist.add(build);
            else
                exclude.add(build);
        });
        param.setExist(exist);
        param.setExclude(exclude);
        return param;
    }

    @Override
    public List<ComplianceTemplate> search(TemplateSearchReq req) {
        return complianceTemplateMapper.search(req);
    }
}
