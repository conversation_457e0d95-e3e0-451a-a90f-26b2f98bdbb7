package com.dcas.system.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.domain.entity.TaskStep;
import com.dcas.common.enums.JobStatusEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.TaskStepMapper;
import com.dcas.common.model.dto.StepMsgDTO;
import com.dcas.common.model.other.InParam;
import com.dcas.common.model.other.OutParam;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.model.vo.IntegrationInterfaceVO;
import com.dcas.common.utils.DateUtils;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.params.ResponseConverter;
import com.dcas.system.service.ITaskStepService;
import com.dcas.system.service.IApiCallService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 任务步骤Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskStepServiceImpl extends ServiceImpl<TaskStepMapper, TaskStep> implements ITaskStepService {

    private final IApiCallService apiCallService;

    /**
     * 查询任务步骤
     * 
     * @param stepId 任务步骤主键
     * @return 任务步骤
     */
    @Override
    public TaskStep selectTaskStepByStepId(Long stepId) {
        return baseMapper.selectTaskStepByStepId(stepId);
    }

    /**
     * 查询任务步骤列表
     * 
     * @param taskStep 任务步骤
     * @return 任务步骤
     */
    @Override
    public List<TaskStep> selectTaskStepList(TaskStep taskStep) {
        return baseMapper.selectTaskStepList(taskStep);
    }

    /**
     * 根据任务ID查询步骤列表
     * 
     * @param taskId 任务ID
     * @return 步骤列表
     */
    @Override
    public List<TaskStep> selectTaskStepByTaskId(Long taskId) {
        return baseMapper.selectTaskStepByTaskId(taskId);
    }

    /**
     * 新增任务步骤
     * 
     * @param taskStep 任务步骤
     * @return 结果
     */
    @Override
    public int insertTaskStep(TaskStep taskStep) {
        taskStep.setCreateTime(DateUtils.getNowDate());
        taskStep.setCreateBy(getUsername());
        taskStep.setStatus(JobStatusEnum.UN_START.getCode());
        taskStep.setRetryCount(0);
        
        if (taskStep.getMaxRetries() == null) {
            taskStep.setMaxRetries(3);
        }
        if (taskStep.getTimeoutSeconds() == null) {
            taskStep.setTimeoutSeconds(30);
        }
        
        return baseMapper.insertTaskStep(taskStep);
    }

    /**
     * 修改任务步骤
     * 
     * @param taskStep 任务步骤
     * @return 结果
     */
    @Override
    public int updateTaskStep(TaskStep taskStep) {
        return baseMapper.updateTaskStep(taskStep);
    }

    /**
     * 批量删除任务步骤
     * 
     * @param stepIds 需要删除的任务步骤主键
     * @return 结果
     */
    @Override
    public int deleteTaskStepByStepIds(Long[] stepIds) {
        return baseMapper.deleteTaskStepByStepIds(stepIds);
    }

    /**
     * 删除任务步骤信息
     * 
     * @param stepId 任务步骤主键
     * @return 结果
     */
    @Override
    public int deleteTaskStepByStepId(Long stepId) {
        return baseMapper.deleteTaskStepByStepId(stepId);
    }

    /**
     * 根据任务ID删除所有步骤
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int deleteTaskStepByTaskId(Long taskId) {
        return baseMapper.deleteTaskStepByTaskId(taskId);
    }

    /**
     * 执行单个步骤
     * 
     * @param step 步骤
     * @param previousStepOutput 前一步骤的输出数据
     * @return 执行结果
     */
    @Override
    public StepMsgDTO executeStep(TaskStep step, TaskStep nextStep, String previousStepOutput, IntegrationInterfaceVO interfaceParam) {
        log.info("开始执行步骤，步骤ID: {}", step.getId());

        Long stepId = step.getId();
        Date startTime = DateUtils.getNowDate();
        
        try {
            // 更新步骤状态为执行中
            updateStepStatus(stepId, JobStatusEnum.RUNNING.getCode());

            List<IntegrationFormFieldVO> params = new ArrayList<>();
            if (Objects.nonNull(interfaceParam)) {
                params = interfaceParam.getConfigParams().stream().filter(p -> Objects.equals(p.getParamType(), 1)).collect(Collectors.toList());
            }
            // 上一个步骤的输出需要这个步骤作为接口的输入的数据
            if (StrUtil.isNotBlank(previousStepOutput)) {
                List<IntegrationFormFieldVO> previousResult = JSONUtil.toList(previousStepOutput, IntegrationFormFieldVO.class);
                params.addAll(previousResult);
            }

            // 执行API调用
            IApiCallService.ApiCallResult result = apiCallService.executeApiCall(step, params);
            
            Date endTime = DateUtils.getNowDate();
            long duration = endTime.getTime() - startTime.getTime();
            
            // 记录执行时间
            recordStepExecutionTime(stepId, startTime, endTime, duration);
            
            if (result.isSuccess()) {
                // 如果下一步不为空，则中间步骤不存储全量内容，根据筛选字段筛选然后只保存下一个接口调用所需要用到的内容
                OutParam outParam = JSONUtil.toBean(step.getResponseFormat(), OutParam.class);
                if (Objects.nonNull(nextStep)) {
                    // 当前接口配置的出参中，参数要求为筛选条件的参数，用于筛选一部分需要的响应内容
                    List<IntegrationFormFieldVO> filterParam = interfaceParam.getConfigParams().stream().filter(p ->
                            Objects.equals(p.getParamType(), 2)).collect(Collectors.toList());
                    InParam nextInParams = JSONUtil.toBean(nextStep.getRequestParams(), InParam.class);
                    // 下个接口请求入参中上个接口的参数来源，中间接口只保留下个接口需要用到的结果
                    List<InParam.ColumnParam> saveParams = nextInParams.getColumnList().stream().filter(p ->
                            Objects.equals(p.getType(), 2)).collect(Collectors.toList());
                    List<IntegrationFormFieldVO> convertedFields = ResponseConverter.convertToFormFields(result.getResponseData(), outParam, filterParam, saveParams);
                    // 将转换后的字段数据作为输出数据存储，供下一个接口使用
                    if (!convertedFields.isEmpty()) {
                        // 验证参数类型匹配性，防止类型不匹配导致下个接口调用失败
                        validateParameterTypes(convertedFields, saveParams, stepId, step.getStepName());

                        String outputData = JSONUtil.toJsonStr(convertedFields);
                        recordStepOutput(stepId, outputData);
                        log.info("步骤 {} 转换并保存了 {} 个字段用于下一接口", stepId, convertedFields.size());
                    } else {
                        // 过滤出的结果为空，无法满足下个接口的调用
                        log.error("步骤 {} 过滤出的结果为空，无法满足下个接口的调用，步骤名: {}", stepId, step.getStepName());
                        return new StepMsgDTO(Boolean.FALSE, "步骤执行失败，根据筛选条件无法得到预期结果");
                    }
                } else {
                    // 如果没有下一步，过滤并保存配置的显示字段
                    String filteredDisplayData = ResponseConverter.filterDisplayFields(
                            result.getResponseData(), outParam);
                    recordStepOutput(stepId, filteredDisplayData);
                    log.info("步骤 {} 过滤并保存了显示字段数据，原始长度: {}, 过滤后长度: {}",
                            stepId, result.getResponseData().length(), filteredDisplayData.length());
                }
                // 记录成功结果
                recordStepResponse(stepId, result.getStatusCode(), result.getResponseData());
                updateStepStatus(stepId, JobStatusEnum.COMPLETE.getCode());
                
                log.info("步骤执行成功，步骤名: {}, 执行时间: {}ms", step.getStepName(), duration);
                return new StepMsgDTO(Boolean.TRUE, "步骤执行成功");
            } else {
                // 记录失败结果
                recordStepResponse(stepId, result.getStatusCode(), result.getResponseData());
                recordStepError(stepId, result.getErrorMessage());

                updateStepStatus(stepId, JobStatusEnum.EXCEPTION_STOP.getCode());
                log.error("步骤执行失败，步骤ID: {}, 错误: {}", stepId, result.getErrorMessage());
                return new StepMsgDTO(Boolean.FALSE, result.getErrorMessage());
            }
            
        } catch (Exception e) {
            Date endTime = DateUtils.getNowDate();
            long duration = endTime.getTime() - startTime.getTime();
            
            recordStepExecutionTime(stepId, startTime, endTime, duration);
            recordStepError(stepId, "步骤执行异常: " + e.getMessage());
            updateStepStatus(stepId, JobStatusEnum.EXCEPTION_STOP.getCode());
            
            log.error("步骤执行异常，步骤ID: {}", stepId, e);
            return new StepMsgDTO(Boolean.FALSE, e.getMessage());
        }
    }

    /**
     * 获取任务的下一个待执行步骤
     * 
     * @param taskId 任务ID
     * @return 下一个步骤
     */
    @Override
    public TaskStep getNextPendingStep(Long taskId) {
        return baseMapper.selectNextPendingStep(taskId);
    }

    /**
     * 根据任务ID和步骤顺序获取步骤
     * 
     * @param taskId 任务ID
     * @param stepOrder 步骤顺序
     * @return 任务步骤
     */
    @Override
    public TaskStep getTaskStepByOrder(Long taskId, Integer stepOrder) {
        return baseMapper.selectTaskStepByTaskIdAndOrder(taskId, stepOrder);
    }

    /**
     * 更新步骤状态
     * 
     * @param stepId 步骤ID
     * @param status 新状态
     * @return 结果
     */
    @Override
    public boolean updateStepStatus(Long stepId, Integer status) {
        return baseMapper.updateTaskStepStatus(stepId, status, getUsername()) > 0;
    }

    /**
     * 记录步骤执行时间
     * 
     * @param stepId 步骤ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param executionDuration 执行时长
     * @return 结果
     */
    @Override
    public boolean recordStepExecutionTime(Long stepId, Date startTime, Date endTime, Long executionDuration) {
        return baseMapper.updateTaskStepExecutionTime(stepId, startTime, endTime, executionDuration, getUsername()) > 0;
    }

    /**
     * 记录步骤响应信息
     * 
     * @param stepId 步骤ID
     * @param responseStatus 响应状态码
     * @param responseData 响应数据
     * @return 结果
     */
    @Override
    public boolean recordStepResponse(Long stepId, Integer responseStatus, String responseData) {
        return baseMapper.updateTaskStepResponse(stepId, responseStatus, responseData, getUsername()) > 0;
    }

    /**
     * 记录步骤错误信息
     * 
     * @param stepId 步骤ID
     * @param errorMessage 错误信息
     * @return 结果
     */
    @Override
    public boolean recordStepError(Long stepId, String errorMessage) {
        return baseMapper.updateTaskStepError(stepId, errorMessage, getUsername()) > 0;
    }

    /**
     * 记录步骤输出数据
     * 
     * @param stepId 步骤ID
     * @param outputData 输出数据
     * @return 结果
     */
    @Override
    public boolean recordStepOutput(Long stepId, String outputData) {
        return baseMapper.updateTaskStepOutput(stepId, outputData, getUsername()) > 0;
    }

    /**
     * 增加步骤重试次数
     * 
     * @param stepId 步骤ID
     * @return 结果
     */
    @Override
    public boolean incrementStepRetryCount(Long stepId) {
        return baseMapper.incrementStepRetryCount(stepId, getUsername()) > 0;
    }

    /**
     * 统计任务中指定状态的步骤数量
     * 
     * @param taskId 任务ID
     * @param status 状态
     * @return 步骤数量
     */
    @Override
    public int countTaskStepsByStatus(Long taskId, String status) {
        return baseMapper.countTaskStepsByTaskIdAndStatus(taskId, status);
    }

    /**
     * 检查步骤是否可以重试
     *
     * @param stepId 步骤ID
     * @return 是否可以重试
     */
    @Override
    public boolean canRetryStep(Long stepId) {
        return false;
    }

    /**
     * 重置步骤状态为待执行
     *
     * @param stepId 步骤ID
     */
    @Override
    public void resetStepToPending(Long stepId) {
        TaskStep step = selectTaskStepByStepId(stepId);
        if (step == null) {
            return;
        }

        // 重置步骤状态和相关字段
        step.setStatus(JobStatusEnum.UN_START.getCode());
        step.setRetryCount(0);
        step.setStartTime(null);
        step.setEndTime(null);
        step.setExecutionDuration(null);
        step.setResponseStatus(null);
        step.setResponseData(null);
        step.setErrorMessage(null);
        step.setOutputData(null);

        baseMapper.updateById(step);
    }

    /**
     * 验证参数类型匹配性，防止类型不匹配导致下个接口调用失败
     *
     * @param convertedFields 转换后的字段列表
     * @param saveParams 下个接口的保存参数配置
     * @param stepId 当前步骤ID
     * @param stepName 当前步骤名称
     * @throws ServiceException 当检测到类型不匹配时抛出异常
     */
    private void validateParameterTypes(List<IntegrationFormFieldVO> convertedFields,
                                      List<InParam.ColumnParam> saveParams,
                                      Long stepId, String stepName) {
        log.debug("开始验证步骤 {} 的参数类型匹配性，转换字段数: {}, 保存参数数: {}",
                stepId, convertedFields.size(), saveParams.size());

        for (IntegrationFormFieldVO convertedField : convertedFields) {
            // 查找对应的下个接口参数配置
            Optional<InParam.ColumnParam> matchingParam = saveParams.stream()
                    .filter(param -> Objects.equals(param.getColumnName(), convertedField.getName()))
                    .findFirst();

            matchingParam.ifPresent(nextParam -> validateSingleParameterType(convertedField, nextParam, stepId, stepName));
        }

        log.debug("步骤 {} 参数类型验证完成", stepId);
    }

    /**
     * 验证单个参数的类型匹配性
     *
     * @param convertedField 转换后的字段
     * @param nextParam 下个接口的参数配置
     * @param stepId 当前步骤ID
     * @param stepName 当前步骤名称
     * @throws ServiceException 当检测到类型不匹配时抛出异常
     */
    private void validateSingleParameterType(IntegrationFormFieldVO convertedField,
                                           InParam.ColumnParam nextParam,
                                           Long stepId, String stepName) {
        String fieldName = convertedField.getName();
        String fieldValue = convertedField.getValue();
        String expectedType = nextParam.getColumnType();

        if (StrUtil.isBlank(fieldValue)) {
            log.debug("字段 {} 的值为空，跳过类型验证", fieldName);
            return;
        }

        // 检查是否为数组类型的值（JSON数组格式）
        boolean isArrayValue = isArrayValue(fieldValue);
        boolean expectsSingleValue = expectsSingleValue(expectedType);

        log.debug("字段 {} 类型验证: 值类型={}, 期望类型={}, 是否数组值={}, 期望单值={}",
                fieldName, fieldValue.getClass().getSimpleName(), expectedType, isArrayValue, expectsSingleValue);

        // 类型不匹配检查
        if (isArrayValue && expectsSingleValue) {
            String errorMessage = String.format(
                "步骤 '%s' 参数类型不匹配: 字段 '%s' 返回了数组类型的值，但下个接口期望单个 %s 类型的值。" +
                "当前筛选条件返回了多个结果，请调整筛选条件以确保只返回单个结果，或者检查下个接口的参数配置是否正确。",
                stepName, fieldName, expectedType
            );
            log.error(errorMessage);
            throw new ServiceException(errorMessage);
        }

        // 进一步的数据类型验证
        if (!isArrayValue && expectsSingleValue) {
            validateSingleValueType(fieldValue, expectedType, fieldName, stepId, stepName);
        }
    }

    /**
     * 判断字段值是否为数组类型
     *
     * @param fieldValue 字段值
     * @return 是否为数组类型
     */
    private boolean isArrayValue(String fieldValue) {
        if (StrUtil.isBlank(fieldValue)) {
            return false;
        }

        // 检查是否为JSON数组格式
        try {
            Object parsed = JSONUtil.parse(fieldValue);
            return JSONUtil.isTypeJSONArray(fieldValue) || (parsed instanceof List);
        } catch (Exception e) {
            // 如果解析失败，认为不是数组
            return false;
        }
    }

    /**
     * 判断参数类型是否期望单个值
     *
     * @param columnType 字段类型
     * @return 是否期望单个值
     */
    private boolean expectsSingleValue(String columnType) {
        if (StrUtil.isBlank(columnType)) {
            return true; // 默认期望单个值
        }

        String lowerType = columnType.toLowerCase();
        // 基本数据类型都期望单个值
        return lowerType.equals("string") || lowerType.equals("integer") || lowerType.equals("int") ||
               lowerType.equals("long") || lowerType.equals("double") || lowerType.equals("float") ||
               lowerType.equals("boolean") || lowerType.equals("date") || lowerType.equals("datetime");
    }

    /**
     * 验证单个值的数据类型
     *
     * @param fieldValue 字段值
     * @param expectedType 期望的数据类型
     * @param fieldName 字段名称
     * @param stepId 步骤ID
     * @param stepName 步骤名称
     * @throws ServiceException 当数据类型不匹配时抛出异常
     */
    private void validateSingleValueType(String fieldValue, String expectedType,
                                       String fieldName, Long stepId, String stepName) {
        if (StrUtil.isBlank(expectedType)) {
            return; // 没有指定类型，跳过验证
        }

        String lowerType = expectedType.toLowerCase();

        try {
            switch (lowerType) {
                case "integer":
                case "int":
                    Integer.parseInt(fieldValue);
                    break;
                case "long":
                    Long.parseLong(fieldValue);
                    break;
                case "double":
                case "float":
                    Double.parseDouble(fieldValue);
                    break;
                case "boolean":
                    // 检查是否为有效的布尔值
                    if (!"true".equalsIgnoreCase(fieldValue) && !"false".equalsIgnoreCase(fieldValue)) {
                        throw new NumberFormatException("Invalid boolean value");
                    }
                    break;
                default:
                    // 对于字符串类型和其他类型，不进行额外验证
                    break;
            }
        } catch (NumberFormatException e) {
            String errorMessage = String.format(
                "步骤 '%s' (ID: %d) 数据类型不匹配: 字段 '%s' 的值 '%s' 无法转换为期望的 %s 类型。" +
                "请检查筛选条件和数据格式是否正确。",
                stepName, stepId, fieldName, fieldValue, expectedType
            );
            log.error(errorMessage);
            throw new ServiceException(errorMessage);
        }
    }

    /**
     * 获取当前用户名
     */
    private String getUsername() {
        try {
            return SecurityUtils.getAccount();
        } catch (Exception e) {
            return "system";
        }
    }
}
