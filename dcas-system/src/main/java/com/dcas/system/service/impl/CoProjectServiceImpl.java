package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.algorithms.SnowFlake;
import com.dcas.common.annotation.DataScope;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.model.LoginUser;
import com.dcas.common.enums.CommonResultCode;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.enums.SysEditionEnum;
import com.dcas.common.enums.WorkPermissionType;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.exception.params.FailParamsException;
import com.dcas.common.utils.DateUtils;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.StringUtils;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.CoConstant;
import com.dcas.common.domain.entity.CoCustomer;
import com.dcas.common.domain.entity.CoFile;
import com.dcas.common.domain.entity.CoProject;
import com.dcas.common.model.vo.ProjectDetailVO;
import com.dcas.common.model.vo.ProjectWorkVO;
import com.dcas.common.model.vo.QueryProjectVo;
import com.dcas.common.mapper.*;
import com.dcas.system.service.CoCustomerService;
import com.dcas.system.service.CoProjectService;
import com.dcas.system.service.ISysConfigService;
import com.dcas.system.service.SecurityOperationService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CoProjectServiceImpl extends ServiceImpl<CoProjectMapper, CoProject> implements CoProjectService {

    /**
     * 业务逻辑：
     * 1.输入客户名称，查询客户表，填充客户负责人
     * 2.工期=计划完成日期—开工日期
     * 3.没在项目表存附件id的原因：解决一个项目多附件的问题，在附件表存项目表的id
     * 4.删除项目同时会删除项目关联作业：更新del_flag=2
     */

    private final CoProjectMapper coProjectMapper;
    private final CoCustomerMapper coCustomerMapper;
    private final CoCustomerService coCustomerService;
    private final CoOperationMapper coOperationMapper;
    private final CoConstantMapper coConstantMapper;

    private final CoFileServiceImpl coFileService;
    private final ISysConfigService sysConfigService;
    private final SecurityOperationService securityOperationService;

    /**
     * 新增项目
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 14:51
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int add(RequestModel<AddProjectDto> dto) {
        // 入参校验
        AddProjectDto request = dto.getPrivator();
        CheckUtil.checkParams(request);
        // 检查billNo是否重复
        checkBillNoAndName(null, request.getBillNo(), request.getProjectName());

        // 附件
        String fileIds = String.join(StrUtil.COMMA, request.getFileIdList());
        if (fileIds.length() > 1000) {
            throw new FailParamsException("附件数量超过限制");
        }

        CoProject coProject = new CoProject();
        BeanUtils.copyProperties(request, coProject);

        // 创建用户（统一身份登录账号）
        coProject.setCreateBy(SecurityUtils.getAccount());

        // 创建时间
        LocalDateTime now = LocalDateTime.now();
        String formatDateStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Date date = DateUtils.parseDate(formatDateStr);
        coProject.setCreateTime(date);
        // 更新时间
        coProject.setUpdateTime(date);

        // 主键
        String id = SnowFlake.getId();
        coProject.setProjectId(id);
        coProject.setFileIds(fileIds);
        this.updateCustomer(request, coProject);

        // 保存
        return coProjectMapper.insert(coProject);
    }

    /**
     * 更新客户资料表:客户名称已存在则更新·
     * TODO 并发问题，多用户同时更新一个客户的信息
     *
     * @param dto
     * @param coProject request
     * @return
     * @Date 2022/11/18 9:34
     */
    public synchronized void updateCustomer(AddProjectDto dto, CoProject coProject) {
        String customerId = SnowFlake.getId();
        CoCustomer coCustomer = new CoCustomer();
        BeanUtils.copyProperties(dto, coCustomer);
        QueryWrapper<CoCustomer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_name", dto.getCustomerName());
        List<CoCustomer> coCustomers = coCustomerMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(coCustomers)) {
            coCustomer.setCustomerId(customerId);
            coProject.setCustomerId(customerId);
            coCustomerMapper.insert(coCustomer);
            //并且新增一条客户最高敏感等级，默认是5
            CoConstant coConstant = new CoConstant();
            coConstant.setConstantId(SnowFlake.getId());
            coConstant.setCustomerId(customerId);
            coConstant.setHighestSensitiveLevel(5);
            coConstantMapper.insert(coConstant);
        } else {
            coProject.setCustomerId(coCustomers.get(0).getCustomerId());
            UpdateWrapper<CoCustomer> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("customer_director", dto.getCustomerDirector());
            updateWrapper.eq("customer_name", dto.getCustomerName());
            coCustomerMapper.update(new CoCustomer(), updateWrapper);
        }
    }

    /**
     * 批量删除项目记录
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:39
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int remove(RequestModel<PrimaryKeyListDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        List<String> idList = dto.getPrivator().getIdList();
        //更新评估作业删除标志
        coOperationMapper.updateOperationByProjectIds(idList);
        //删除安全检查作业
        securityOperationService.deleteByProjectIds(idList);
        return coProjectMapper.updateProjectByIds(idList);
    }

    /**
     * 查询项目记录
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:27
     */
    @Override
    @DataScope(deptAlias = "cp", userAlias = "cp")
    public PageInfo<QueryProjectVo> query(RequestModel<QueryProjectDTO> dto) {
        String account = SecurityUtils.getAccount();
        try (Page<Object> ignore = PageHelper.startPage(dto.getPageNum(), dto.getPageSize())) {
            List<CoProject> coProjects = coProjectMapper.queryProjectByUserAndParams( dto.getPrivator());
            Set<String> fileIds = coProjects.stream().flatMap(p -> StrUtil.split(p.getFileIds(), StrUtil.COMMA).stream()).collect(Collectors.toSet());
            Map<String, CoFile> fileMap = null;
            if (CollUtil.isNotEmpty(fileIds)) {
                fileMap = coFileService.getBaseMapper().selectBatchIds(fileIds).stream().collect(Collectors.toMap(CoFile::getFileId, file -> file));
            }
            Map<String, CoFile> finalFileMap = fileMap;
            List<QueryProjectVo> res = coProjects.stream().map(p -> {
                QueryProjectVo queryProjectVo = BeanUtil.copyProperties(p, QueryProjectVo.class);
                queryProjectVo.setHasDeletePermission(Objects.equals(p.getCreateBy(), account));
                if (finalFileMap != null && StrUtil.isNotBlank(p.getFileIds())) {
                    queryProjectVo.setFileList(StrUtil.split(p.getFileIds(), StrUtil.COMMA).stream().map(finalFileMap::get).collect(Collectors.toList()));
                }
                return queryProjectVo;
            }).collect(Collectors.toList());
            return new PageInfo<>(res);
        }
    }

    /**
     * 更新项目记录
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:46
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean edit(RequestModel<UpdateProjectDto> dto) {
        //入参校验
        UpdateProjectDto request = dto.getPrivator();
        CheckUtil.checkParams(request);

        checkBillNoAndName(request.getProjectId(), request.getBillNo(), request.getProjectName());

        CoProject coProject = new CoProject();
        BeanUtils.copyProperties(request, coProject);
        //更新时间
        LocalDateTime now = LocalDateTime.now();
        String formatDateStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        coProject.setUpdateTime(DateUtils.parseDate(formatDateStr));

        //更新客户资料表
        CoCustomer coCustomer = new CoCustomer();
        BeanUtils.copyProperties(request, coCustomer);
        coCustomerService.saveOrUpdate(coCustomer);

        //附件
        if (CollectionUtils.isNotEmpty(request.getFileIdList())) {
            String fileIds = StringUtils.join(request.getFileIdList().toArray(), ",");
            coProject.setFileIds(fileIds);
        } else {
            coProject.setFileIds(null);
        }
        return this.updateById(coProject);
    }

    /**
     * 查询客户记录
     *
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:27
     */
    @Override
    public List<CoCustomer> queryCustomer(RequestModel<QueryCustomerDto> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        return coCustomerMapper.queryCustomerList(dto.getPrivator());
    }

    @Override
    public CoProject queryProject(String operationId) {
        return coProjectMapper.queryProjectByOperationId(operationId);
    }

    @Override
    public ProjectDetailVO projectWorks(String projectId) {
        QueryWrapper<CoProject> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);
        queryWrapper.eq("del_flag", '0');
        CoProject coProject = coProjectMapper.selectOne(queryWrapper);
        if (Objects.isNull(coProject))
            throw new ServiceException("项目不存在");
        ProjectDetailVO projectDetailVO = BeanUtil.copyProperties(coProject, ProjectDetailVO.class);
        final SSOAccountDTO user = SecurityUtils.getLoginUser();
        List<ProjectWorkVO> projectWorks = coProjectMapper.projectWorks(projectId);
        if (StrUtil.isNotBlank(coProject.getFileIds())) {
            projectDetailVO.setFileList(coFileService.getBaseMapper().selectBatchIds(StrUtil.split(coProject.getFileIds(), StrUtil.COMMA)));
        }
        projectDetailVO.setWorkList(projectWorks.stream().filter(w -> hasPermission(user, w, WorkPermissionType.EXECUTE)).collect(Collectors.toList()));
        return projectDetailVO;
    }

    // 用户是否具有相应权限
    @Override
    public boolean hasPermission(SSOAccountDTO user, ProjectWorkVO vo, WorkPermissionType type) {
        String account = user.getAccount();
        return (vo.getType() == 1 && checkAccessPermission(account, vo, type))
                || (vo.getType() == 2 && checkReviewPermission(account, vo, type));
    }

    @Override
    public void fileDownload(String projectId, HttpServletResponse response) {
        CoProject coProject = coProjectMapper.selectById(projectId);
        if (Objects.isNull(coProject))
            throw new ServiceException("项目不存在");
        if (StrUtil.isBlank(coProject.getFileIds()))
            throw new ServiceException("当前项目未上传附件");
        coFileService.fileDownload(StrUtil.split(coProject.getFileIds(), StrUtil.COMMA),
                String.format("%s项目附件信息-%s.zip", coProject.getProjectName(), System.currentTimeMillis()), response);
    }

    private boolean checkAccessPermission(String account, ProjectWorkVO vo, WorkPermissionType type) {
        switch (type) {
            case CREATE:
                return Objects.equals(account, vo.getCreateBy());
            case EXECUTE:
                return Objects.equals(account, vo.getCreateBy())
                        || StrUtil.split(vo.getExecutorAccount(), StrUtil.COMMA).contains(account);
            case REVIEW:
                return Objects.equals(account, vo.getCreateBy())
                        || StrUtil.split(vo.getReviewerAccount(), StrUtil.COMMA).contains(account);
            default:
                return Boolean.FALSE;
        }
    }

    private boolean checkReviewPermission(String userName, ProjectWorkVO vo, WorkPermissionType type) {
        switch (type) {
            case CREATE:
                return Objects.equals(userName, vo.getUserAccount());
            case EXECUTE:
                return Objects.equals(userName, vo.getUserAccount())
                        || StrUtil.split(vo.getExecutor(), StrUtil.COMMA).contains(userName);
            default:
                return Boolean.FALSE;
        }
    }

    /**
     * 检查billNo是否重复
     *
     * @param billNo request
     * @Date 2022/7/13 16:11
     */
    private void checkBillNoAndName(String projectId, String billNo, String projectName) {
        QueryWrapper<CoProject> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_no", billNo);
        queryWrapper.eq("del_flag", '0');
        if (StrUtil.isNotBlank(projectId)) {
            queryWrapper.ne("project_id", projectId);
        }
        Integer count = coProjectMapper.selectCount(queryWrapper);
        if (count > 0)
            throw new FailParamsException("项目编号已存在");
        QueryWrapper<CoProject> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("project_name", projectName);
        queryWrapper1.eq("del_flag", '0');
        if (StrUtil.isNotBlank(projectId)) {
            queryWrapper1.ne("project_id", projectId);
        }
        Integer count1 = coProjectMapper.selectCount(queryWrapper1);
        if (count1 > 0)
            throw new FailParamsException("项目名称已存在");
    }
}
