package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.DataValidator;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.enums.*;
import com.dcas.common.excel.WaterMarkHandler;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.req.AdviseGenerateReq;
import com.dcas.common.utils.DateUtils;
import com.dcas.common.utils.Func;
import com.dcas.common.model.excel.LegalReportExcel;
import com.dcas.common.model.param.AnalysisParam;
import com.dcas.system.service.*;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.dcas.common.algorithms.SnowFlake;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.utils.judge.JudgeResultUtil;
import com.dcas.common.utils.judge.JudgeRiskLevelUtil;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.common.utils.sign.Base64;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.vo.*;
import com.dcas.common.mapper.*;
import com.mchz.dcas.client.enums.FileAnalysisType;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合法合规实现类
 *
 * <AUTHOR>
 * @Date 2022/6/24 15:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoLegalServiceImpl extends ServiceImpl<CoLegalMapper, CoLegal> implements CoLegalService {
    private final TagMapper tagMapper;
    private final AdviseService adviseService;
    private final CoLicenseMapper coLicenseMapper;
    private final CoLawItemMapper coLawItemMapper;
    private final CoQuestionnaireMapper coQuestionnaireMapper;
    private final CoLegalMapper coLegalMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final CoGapAnalysisMapper coGapAnalysisMapper;
    private final CoViewCustomerService coViewCustomerService;
    private final ModelAnalysisService modelAnalysisService;
    private final FileAnalysisLogicMapper fileAnalysisLogicMapper;
    private final ComplianceTemplateMapper complianceTemplateMapper;
    private final SpecialEvaluationConfigMapper specialEvaluationConfigMapper;
    private final ICoSystemResultService iCoSystemResultService;


    public static final String IMAGE_PNG_BASE64 = "data:image/png;base64,";
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final CoProgressMapper coProgressMapper;
    private final AdviseItemMapper adviseItemMapper;
    private final LlmTaskMapper llmTaskMapper;

    @DataValidator(type = TemplateTypeEnum.LAW_TEMPLATE)
    @Override
    @SchemaSwitch(String.class)
    public List<LegalModelVO> retrieveModel(String operationId) {
        Map<String, String> industryMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("type_id", 2)
                .eq("status", 0)).stream().collect(Collectors.toMap(t -> t.getId().toString(), Tag::getName));
        CoOperation coOperation = coOperationMapper.selectById(operationId);
        List<String> industryList = StrUtil.split(coOperation.getRelatedIndustry(), StrUtil.COMMA);
        QueryWrapper<ComplianceTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable", Boolean.TRUE);
        List<ComplianceTemplate> complianceTemplates = complianceTemplateMapper.selectList(queryWrapper);

        List<Integer> lawIds;
        //查询专项关联模板,筛选专项评估关联模板
        if (coOperation.getSpecId() != null ) {
            lawIds = new ArrayList<>();
            SpecialEvaluationConfig specialEvaluationConfig = specialEvaluationConfigMapper.selectById(coOperation.getSpecId());
            if (specialEvaluationConfig.getRelLawId() != null){
                complianceTemplates = complianceTemplates.stream().filter(complianceTemplate ->  Objects.equals(
                    complianceTemplate.getId(), specialEvaluationConfig.getRelLawId())).collect(Collectors.toList());
            }
        } else {
            // 需排除专项关联的合规评估模板
            lawIds = specialEvaluationConfigMapper.listRelLawIds(null);
        }
        return complianceTemplates.stream().filter(c ->
                CoOperationServiceImpl.templateFilter(coOperation.getRelatedDistrict(), industryList, c.getTags(), c.getRegionCode(), c.getIndustryCode(), industryMap) && !lawIds.contains(c.getId())).map(c -> {
            LegalModelVO vo = new LegalModelVO();
            vo.setTemplateId(c.getId());
            vo.setTemplateName(c.getName());
            vo.setUnderstand(c.getUnderstand());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 删除合法合规记录
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/5/31 10:38
     */
    @Override
    public int remove(RequestModel<CommonDto> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        QueryWrapper<CoLegal> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper.eq("label_id", dto.getPrivator().getLabelId());

        // 删除该作业业务系统层的符合情况
        iCoSystemResultService.remove(new QueryWrapper<CoSystemResult>().eq("operation_id", dto.getPrivator().getOperationId()));
        return coLegalMapper.delete(queryWrapper);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void apply(AnalysisParam param) {
        CoOperation coOperation = coOperationMapper.selectById(param.getOperationId());
        // 查询问卷调研模块下所有子类标签
        QueryWrapper<DynamicProcessTree> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", param.getOperationId());
        queryWrapper.eq("parent_id", LabelEnum.WJDY.getCode());
        List<DynamicProcessTree> treeList = dynamicProcessTreeMapper.selectList(queryWrapper);
        Set<Long> labels = treeList.stream().map(DynamicProcessTree::getTreeId).collect(Collectors.toSet());
        int count = coProgressMapper.selectFinishedLabelSize(param.getOperationId(), labels);
        if (count != labels.size()) {
            throw new ServiceException("请先完成问卷调研模块所有内容再进行操作");
        }
        param.setIsSpec(coOperation.getIsSpec());
        param.setSpecId(coOperation.getSpecId());
        List<CoLegal> coLegals = modelAnalysisService.complianceAnalysis(param);

        // 先删除该作业旧数据，后批量保存新数据
        this.remove(new QueryWrapper<CoLegal>().eq("operation_id", param.getOperationId()));
        this.saveBatch(coLegals);

        // 先删除该作业旧数据，后批量保存业务系统符合情况以及风险分析情况
        iCoSystemResultService.remove(new QueryWrapper<CoSystemResult>().eq("operation_id", param.getOperationId()));
        List<CoSystemResult> systemResults =  new ArrayList<>();
        coLegals.forEach(coLegal -> systemResults.addAll(coLegal.getCoSystemResultList()));
        iCoSystemResultService.saveBatch(systemResults);

        Integer itemCount = adviseItemMapper.selectCount(new QueryWrapper<AdviseItem>()
                .eq("operation_id", param.getOperationId()).eq("type", AdviceTypeEnum.LEGAL.getCode().toString()));
        if (itemCount > 0) {
            // 如果不是第一次应用，则需要设置处置建议需要提示框
            UpdateWrapper<CoOperation> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("repeat", 1);
            updateWrapper.eq("operation_id", param.getOperationId());
            coOperationMapper.update(new CoOperation(), updateWrapper);
        }

        adviseService.generate(new AdviseGenerateReq(param.getOperationId(), 1));
    }

    /**
     * 添加合规记录
     *
     * @param dto request
     * @return * @return List<AddLegalDTO>
     * @Date 2022/6/17 10:30
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean add(RequestModel<AddLegalDTO> dto) {

        CheckUtil.checkParams(dto.getPrivator());
        CommonDto commonDto = new CommonDto();
        BeanUtils.copyProperties(dto.getPrivator(), commonDto);
        List<CoLegal> coLegals = new ArrayList<>();
        if (!LegalModelEnum.ZHMB.getInfo().equals(dto.getPrivator().getModelName())) {
            List<CoLegal> coLegalList = this.getLegalList(commonDto, dto.getPrivator().getModelName());
            coLegals.addAll(coLegalList);
        } else {
            for (LegalModelEnum legalModelEnum : LegalModelEnum.values()) {
                List<CoLegal> coLegalList = this.getLegalList(commonDto, legalModelEnum.getInfo());
                coLegals.addAll(coLegalList);
            }
        }
        return this.saveBatch(coLegals);
    }


    /**
     * 查询合法合规列表
     *
     * @param commonDto request
     * @return * @return List<CoVerification>
     * @Date 2022/9/19 15:46
     */
    public List<CoLegal> getLegalList(CommonDto commonDto, String modelName) {

        //查询法律条文表
        QueryWrapper<CoLawItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("law_name", modelName);
        List<CoLawItem> coLawItems = coLawItemMapper.selectList(queryWrapper);
        //法条排序
        Comparator<CoLawItem> byLawId = Comparator.comparing(CoLawItem::getLawId);
        Comparator<CoLawItem> byItemNum = Comparator.comparing(CoLawItem::getItemNum);
        coLawItems.sort(byLawId.thenComparing(byItemNum));
        //查询问卷记录
        OperationIdDto operationIdDto = new OperationIdDto();
        operationIdDto.setOperationId(commonDto.getOperationId());
        List<CoQuestionnaire> coQuestionnaires = coQuestionnaireMapper.queryQuestionnaireForLegal(operationIdDto);

        //包含重复articleCode对应选项的List集合；因为存在不同问题的articleCode可能相同的情况
        List<Map<String, String>> parseCodeList = new ArrayList<>();
        //问卷上的备注,不同问题的法条编码可能相同，所以有重复
        List<Map<String, String>> parseRemarkList = new ArrayList<>();
        log.info("parseRemarkList:" + parseRemarkList.toString());
        //法律条文遍历 问卷表id对应的article_codes-法条编号
        coLawItems.stream().forEach(a -> {
            coQuestionnaires.stream().forEach(q -> {
                if (StringUtils.isNotBlank(q.getArticleCodes())) {
                    List<String> articleCodes = Arrays.asList(q.getArticleCodes().split(","));
                    articleCodes.stream().forEach(m -> {
                        //当法律条文表articleCode（条款项目）包含问卷id对应的articleCode（可能只到条）
                        if (a.getArticleCode().contains(m)) {
                            Map<String, String> map = new HashMap<>();
                            //条文编码
                            map.put(a.getArticleCode(), q.getOpt());
                            parseCodeList.add(map);
                            //问卷备注
                            Map<String, String> remarkMap = new HashMap<>();
                            remarkMap.put(a.getArticleCode(), q.getRemark());
                            parseRemarkList.add(remarkMap);
                        }
                    });
                }
            });
        });

        //按articleCode分组，得到不重复的以articleCode为键的Map集合
        Map<Set<String>, List<Map<String, String>>> parseCodeMap = parseCodeList.stream().collect(Collectors.groupingBy(p -> p.keySet()));
        //articleCode/optList
        Map<String, List<String>> mapList = new HashMap<>();
        for (Map.Entry<Set<String>, List<Map<String, String>>> entry : parseCodeMap.entrySet()) {
            //将key转成articleCode
            String articleCode = StringUtils.join(entry.getKey().toArray());
            //map结构 articleCode/opt; parseMap.get(key):当articleCode对应的选项有多个时，获取List
            List<Map<String, String>> map = entry.getValue();

            //当某一条map.size>1; 则去组装opts
            if (map.size() > 1) {
                //组装opts
                List<String> optList = new ArrayList<>();
                for (Map<String, String> stringMap : map) {
                    optList.add(stringMap.get(articleCode));
                }
                mapList.put(articleCode, optList);

            } else {
                List<String> optList = new ArrayList<>();
                optList.add(map.get(0).get(articleCode));
                mapList.put(articleCode, optList);
            }
        }

        //查询结果
        List<CoLegal> coLegalList = new ArrayList<CoLegal>();
        //根据选项列表判断符合情况
        for (Map.Entry<String, List<String>> entry : mapList.entrySet()) {
            //key=articleCode,result=符合情况
            String key = entry.getKey();
            String result = JudgeResultUtil.getResult(entry.getValue());
            //判断风险等级
            String riskLevel = JudgeRiskLevelUtil.getRiskLevel(result);

            //返回值
            coLawItems.stream().forEach(p -> {
                if (p.getArticleCode().equals(key)) {
                    CoLegal coLegal = new CoLegal();
                    BeanUtils.copyProperties(p, coLegal);
                    //生成主键
                    coLegal.setLawId(p.getLawId());
                    coLegal.setResult(result);
                    coLegal.setRiskLevel(riskLevel);
                    coLegal.setOperationId(commonDto.getOperationId());
                    coLegal.setLabelId(commonDto.getLabelId());
                    coLegalList.add(coLegal);
                }
            });
        }

        /**
         * 问卷备注
         */
        //按articleCode分组，得到不重复的以articleCode为键的Map集合
        Map<Set<String>, List<Map<String, String>>> parseRemarkMap = parseRemarkList.stream().collect(Collectors.groupingBy(p -> p.keySet()));
        //articleCode/remarkList
        Map<String, List<String>> listMap = new HashMap<>();
        for (Map.Entry<Set<String>, List<Map<String, String>>> entry : parseRemarkMap.entrySet()) {
            //将key转成articleCode
            String articleCode = StringUtils.join(entry.getKey().toArray());
            //map结构 articleCode/remark; parseRemarkMap.get(key):当articleCode对应的备注有多个时，获取List
            List<Map<String, String>> map = entry.getValue();

            //当某一条map.size>1; 则去组装opts
            if (map.size() > 1) {
                //组装remark
                List<String> remarkList = new ArrayList<>();
                for (Map<String, String> stringMap : map) {
                    remarkList.add(stringMap.get(articleCode));
                }
                listMap.put(articleCode, remarkList);

            } else {
                List<String> remarkList = new ArrayList<>();
                remarkList.add(map.get(0).get(articleCode));
                listMap.put(articleCode, remarkList);
            }
        }

        // 匹配listMap与coLegalList articleCode相同的话，则把remarks填入
        // 填充备注
        for (Map.Entry<String, List<String>> entry : listMap.entrySet()) {
            //key=articleCode,remarks=备注列表转字符串
            String key = entry.getKey();
            //备注
            String remark = StringUtils.join(entry.getValue().toArray(), ";");
            //返回值
            coLegalList.stream().forEach(p -> {
                if (p.getArticleCode().equals(key)) {
                    p.setRemark(remark);
                }
            });
        }
        //list根据sort排序
        coLegalList.sort(Comparator.comparing(CoLegal::getLawId));
        //去重操作 group by itemNum
        Map<Integer, List<CoLegal>> collect = coLegalList.stream().sorted(Comparator.comparing(CoLegal::getLawId)).collect(Collectors.groupingBy(p -> p.getItemNum()));
        List<CoLegal> voList = new ArrayList<>();
        for (Map.Entry<Integer, List<CoLegal>> entry : collect.entrySet()) {
            //key=itemNum
            Integer itemNum = entry.getKey();
            List<CoLegal> coLegals = entry.getValue();
            coLegals.sort(Comparator.comparing(CoLegal::getLawId));
            //新值
            List<String> lawIds = new ArrayList<>();
            List<String> itemContents = new ArrayList<>();
            List<String> articleCodes = new ArrayList<>();
            String remark = "";
            String result = "";
            String riskLevel = "";

            CoLegal legal = new CoLegal();
            if (coLegals.size() > 1) {
                for (CoLegal coLegal : coLegals) {
                    itemContents.add(coLegal.getItemContent());
                    lawIds.add(coLegal.getLawId().toString());
                    articleCodes.add(coLegal.getArticleCode());
                    remark = coLegal.getRemark();
                    result = coLegal.getResult();
                    riskLevel = coLegal.getRiskLevel();
                }
            } else {
                itemContents.add(coLegals.get(0).getItemContent());
                lawIds.add(coLegals.get(0).getLawId().toString());
                articleCodes.add(coLegals.get(0).getArticleCode());
                remark = coLegals.get(0).getRemark();
                result = coLegals.get(0).getResult();
                riskLevel = coLegals.get(0).getRiskLevel();
            }
            legal.setLegalId(SnowFlake.getId());
            legal.setItemId(StringUtils.join(lawIds.toArray(), ","));
            legal.setLawName(modelName);
            legal.setItemNum(itemNum);
            legal.setItemContent(StringUtils.join(itemContents.toArray(), ""));
            legal.setArticleCode(StringUtils.join(articleCodes.toArray(), ","));
            legal.setRemark(remark);
            legal.setResult(result);
            legal.setRiskLevel(riskLevel);
            legal.setOperationId(commonDto.getOperationId());
            legal.setLabelId(commonDto.getLabelId());
            voList.add(legal);
        }
        //list多字段排序
        Comparator<CoLegal> byLawName = Comparator.comparing(CoLegal::getLawName);
        Comparator<CoLegal> byLegalItemNum = Comparator.comparing(CoLegal::getItemNum);
        voList.sort(byLawName.thenComparing(byLegalItemNum));
        //合并按条为最小颗粒度展示
        return voList;
    }


    /**
     * 查询合规记录
     *
     * @param dto request
     * @return * @return List<QueryLegalDTO>
     * @Date 2022/6/17 10:30
     */
    @Override
    public PageInfo<CoLegal> query(RequestModel<QueryLegalDTO> dto) {
        CheckUtil.checkParams(dto.getPrivator());
        try (Page<Object> ignored = PageHelper.startPage(dto.getPageNum(), dto.getPageSize())) {
            List<CoLegal> coLegalList = coLegalMapper.queryLegalList(dto.getPrivator());
            if (CollUtil.isEmpty(coLegalList)){
                return new PageInfo<>(coLegalList);
            }
            List<CoSystemResult> coSystemResultList = iCoSystemResultService.list(
                new QueryWrapper<CoSystemResult>().eq("operation_id", dto.getPrivator().getOperationId()).eq("type",
                    AdviceTypeEnum.LEGAL.getCode()));
            Map<Long, String> busSystemMap =
                dynamicProcessTreeMapper.selectByOperationIdAndTreeId(dto.getPrivator().getOperationId(),
                        LabelEnum.XTDY.getCode()).stream()
                    .collect(Collectors.toMap(TreeLabelDTO::getTreeId, TreeLabelDTO::getTreeName));
            List<String> systemResultIds = coSystemResultList.stream().map(CoSystemResult::getId).map(String::valueOf).collect(Collectors.toList());
            Map<String, Integer> taskMap = llmTaskMapper.selectList(new QueryWrapper<LlmTask>()
                    .eq("operation_id", dto.getPrivator().getOperationId())
                    .eq("label_id", LabelEnum.HFHG.getCode())
                    .in("data_id", systemResultIds))
                    .stream()
                    .collect(Collectors.toMap(LlmTask::getDataId, LlmTask::getStatus));
            Map<String, List<CoSystemResult>> systemResultMap = coSystemResultList.stream()
                .peek(coSystemResult -> {
                    coSystemResult.setSystemName(busSystemMap.get(coSystemResult.getSystemId()));
                    // 如果AI风险描述不为空，说明为手动应用过，取AI描述展示
                    coSystemResult.setRiskDesc(StrUtil.isEmpty(coSystemResult.getAiRiskDesc()) ? coSystemResult.getRiskDesc() : coSystemResult.getAiRiskDesc());
                    coSystemResult.setStatus(taskMap.get(coSystemResult.getId().toString()));
                })
                .collect(Collectors.groupingBy(CoSystemResult::getRelId));
            coLegalList.forEach(coLegal -> coLegal.setCoSystemResultList(systemResultMap.get(coLegal.getLegalId())));
            return new PageInfo<>(coLegalList);
        }
    }

    /**
     * 更新合法合规记录
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/6/27 16:07
     */
    @Override
    public int edit(RequestModel<UpdateLegalDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        CoLegal coLegal = new CoLegal();
        BeanUtils.copyProperties(dto.getPrivator(), coLegal);
        return coLegalMapper.updateById(coLegal);
    }

    /**
     * 单行删除
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/9/21 10:19
     */
    public int removeRow(RequestModel<PrimaryKeyListDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());

        // 删除该作业业务系统层的符合情况
        iCoSystemResultService.remove(new QueryWrapper<CoSystemResult>().in("rel_id", dto.getPrivator().getIdList()));
        return coLegalMapper.deleteBatchIds(dto.getPrivator().getIdList());
    }

    /**
     * 导出word
     *
     * @param response request
     * @throws IOException
     * @Date 2022/7/27 15:45
     */
    @Override
    public void exportWord(HttpServletResponse response, RequestModel<ExportOperationWordDto> dto) throws IOException {

        ClassPathResource classPathResource = new ClassPathResource("template/LegalExportTemplate.docx");
        InputStream inputStream = classPathResource.getInputStream();

        //数据模型
        Map<String, Object> model = new HashMap<>();

        //准备数据
        String operationId = dto.getPrivator().getOperationId();
        //1、2阶段查询
        QueryProjectOperationExportVo poVo = coOperationMapper.queryOperationExport(dto.getPrivator().getOperationId());

        //文本
        model.put("customerName", poVo.getCustomerName());
        model.put("projectName", poVo.getProjectName());
        model.put("date", LocalDate.now());
        model.put("relatedSystem", StringUtils.replace(poVo.getRelatedSystem(),",","、"));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        model.put("createTime", simpleDateFormat.format(poVo.getCreateTime()));
        model.put("completedDate", simpleDateFormat.format(poVo.getCompletedDate()));
        model.put("customerDirector", poVo.getCustomerDirector());
        model.put("projectManager", poVo.getProjectManager());
        model.put("executor", poVo.getExecutorAccount());
        model.put("reviewer", poVo.getReviewerAccount());


        //敏感数据占比图表 从入参中获取
        List<ExportWordChart> chartList = dto.getPrivator().getChartList();
        List<ExportWordChart> sensitiveChartList = chartList.stream().filter(s -> "敏感数据占比图表".equals(s.getName())).collect(Collectors.toList());

        String imageBase64Data = sensitiveChartList.get(0).getPicture();
        imageBase64Data = imageBase64Data.substring(IMAGE_PNG_BASE64.length());
        //base64解码
        byte[] decode = Base64.decode(imageBase64Data);
        ByteArrayInputStream bis = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis1 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis2 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis3 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis4 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis5 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis6 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis7 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis8 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis9 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis10 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis11 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis12 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis13 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis14 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis15 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis16 = new ByteArrayInputStream(decode);


        model.put("sensitiveAssetsProportionImg", Pictures.ofStream(bis, PictureType.PNG)
                .size(600, 200).create());
        model.put("userAuthorityTypeAddImg", Pictures.ofStream(bis1, PictureType.PNG)
                .size(600, 200).create());
        model.put("userAuthorityTypeUpdateImg", Pictures.ofStream(bis2, PictureType.PNG)
                .size(600, 200).create());
        model.put("userAuthorityTypeSelectImg", Pictures.ofStream(bis3, PictureType.PNG)
                .size(600, 200).create());
        model.put("userAuthorityTypeDeleteImg", Pictures.ofStream(bis4, PictureType.PNG)
                .size(600, 200).create());
        model.put("userAuthorityTypeDropImg", Pictures.ofStream(bis5, PictureType.PNG)
                .size(600, 200).create());


        //4.1.1	评估概述
        //查询现状核验表获取标签页
        QueryWrapper<CoVerification> query = new QueryWrapper<>();
        query.eq("model_name", "综合模型");
        query.eq("level", "3");
        List<CoVerification> coVerifications = coVerificationMapper.selectList(query);
        List<String> tabNameList = coVerifications.stream().map(v -> v.getType()).distinct().collect(Collectors.toList());
        String tabNames = StringUtils.join(tabNameList.toArray(), "、");

        model.put("modelName", "综合模型");
        model.put("level", "3级 充分定义");
        model.put("tabNames", tabNames);

        //4.1.2.1	评估结果{{chartName}}
//        model.put("chartName", dto.getPrivator().getChartName());
        model.put("chartNameImg", Pictures.ofStream(bis6, PictureType.PNG)
                .size(600, 200).create());

        //查询图表备注
        QueryWrapper<CoGapAnalysis> query0 = new QueryWrapper<>();
        query0.eq("operation_id", dto.getPrivator().getOperationId());
//        query0.eq("chart_name", dto.getPrivator().getChartName());
        List<CoGapAnalysis> coGapAnalysisList = coGapAnalysisMapper.selectList(query0);
        String chartRemark = StringUtils.isNotBlank(coGapAnalysisList.get(0).getRemark()) ? coGapAnalysisList.get(0).getRemark() : "";
        model.put("chartRemark", chartRemark);

        //4.1.3.1	{{tabName1}}安全现状
        QueryWrapper<CoVerification> query1 = new QueryWrapper<>();
        query1.eq("operation_id", dto.getPrivator().getOperationId());
        query1.eq("type", tabNameList.get(0));
        List<CoVerification> tabList1 = coVerificationMapper.selectList(query1);
        int sort1 = 1;
        for (CoVerification coVerification : tabList1){
            coVerification.setSort(sort1++);
        }
        model.put("tabName1", tabNameList.get(0));
        model.put("tabList1", tabList1);

        QueryWrapper<CoVerification> query2 = new QueryWrapper<>();
        query2.eq("operation_id", dto.getPrivator().getOperationId());
        query2.eq("type", tabNameList.get(1));
        List<CoVerification> tabList2 = coVerificationMapper.selectList(query2);
        int sort2 = 1;
        for (CoVerification coVerification : tabList2){
            coVerification.setSort(sort2++);
        }

        model.put("tabName2", tabNameList.get(1));
        model.put("tabList2", tabList2);

        RequestModel<OperationIdDto> requestModel = new RequestModel<>();
        OperationIdDto operationIdDto = new OperationIdDto();
        operationIdDto.setOperationId(operationId);
        requestModel.setPrivator(operationIdDto);
        //5.2.2.1	合规风险分析
        QueryViewLegalResultVo legalResultVo = coViewCustomerService.queryLegalProportion(requestModel);
        model.put("lawTotalNum", legalResultVo.getLawTotalNum());
        model.put("lawDocNum", legalResultVo.getLawDocNum());
        model.put("ruleDocNum", legalResultVo.getRuleDocNum());
        model.put("standardDocNum", legalResultVo.getStandardDocNum());
        model.put("itemTotalNum", legalResultVo.getItemTotalNum());
        model.put("qualifiedProportion", legalResultVo.getQualifiedProportion());
        model.put("countANum", legalResultVo.getCountANum());
        model.put("countBNum", legalResultVo.getCountBNum());
        model.put("countCNum", legalResultVo.getCountCNum());
        model.put("countDNum", legalResultVo.getCountDNum());
        model.put("countAProportion", legalResultVo.getCountAProportion());
        model.put("countBProportion", legalResultVo.getCountBProportion());
        model.put("countCProportion", legalResultVo.getCountCProportion());
        model.put("countDProportion", legalResultVo.getCountDProportion());

        model.put("legalResultImg", Pictures.ofStream(bis9, PictureType.PNG)
                .size(600, 200).create());

        //5.2.3	具体评估结果 5.2.3.1	{{co_legal.law_name}}评估详情
        QueryWrapper<CoLegal> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("operation_id", dto.getPrivator().getOperationId());
        List<CoLegal> coLegalList = coLegalMapper.selectList(queryWrapper2);
        String lawName = coLegalList.get(0).getLawName();
        queryWrapper2.eq("law_name", lawName);
        List<CoLegal> legalList = coLegalMapper.selectList(queryWrapper2);
        model.put("lawName", lawName);
        model.put("legalList", legalList);


        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder().bind("userAssetAuthorityList", policy)
                .bind("tabList1", policy)
                .bind("tabList2", policy)
                .bind("loopholeByIPSystemList", policy)
                .bind("legalList", policy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);


        //输出结果
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        template.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, bos, out);

    }

    /**
     * 导出EXCEL
     *
     * @param response    request
     * @param operationId request
     */
    @Override
    @SneakyThrows
    public void exportExcel(HttpServletResponse response, String operationId) {
        List<LegalReportExcel> list = coLegalMapper.selectLegalExcelByOperationId(operationId);
        Func.responseSetting(response,
                String.format("%s-%s.xlsx", "合规评估", DateUtils.getExportDateStr(System.currentTimeMillis())));
        EasyExcel.write(response.getOutputStream(), LegalReportExcel.class)
                .registerWriteHandler(WaterMarkHandler.simple(coLicenseMapper.queryCustomName()))
                .sheet().doWrite(list);
    }

    @Override
    @SchemaSwitch(String.class)
    public String queryStatusDesc(String operationId) {
        QueryWrapper<CoLegal> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct law_id");
        queryWrapper.eq("operation_id", operationId);
        Set<Integer> lawIds = coLegalMapper.selectList(queryWrapper).stream().map(CoLegal::getLawId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(lawIds))
            return null;
        List<IntervalDTO> attributes = fileAnalysisLogicMapper.selectList(new QueryWrapper<FileAnalysisLogic>().in("file_id",
                lawIds).eq("file_type", "LAW")).stream().filter(f ->
                f.getHasException() && Objects.equals(f.getExceptionType(), FileAnalysisType.MIDDLE.name()))
                .map(f -> JSON.parseObject(f.getAttributes().toString(), IntervalDTO.class)).collect(Collectors.toList());
        return attributes.stream().map(a -> String.format("【%s】：%s", a.getStatusName(), a.getStatusDesc())).collect(Collectors.joining(StrUtil.LF));
    }

    @Override
    public void editSystemResult(CoSystemResult coSystemResult) {
        CoSystemResult old = iCoSystemResultService.getById(coSystemResult.getId());
        // 若页面上修改风险分析内容，先判断与老的风险描述内容是否一致，不一致则更新到aiRiskDesc
        if (coSystemResult.getRiskDesc() != null && (!coSystemResult.getRiskDesc()
                .equals(old.getRiskDesc()) || !coSystemResult.getAiRiskDesc().equals(old.getAiRiskDesc()))) {
            coSystemResult.setAiRiskDesc(coSystemResult.getRiskDesc());
        }
        iCoSystemResultService.saveOrUpdate(coSystemResult);

        // 重新计算一级条文的符合性和风险程度
        Set<String> resSet = iCoSystemResultService.list(
            new QueryWrapper<CoSystemResult>().eq("operation_id", coSystemResult.getOperationId())
                .eq("rel_id", coSystemResult.getRelId())).stream().map(CoSystemResult::getSystemResult).collect(Collectors.toSet());
        String result = "";
        String risk = "";
        if (resSet.size() == 1){
            result = resSet.stream().findFirst().get();
        } else {
            result = OptEnum.B.getInfo();
        }

        if (OptEnum.A.getInfo().equals(result)){
            risk = DangerLevelEnum.LOW.getInfo();
        } else if (OptEnum.B.getInfo().equals(result)) {
            risk = DangerLevelEnum.MEDIUM.getInfo();
        } else if (OptEnum.C.getInfo().equals(result)) {
            risk = DangerLevelEnum.HIGH.getInfo();
        } else {
            risk = DangerLevelEnum.NONE.getInfo();
        }

        coLegalMapper.updateResultAndRiskLevelById(coSystemResult.getRelId(), result, risk);
    }
}
