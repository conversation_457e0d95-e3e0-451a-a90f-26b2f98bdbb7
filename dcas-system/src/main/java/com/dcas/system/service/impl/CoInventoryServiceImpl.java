package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.PathUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.DataValidator;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.enums.DataTagEnum;
import com.dcas.common.enums.TemplateTypeEnum;
import com.dcas.common.excel.WaterMarkHandler;
import com.dcas.common.utils.*;
import com.dcas.common.utils.file.FileUtils;
import com.dcas.common.model.excel.InventoryExportExcel;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.InventoryAddReq;
import com.dcas.system.report.ReportUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.dcas.common.algorithms.SnowFlake;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.vo.*;
import com.dcas.common.mapper.*;
import com.dcas.system.service.CoInventoryService;
import com.google.common.collect.Lists;
import com.mchz.mcdatasource.core.DatasourceConstant;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (ConInventory)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-30 11:44:57
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoInventoryServiceImpl extends ServiceImpl<CoInventoryMapper, CoInventory> implements CoInventoryService {

    @Value("${safety.profile}")
    private String basePath;

    private final TagMapper tagMapper;
    private final CoLicenseMapper coLicenseMapper;
    private final AssetTemplateMapper assetTemplateMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final CoConstantMapper coConstantMapper;
    private final CoOperationMapper coOperationMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final AssetTypeServiceImpl assetTypeService;
    private final AssetDataServiceImpl assetDataService;
    private final DetermineAssetTypeServiceImpl determineAssetTypeService;
    private final DetermineAssetDataServiceImpl determineAssetDataService;
    private final DiscoveryClassifyDataMapper discoveryClassifyDataMapper;
    private final ResultMetaTableMapper resultMetaTableMapper;
    private final ResultMetaColumnMapper resultMetaColumnMapper;
    private final AnalysisSampleMapper analysisSampleMapper;
    private final PreSourceConfigMapper preSourceConfigMapper;

    /**
     * 添加盘点数据
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/5/31 10:34
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int insertInventory(RequestModel<InventoryAddReq> dto) {
        InventoryAddReq req = dto.getPrivator();
        CheckUtil.checkParams(req);
        DbConfig dbConfig = new DbConfig(req.getHost(), req.getPort(), req.getDbType(), req.getDbName(), req.getSchemaName());
        int count = coInventoryMapper.selectCountWithDbConfig(req.getOperationId(), dbConfig, req.getDataAsset());
        if (count > 0)
            throw new ServiceException("资产已存在");
        CoInventory coInventory = new CoInventory();
        BeanUtils.copyProperties(req, coInventory);
        CheckUtil.checkParams(coInventory);
        coInventory.setDbConfig(dbConfig);
        if (StrUtil.isEmpty(req.getDataTag())){
            coInventory.setDataTag(DataTagEnum.GENERAL.getTag());
        }
        return coInventoryMapper.insert(coInventory);
    }

    /**
     * 删除盘点数据
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/5/31 10:38
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int deleteByIdList(RequestModel<PrimaryKeyListDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        List<String> inventoryIds = dto.getPrivator().getIdList();
        List<CoInventory> coInventories = coInventoryMapper.selectBatchIds(inventoryIds);
        resultMetaTableMapper.deleteByInventory(coInventories);
        resultMetaColumnMapper.deleteByInventory(coInventories);
        analysisSampleMapper.deleteByInventory(coInventories);
        coInventoryMapper.deleteBatchIds(inventoryIds);
        discoveryClassifyDataMapper.delete(new QueryWrapper<DiscoveryClassifyData>().in("inventory_id", inventoryIds));
        return 1;
    }

    /**
     * 修改盘点数据
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/5/31 11:29
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int updateInventoryById(RequestModel<UpdateInventoryDTO> dto) {
        //入参校验
        UpdateInventoryDTO updateInventoryDTO = dto.getPrivator();
        CheckUtil.checkParams(updateInventoryDTO);

        CoInventory coInventory = new CoInventory();
        //复制对象
        BeanUtils.copyProperties(updateInventoryDTO, coInventory);
        //查询作业id
        CoInventory inventory = coInventoryMapper.selectById(updateInventoryDTO.getInventoryId());
        if (Objects.isNull(inventory))
            throw new ServiceException("资产不存在");
        //查询最高敏感等级
        QueryWrapper<CoConstant> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_id", coOperationMapper.selectCustomIdByOperationId(inventory.getOperationId()));
        List<CoConstant> coConstants = coConstantMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(coConstants) || coConstants.get(0).getHighestSensitiveLevel() < 3)
            throw new ServiceException("请先设置最大敏感等级");
        // 更新数据标识
        if (StrUtil.isEmpty(updateInventoryDTO.getDataTag())){
            coInventory.setDataTag(DataTagEnum.GENERAL.getTag());
        }
        //更新资产盘点
        return coInventoryMapper.updateById(coInventory);
    }

    @Override
    public void batchUpdate(BatchEditInventoryDTO dto) {
        CoInventory bean = Func.toBean(dto, CoInventory.class);
        coInventoryMapper.updateBatchById(bean, dto.getInventoryIds());
    }


    /**
     * 查询盘点数据
     *
     * @param dto request
     * @return * @return List<ConInventoryEntity>
     * @Date 2022/5/31 10:52
     */
    @Override
    public PageResult<InventoryPageQueryVO> selectInventoryList(RequestModel<QueryInventoryDTO> dto) {
        //入参校验
        QueryInventoryDTO queryInventoryDTO = dto.getPrivator();
        CheckUtil.checkParams(queryInventoryDTO);

        CoInventory coInventory = new CoInventory();
        BeanUtils.copyProperties(queryInventoryDTO, coInventory);

        try (Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());) {
            List<InventoryPageQueryVO> list = coInventoryMapper.queryInventoryByLimit(queryInventoryDTO);
           return PageResult.ofPage(page.getTotal(), list);
        }
    }

    @Override
    public Boolean qryUnSensitiveData(String operationId) {
        return coInventoryMapper.qryUnSensitiveData(operationId) > 0;
    }

    /**
     * 数据导入
     *
     * @param operationId request
     * @Date 2022/6/2 11:41
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void importData(MultipartFile file, String operationId) throws IOException {
        Set<CoInventoryImport> inventoryImportList = dataAssetImportCheck(file, operationId);
        QueryWrapper<CoConstant> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_id", coOperationMapper.selectCustomIdByOperationId(operationId));
        Map<String, DbConfig> dbConfigMap = new HashMap<>();
        List<CoInventory> coInventoryList = new ArrayList<>();
        //多字段分组：根据数据源（所属业务系统）、schema、tableName分组
        Map<InventorySourceDTO, List<CoInventoryImport>> groupMap = inventoryImportList.stream().collect(Collectors.groupingBy(c -> Func.toBean(c, InventorySourceDTO.class)));
        groupMap.forEach((key, columnList) -> {
            //分级的最大值 = 数据资产的敏感等级
            List<Integer> grades = columnList.stream().filter(p -> StringUtils.isNotBlank(p.getGraded())).mapToInt(p ->
                    Integer.parseInt(StringUtils.substring(p.getGraded(), 0, 1))).boxed().collect(Collectors.toList());
            int sensitiveCount = 0;
            int max = 0;
            for (Integer grade : grades) {
                max = Math.max(max, grade);
                if (grade >= 3)
                    sensitiveCount++;
            }
            //资产盘点表
            String dbName = StrUtil.isEmpty(key.getDbName()) ? StrUtil.EMPTY : key.getDbName();
            String dbKey = StrUtil.join(StrUtil.COMMA, key.getConfigType(), dbName, key.getHost(), key.getPort());
            if (!dbConfigMap.containsKey(dbKey)) {
                dbConfigMap.put(dbKey, new DbConfig(key.getHost(), key.getPort(), key.getConfigType(), dbName, null));
            }
            CoInventory coInventory = new CoInventory();
            String inventoryId = SnowFlake.getId();
            coInventory.setInventoryId(inventoryId);
            coInventory.setDbConfig(dbConfigMap.get(dbKey));
            coInventory.setOperationId(operationId);
            coInventory.setLabelId(LabelEnum.ZCPD.getCode());
            coInventory.setSchemaName(key.getSchemaName());
            coInventory.setDataAsset(key.getTableName());
            coInventory.setAssetComment(columnList.get(0).getTableComment());
            coInventory.setSensitiveLevel(max);
            coInventory.setColumnNum(columnList.size());
            coInventory.setSensitiveColumnNum(sensitiveCount);
            coInventory.setJobId(-1L);
            coInventory.setSensitive(max >= 3 ? 1 : 0);
            coInventory.setDataTag(DataTagEnum.GENERAL.getTag());
            coInventory.setBusSystem(key.getBusSystem());
            coInventory.setSystemId(key.getSystemId());
            coInventoryList.add(coInventory);
        });

        // 删除上次导入的资产
        baseMapper.delete(new QueryWrapper<CoInventory>().eq("operation_id", operationId).eq("job_id", -1L));

        PartitionUtils.part(coInventoryList, this::saveBatch);
    }

    private Set<CoInventoryImport> dataAssetImportCheck(MultipartFile file, String operationId) throws IOException {
        if (!FileUtils.isExcelFile(file.getOriginalFilename())) {
            throw new ServiceException("不支持的文件格式");
        }
        List<CoInventoryImport> inventoryImportList = Func.fileAttestationPart(file, CoInventoryImport.class, 2);
        if (CollUtil.isEmpty(inventoryImportList)) {
            throw new ServiceException("导入模板不能为空");
        }
        final Map<String, Long> map = Arrays.stream(DataSourceType.values()).collect(
                Collectors.toMap(DataSourceType::getName, DataSourceType::getCode));
        Map<String, Long> systemMap = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode())
                .stream().collect(Collectors.toMap(TreeLabelDTO::getTreeName, TreeLabelDTO::getTreeId));
        AtomicInteger index = new AtomicInteger(2);
        return inventoryImportList.stream().peek(i -> {
            int idx = index.incrementAndGet();
            if (StringUtils.isBlank(i.getConfigType())) {
                throw new ServiceException(String.format("第%s行数据源类型不能为空", idx));
            }
            if (StringUtils.isBlank(i.getHost())) {
                throw new ServiceException(String.format("第%s行IP不能为空", idx));
            }
            if (StringUtils.isBlank(i.getPort())) {
                throw new ServiceException(String.format("第%s行端口不能为空", idx));
            }
            if (StringUtils.isBlank(i.getSchemaName())) {
                throw new ServiceException(String.format("第%s行schema不能为空", idx));
            }
            if (StringUtils.isBlank(i.getTableName())) {
                throw new ServiceException(String.format("第%s行表名不能为空", idx));
            }
            if (StrUtil.isEmpty(i.getBusSystem())) {
                throw new ServiceException(String.format("第%s行业务系统不能为空", idx));
            }
            if (!systemMap.containsKey(i.getBusSystem())) {
                throw new ServiceException(String.format("第%s行业务系统不存在", idx));
            }
            i.setSystemId(systemMap.get(i.getBusSystem()));
            Long sourceType = map.get(i.getConfigType());
            if (Objects.isNull(sourceType))
                throw new ServiceException(String.format("第%s行，不支持的数据源类型", idx));
            i.setConfigType(sourceType.toString());
        }).collect(Collectors.toSet());
    }

    /**
     * 盘点列表快速补充
     *
     * @param dto request
     * @Date 2022/6/8 17:17
     */
    @Override
    public void append(RequestModel<List<AppendInventoryDTO>> dto) {
        CheckUtil.checkParams(dto.getPrivator());
        for (AppendInventoryDTO appendInventoryDTO : dto.getPrivator()) {
            UpdateWrapper<CoInventory> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("area", appendInventoryDTO.getArea());
            updateWrapper.set("insider", appendInventoryDTO.getInsider());
            updateWrapper.set("outsider", appendInventoryDTO.getOutsider());
            //WHERE
            updateWrapper.eq("operation_id", appendInventoryDTO.getOperationId());
            updateWrapper.eq("label_id", LabelEnum.ZCPD.getCode());
            updateWrapper.eq("system_id", appendInventoryDTO.getSystemId());
           coInventoryMapper.update(new CoInventory(), updateWrapper);
        }
    }

    /**
     * 设置最高敏感级别
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/8/12 18:00
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateConstant(RequestModel<UpdateConstantDto> dto) {

        String customerId = coOperationMapper.selectCustomIdByOperationId(dto.getPrivator().getOperationId());
        QueryWrapper<CoConstant> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_id", customerId);
        List<CoConstant> coConstantList = coConstantMapper.selectList(queryWrapper);
        Integer highestSensitiveLevel = dto.getPrivator().getHighestSensitiveLevel();
        if (CollectionUtils.isEmpty(coConstantList)) {
            CoConstant coConstant = new CoConstant();
            coConstant.setHighestSensitiveLevel(highestSensitiveLevel);
            coConstant.setCustomerId(customerId);
            return coConstantMapper.insert(coConstant);
        }
        UpdateWrapper<CoConstant> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("highest_sensitive_level", highestSensitiveLevel);
        updateWrapper.eq("constant_id", coConstantList.get(0).getConstantId());

        //更新盘点表是否敏感
        QueryWrapper<CoInventory> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("operation_id", dto.getPrivator().getOperationId());
        List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper1);

        final Integer oldLevel = coConstantList.get(0).getHighestSensitiveLevel();
        List<CoInventory> coInventories = coInventoryList.stream().map(co -> {
            int level = DcasUtil.getSensitiveLevel(co.getSensitiveLevel(), oldLevel, highestSensitiveLevel);
            return CoInventory.builder()
                    .inventoryId(co.getInventoryId())
                    .sensitiveLevel(level)
                    .sensitive(DcasUtil.getMatrix(level, highestSensitiveLevel))
                    .build();
        }).collect(Collectors.toList());
        PartitionUtils.part(coInventories, this::updateBatchById);

        return coConstantMapper.update(new CoConstant(), updateWrapper);
    }

    /**
     * 查询最高敏感级别
     *
     * @return * @return int
     * @Date 2022/8/12 18:00
     */
    @Override
    public CoConstant retrieveConstant(RequestModel<OperationIdDto> dto) {
        String customerId = coOperationMapper.selectCustomIdByOperationId(dto.getPrivator().getOperationId());
        QueryWrapper<CoConstant> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_id", customerId);
        List<CoConstant> coConstants = coConstantMapper.selectList(queryWrapper);
        return CollectionUtils.isNotEmpty(coConstants) ? coConstants.get(0) : null;
    }


    /**
     * 导出EXCEL
     *
     * @param response    request
     * @param operationId request
     */
    @Override
    @SneakyThrows
    public void exportExcel(HttpServletResponse response, String operationId) {
        String path = exportExcel(operationId);
        ReportUtil.output(response, path, String.format("%s-%s.xlsx", "资产盘点导出", DateUtils.getExportDateStr(System.currentTimeMillis())));
    }

    @Override
    public String exportExcel(String operationId) throws IOException {
        final int pageSize = 5000;
        final String YES = "是";
        final String NO = "否";
        AtomicInteger pageNum = new AtomicInteger(1);
        RequestModel<QueryInventoryDTO> requestModel = new RequestModel<>();
        requestModel.setPageNum(pageNum.get());
        requestModel.setPageSize(pageSize);
        QueryInventoryDTO dto = new QueryInventoryDTO();
        dto.setOperationId(operationId);
        dto.setLabelId(LabelEnum.ZCPD.getCode());
        requestModel.setPrivator(dto);

        String path = String.join(File.separator, basePath, "temp", "附件：数据资产清单.xlsx");
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        ExcelWriterBuilder writerBuilder = EasyExcel.write(out, InventoryExportExcel.class).registerWriteHandler(WaterMarkHandler.simple(coLicenseMapper.queryCustomName()));
        ExcelWriterSheetBuilder sheetBuilder = writerBuilder.sheet();

        Map<Long, String> systemMap = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode())
            .stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId, TreeLabelDTO::getTreeName));

        // 分批次查询并写入数据
        sheetBuilder.doWrite(() -> {
            List<InventoryExportExcel> pageQuery = new ArrayList<>();
            while (true) {
                PageResult<InventoryPageQueryVO> resultList = selectInventoryList(requestModel);
                if (Objects.isNull(resultList.getList()) || resultList.getList().isEmpty())
                    break;
                List<InventoryExportExcel> collect = resultList.getList().stream().map(r -> {
                    InventoryExportExcel excel = new InventoryExportExcel();
                    excel.setSchemaName(r.getSchemaName());
                    excel.setDataAsset(r.getDataAsset());
                    excel.setIsSensitive(r.getSensitive() == 1 ? YES : NO);
                    excel.setColumnInfo(r.getSensitiveColumnNum() + "/" + r.getColumnNum());
                    excel.setDataSource(String.format("%s:%s/%s", r.getHost(), r.getPort(), r.getDbName()));
                    excel.setDbType(DataSourceType.getType(r.getDbType()).getName());
                    excel.setBusSystem(systemMap.get(r.getSystemId()));
                    excel.setSensitiveLevel(r.getSensitiveLevel());
                    excel.setDataTag(DataTagEnum.valueOf(r.getDataTag()).getDesc());
                    return excel;
                }).collect(Collectors.toList());
                pageQuery.addAll(collect);
                requestModel.setPageNum(pageNum.incrementAndGet());
            }
            return pageQuery;
        });

        return path;
    }

    @DataValidator(type = TemplateTypeEnum.ASSET_TEMPLATE)
    @Override
    @SchemaSwitch(String.class)
    public List<AssetTemplateSelectVO> determineTemplateQuery(String operationId) {
        List<AssetTemplateSelectVO> assetTemplates = assetTemplateMapper.selectAssetTemplateList();
        if (operationId.length() < 10) {
            return assetTemplates;
        } else {
            CoOperation coOperation = coOperationMapper.selectById(operationId);
            List<String> industryList = StrUtil.split(coOperation.getRelatedIndustry(), StrUtil.COMMA);
            Integer selectTemplateId = determineAssetTypeService.getBaseMapper().queryTemplateIdByOperationId(operationId, null);
            List<Long> systemIds = determineAssetTypeService.getBaseMapper().querySystemIdByOperationId(operationId);
            Map<String, String> industryMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("type_id", 2)
                    .eq("status", 0)).stream().collect(Collectors.toMap(t -> t.getId().toString(), Tag::getName));
            return assetTemplates.stream().filter(a -> CoOperationServiceImpl.templateFilter(coOperation.getRelatedDistrict(),
                            industryList, StrUtil.EMPTY, a.getRegionCode(), a.getIndustryCode(), industryMap))
                    .peek(a -> {
                        a.setSelect(Objects.equals(a.getId(), selectTemplateId));
                        a.setSystemId(systemIds.isEmpty() ? LabelEnum.XTDY.getCode() : systemIds.get(0));
                    }).collect(Collectors.toList());
        }
    }

    @Override
    @SchemaSwitch(String.class)
    public List<DetermineAssetDataVO> assetDataLevelList(String operationId, Integer templateId, Long systemId) {
        List<DetermineAssetDataVO> result = new ArrayList<>();
        // 模板分类
        List<AssetType> assetTypeList = assetTypeService.list(new QueryWrapper<AssetType>().eq("template_id", templateId));
        // 模板资产map key:所属分类id + 资产名称 value:资产列表
        Map<Integer, List<AssetData>> assetDataMap = assetDataService.list(new QueryWrapper<AssetData>()
                .eq("template_id", templateId).eq("enable", Boolean.TRUE)).stream().collect(Collectors.groupingBy(AssetData::getTypeId));
        // TODO 2024/02/05 这里切换业务系统可能会出现无法删除原业务系统的资产的问题，因为切换业务系统不会重新调用接口查询模板内容，将已选择的资产id填充到对应模板中.需要前端修改逻辑
        Map<String, DetermineAssetData> determineAssetDataMap = determineAssetDataService.list(new QueryWrapper<DetermineAssetData>().eq("operation_id", operationId)
                .eq("system_id", systemId).eq("template_id", templateId)).stream().collect(Collectors.toMap(a -> a.getTypeId() + a.getName(), Function.identity()));
        // 用于前端区分的唯一id，实际没有作用
        AtomicInteger id = new AtomicInteger(RandomUtil.randomInt());
        buildTree(id, result, assetTypeList.stream().filter(a -> a.getLevel() == 1).collect(Collectors.toList()), assetTypeList, assetDataMap, determineAssetDataMap);

        // 对于选中的模板设置其选中状态
        boolean flag = Objects.equals(templateId, determineAssetTypeService.getBaseMapper().queryTemplateIdByOperationId(operationId, systemId));
        if (flag) {
            Set<Integer> typeIds = determineAssetTypeService.getBaseMapper().selectTypeIdsByOperationId(operationId, systemId);
            Set<String> assetNames = determineAssetDataService.getBaseMapper().selectTypeIdsByOperationId(operationId, systemId);
            setCheckedStatus(result, typeIds, assetNames);
        }

        return result;
    }

    @Override
    @SchemaSwitch(DetermineAssetImportDTO.class)
    @Transactional(rollbackFor = Exception.class)
    public void determineAssetImport(DetermineAssetImportDTO dto) {
        Date now = new Date();
        String account = SecurityUtils.getAccount();
        // 是否与上次应用的是同一个模板
        boolean flag = Objects.equals(dto.getTemplateId(), determineAssetTypeService.getBaseMapper().queryTemplateIdByOperationId(dto.getOperationId(), dto.getSystemId()));
        Set<Integer> typeIds = new HashSet<>();
        Set<String> assetNames = new HashSet<>();
        Set<Integer> delTypeIds = new HashSet<>();
        List<Integer> delDataIds = new ArrayList<>();
        if (flag) {
            typeIds.addAll(determineAssetTypeService.getBaseMapper().selectTypeIdsByOperationId(dto.getOperationId(), dto.getSystemId()));
            assetNames.addAll(determineAssetDataService.getBaseMapper().selectTypeIdsByOperationId(dto.getOperationId(), dto.getSystemId()));
        }
        List<DetermineAssetType> assetTypeInsert = Lists.newArrayList();
        List<DetermineAssetData> assetDataInsert = Lists.newArrayList();
        final Map<String, AssetData> assetDataMap = assetDataService.list(new QueryWrapper<AssetData>()
                .eq("template_id", dto.getTemplateId()).eq("enable", Boolean.TRUE)).stream().collect(Collectors.toMap(a -> a.getTypeId() + a.getName(), Function.identity()));
        // 为只勾选了子节点的父节点设置勾选属性
        setCheckedForParents(null, dto.getAssetDataList());
        collectDetermineData(assetTypeInsert, assetDataInsert, new DetermineAssetDataVO(-1, dto.getAssetDataList()), dto, now, account, StrUtil.EMPTY, assetDataMap, flag, typeIds, assetNames, delTypeIds, delDataIds);
        if (!flag) {
            // 和上次应用的模板不同，删除旧数据
            determineAssetTypeService.remove(new QueryWrapper<DetermineAssetType>().eq("operation_id", dto.getOperationId()).eq("system_id", dto.getSystemId()));
            determineAssetDataService.remove(new QueryWrapper<DetermineAssetData>().eq("operation_id", dto.getOperationId()).eq("system_id", dto.getSystemId()));
        } else {
            // 和上次模板相同，删除此次未选择的数据
            if (CollUtil.isNotEmpty(delTypeIds)) {
                List<Integer> delIds = determineAssetTypeService.getBaseMapper().selectAllTypeIdByOperationIdAndTypeId(dto.getOperationId(), delTypeIds);
                PartitionUtils.part(delIds, (ids) -> determineAssetTypeService.remove(new QueryWrapper<DetermineAssetType>().eq("operation_id", dto.getOperationId()).eq("system_id", dto.getSystemId()).in("type_id", ids)));
                PartitionUtils.part(delIds, (ids) -> determineAssetDataService.remove(new QueryWrapper<DetermineAssetData>().eq("operation_id", dto.getOperationId()).eq("system_id", dto.getSystemId()).in("type_id", ids)));
                PartitionUtils.part(delDataIds, determineAssetDataService::removeByIds);
            }
        }
        // 和上次应用的模板相同，新增不存在的数据
        PartitionUtils.part(assetTypeInsert, determineAssetTypeService::saveBatch);
        PartitionUtils.part(assetDataInsert, determineAssetDataService::saveBatch);
    }

    private void setCheckedForParents(DetermineAssetDataVO parent, List<DetermineAssetDataVO> assetDataList) {
        if (CollUtil.isEmpty(assetDataList))
            return;
        for (DetermineAssetDataVO assetData : assetDataList) {
            setCheckedForParents(assetData, assetData.getChildren());
            if (Objects.nonNull(parent))
                parent.setChecked(assetDataList.stream().anyMatch(DetermineAssetDataVO::getChecked));
        }
    }

    private void setCheckedStatus(List<DetermineAssetDataVO> assetDataList, Set<Integer> typeIds, Set<String> assetNames) {
        if (CollUtil.isEmpty(assetDataList))
            return;
        for (DetermineAssetDataVO assetData : assetDataList) {
            setCheckedStatus(assetData.getChildren(), typeIds, assetNames);
            if (CollUtil.isNotEmpty(assetData.getChildren()))
                assetData.setChecked(assetData.getChildren().stream().allMatch(DetermineAssetDataVO::getChecked));
            else
                assetData.setChecked(assetNames.contains(assetData.getTypeId() + assetData.getName()));
        }
    }

    @Override
    public DetermineCategoryVO queryDetermineCategory(String operationId) {
        DetermineCategoryVO root = new DetermineCategoryVO();
        root.setTypeName("全部资产");
        root.setLevel(0);
        List<DetermineAssetType> determineAssetTypes = determineAssetTypeService.getBaseMapper().selectDistinctType(operationId);
        if (CollUtil.isEmpty(determineAssetTypes)) {
            return root;
        }
        // key : typeId value: 当前分类下各级资产数量
        Map<Integer, List<AssetSensitiveInfoDTO>> assetSensitiveGroup = determineAssetDataService.getBaseMapper()
                .queryAssetSensitiveInfo(operationId).stream().collect(Collectors.groupingBy(AssetSensitiveInfoDTO::getTypeId));
        List<DetermineCategoryVO> firstLevelAssetTypes = buildAssetCategory(determineAssetTypes.stream().filter(a -> a.getLevel() == 1).collect(Collectors.toList()), determineAssetTypes, assetSensitiveGroup, new ArrayList<>());
        mergeChildren(root, firstLevelAssetTypes, assetSensitiveGroup);
        return root;
    }

    @Override
    public PageResult<DetermineContentVO> queryDetermineContent(DetermineContentSearchDTO dto) {
        try (Page<Object> page = PageHelper.startPage(dto.getCurrentPage(), dto.getPageSize())) {
            List<DetermineContentVO> list = determineAssetDataService.getBaseMapper().pageQuery(dto);
            Set<Integer> typeIds = list.stream().flatMap(l -> StrUtil.split(l.getBelongs(), StrUtil.COMMA).stream()).map(Integer::parseInt).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(typeIds)) {
                Map<Integer, String> typeNameMap = determineAssetTypeService.list(new QueryWrapper<DetermineAssetType>()
                        .eq("operation_id", dto.getOperationId()).in("type_id", typeIds))
                        .stream().collect(Collectors.toMap(DetermineAssetType::getTypeId, DetermineAssetType::getTypeName, (k1, k2) -> k1));
                list.forEach(l -> l.setBelongs(StrUtil.split(l.getBelongs(), StrUtil.COMMA).stream().map(b -> typeNameMap.get(Integer.parseInt(b))).collect(Collectors.joining("/"))));
                }
            return PageResult.ofPage(page.getTotal(), list);
        }
    }

    @Override
    public void determineAssetAdd(DetermineAssetDTO dto) {
        int count = determineAssetDataService.count(new QueryWrapper<DetermineAssetData>()
                .eq("operation_id", dto.getOperationId()).eq("type_id", dto.getTypeId()).eq("name", dto.getName().trim()));
        if (count > 0)
            throw new ServiceException("资产已存在，请勿重复添加");
        String belongs = determineAssetTypeService.getBaseMapper().queryPathByTypeId(dto.getOperationId(), dto.getTypeId(), dto.getSystemId());
        DetermineAssetData insert = DetermineAssetData.builder().name(dto.getName().trim())
                .introduce(dto.getIntroduce())
                .templateId(determineAssetTypeService.getBaseMapper().selectTemplateIdByOperationId(dto.getOperationId()))
                .sensitiveLevel(dto.getSensitiveLevel())
                .operationId(dto.getOperationId())
                .typeId(dto.getTypeId())
                .systemId(dto.getSystemId())
                .belongs(belongs)
                .dataTag(dto.getDataTag())
                .build();
        Func.beforeInsert(insert);
        Func.beforeUpdate(insert);
        determineAssetDataService.save(insert);
    }

    @Override
    public void determineAssetDel(IdsReq req) {
        determineAssetDataService.removeByIds(req.getIds());
    }

    @Override
    public void determineAssetUpdate(DetermineAssetDTO dto) {
        if (Objects.isNull(dto.getId()))
            throw new ServiceException("资产id不能为空");
        DetermineAssetData update = determineAssetDataService.getById(dto.getId());
        if (Objects.isNull(update))
            throw new ServiceException("资产不存在");
        int count = determineAssetDataService.count(new QueryWrapper<DetermineAssetData>()
                .eq("operation_id", dto.getOperationId()).eq("type_id", dto.getTypeId()).eq("name", dto.getName().trim()).eq("system_id", dto.getSystemId()).ne("id", dto.getId()));
        if (count > 0)
            throw new ServiceException("当前分类资产已存在");
        String belongs = determineAssetTypeService.getBaseMapper().queryPathByTypeId(dto.getOperationId(), dto.getTypeId(), dto.getSystemId());
        update.setName(dto.getName().trim());
        update.setTypeId(dto.getTypeId());
        update.setIntroduce(dto.getIntroduce());
        update.setBelongs(belongs);
        update.setSensitiveLevel(dto.getSensitiveLevel());
        update.setDataTag(dto.getDataTag());
        Func.beforeUpdate(update);
        determineAssetDataService.updateById(update);
    }

    @Override
    public DetermineAssetViewVO queryAssetView(String operationId) {
        DetermineAssetViewVO vo = new DetermineAssetViewVO();
        List<DetermineAssetData> determineAssetDataList = determineAssetDataService.list(new QueryWrapper<DetermineAssetData>().eq("operation_id", operationId));
        long dataAssetNum = determineAssetDataList.size();
        vo.setDataAssetNum(dataAssetNum);
        if (dataAssetNum == 0)
            return vo;
        long systemNum = determineAssetDataList.stream().map(DetermineAssetData::getSystemId).distinct().count();
        vo.setRelatedSystemNum(systemNum);
        // highSensitiveAssetsNum 核心资产数量
        long coreAssetsNum = determineAssetDataList.stream().filter(d -> Objects.equals(DataTagEnum.CORE.getTag(), d.getDataTag())).count();
        vo.setHighSensitiveAssetsNum(coreAssetsNum);
        vo.setHighSensitiveAssetsProportion(NumberUtil.div(coreAssetsNum * 100, dataAssetNum, 1));
        // importantAssetsNum 重要资产数量
        long importantAssetsNum = determineAssetDataList.stream().filter(d -> Objects.equals(DataTagEnum.IMPORTANT.getTag(), d.getDataTag())).count();
        vo.setMediumSensitiveAssetsNum(importantAssetsNum);
        vo.setMediumSensitiveAssetsProportion(NumberUtil.div(importantAssetsNum * 100, dataAssetNum, 1));
        // generalAssetsNum 一般资产数量
        long generalAssetsNum = determineAssetDataList.stream().filter(d -> Objects.equals(DataTagEnum.GENERAL.getTag(), d.getDataTag())).count();
        vo.setLowSensitiveAssetsNum(generalAssetsNum);
        vo.setLowSensitiveAssetsProportion(NumberUtil.div(generalAssetsNum * 100, dataAssetNum, 1));
        Map<String, Double> map = new TreeMap<>(Comparator.comparing(String::valueOf).reversed());
        // key : 资产敏感等级 value: 资产数量
        Map<Integer, Long> assetSensitiveGroup = determineAssetDataList.stream().collect(Collectors.groupingBy(DetermineAssetData::getSensitiveLevel, Collectors.counting()));
        for (int i = 1; i <= 5; i++) {
            String key = i + "级";
            Long levelNum = assetSensitiveGroup.getOrDefault(i, 0L);
            map.put(key, NumberUtil.div(levelNum * 100, dataAssetNum, 1));
        }
        vo.setLevelMap(map);
        return vo;
    }

    @Override
    public void setUnClassifyLevel(String operationId, Integer sensitiveLevel) {
        UpdateWrapper<CoInventory> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("sensitive_level", sensitiveLevel);
        if (sensitiveLevel >= 3)
            updateWrapper.set("sensitive", 1);
        updateWrapper.eq("operation_id", operationId);
        updateWrapper.eq("sensitive_level", 0);
        coInventoryMapper.update(null, updateWrapper);
    }

    private void mergeChildren(DetermineCategoryVO root, List<DetermineCategoryVO> firstLevelAssetTypes, Map<Integer, List<AssetSensitiveInfoDTO>> assetSensitiveGroup) {
        root.setChildren(firstLevelAssetTypes);
        Map<Integer, Integer> collect = firstLevelAssetTypes.stream().filter(t -> CollUtil.isNotEmpty(t.getAssetInfoList()))
                .flatMap(a -> a.getAssetInfoList().stream()).collect(Collectors.toMap(
                DetermineCategoryVO.AssetSensitiveInfo::getSensitiveLevel,
                DetermineCategoryVO.AssetSensitiveInfo::getAssetCount,
                Integer::sum));
        List<AssetSensitiveInfoDTO> assetSensitiveInfoList = assetSensitiveGroup.get(root.getTypeId());
        if (CollUtil.isNotEmpty(assetSensitiveInfoList)) {
            Map<Integer, Long> assetNumMap = assetSensitiveInfoList.stream().collect(Collectors.groupingBy(AssetSensitiveInfoDTO::getSensitiveLevel, Collectors.counting()));
            collect.forEach((k, v) -> {
                if (assetNumMap.containsKey(k)) {
                    collect.put(k, v + assetNumMap.get(k).intValue());
                }
            });
        }
        root.setAssetInfoList(collect.entrySet().stream()
                .map(entry -> new DetermineCategoryVO.AssetSensitiveInfo(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList()));
        root.setAssetCount(collect.values().stream().mapToInt(Integer::intValue).sum());
    }

    private List<DetermineCategoryVO> buildAssetCategory(List<DetermineAssetType> assetTypes, List<DetermineAssetType> nodes, Map<Integer, List<AssetSensitiveInfoDTO>> assetSensitiveGroup, List<DetermineCategoryVO> result) {
        for (DetermineAssetType assetType : assetTypes) {
            List<DetermineAssetType> children = nodes.stream().filter(a -> a.getParentId().equals(assetType.getTypeId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(children)) {
                List<DetermineCategoryVO> determineCategoryVOS = buildAssetCategory(children, nodes, assetSensitiveGroup, new ArrayList<>());
                DetermineCategoryVO categoryNode = new DetermineCategoryVO();
                categoryNode.setTypeId(assetType.getTypeId());
                categoryNode.setTypeName(assetType.getTypeName());
                categoryNode.setLevel(assetType.getLevel());
                mergeChildren(categoryNode, determineCategoryVOS, assetSensitiveGroup);
                result.add(categoryNode);
            } else {
                DetermineCategoryVO categoryNode = buildAssetCategoryNode(assetType, assetSensitiveGroup);
                result.add(categoryNode);
            }
        }
        return result;
    }

    private DetermineCategoryVO buildAssetCategoryNode(DetermineAssetType assetType, Map<Integer, List<AssetSensitiveInfoDTO>> assetSensitiveGroup) {
        DetermineCategoryVO node = new DetermineCategoryVO();
        node.setTypeId(assetType.getTypeId());
        node.setTypeName(assetType.getTypeName());
        node.setLevel(assetType.getLevel());
        List<AssetSensitiveInfoDTO> assetSensitiveInfoDTOS = assetSensitiveGroup.get(assetType.getTypeId());
        if (CollUtil.isNotEmpty(assetSensitiveInfoDTOS)) {
            Map<Integer, List<AssetSensitiveInfoDTO>> sensitiveMap = assetSensitiveInfoDTOS.stream().collect(Collectors.groupingBy(AssetSensitiveInfoDTO::getSensitiveLevel));
            List<DetermineCategoryVO.AssetSensitiveInfo> assetSensitiveInfos = Lists.newArrayList();
            for (int i = 1; i <= 5; i++) {
                int assetLevelCount = 0;
                if (CollUtil.isNotEmpty(sensitiveMap.get(i))) {
                    Optional<Integer> reduce = sensitiveMap.get(i).stream().map(AssetSensitiveInfoDTO::getAssetCount).reduce(Integer::sum);
                    if (reduce.isPresent()) {
                        assetLevelCount = reduce.get();
                    }
                }
                assetSensitiveInfos.add(new DetermineCategoryVO.AssetSensitiveInfo(i, assetLevelCount));
            }
            node.setAssetInfoList(assetSensitiveInfos);
            node.setAssetCount(assetSensitiveInfos.stream().mapToInt(DetermineCategoryVO.AssetSensitiveInfo::getAssetCount).sum());
        }
        return node;
    }

    private void collectDetermineData(List<DetermineAssetType> assetTypeInsert, List<DetermineAssetData> assetDataInsert,
                                      DetermineAssetDataVO determineAssetData, DetermineAssetImportDTO dto, Date now,
                                      String username, String path, Map<String, AssetData> assetDataMap, boolean flag,
                                      Set<Integer> typeIds, Set<String> assetNames, Set<Integer> delTypeIds, List<Integer> delDataIds) {
        for (DetermineAssetDataVO assetData : determineAssetData.getChildren()) {
            // 没有选择该分类
            if (!assetData.getChecked()) {
                // 如果选择的是上个模板，没选中的资产分类删除
                if (flag) {
                    if (CollUtil.isNotEmpty(assetData.getChildren())) {
                        delTypeIds.add(assetData.getTypeId());
                    } else {
                        delDataIds.add(assetData.getId());
                    }
                }
                continue;
            }
            String belongs = StrUtil.isEmpty(path) ? assetData.getTypeId().toString() : path + StrUtil.COMMA + assetData.getTypeId();
            List<DetermineAssetDataVO> children = assetData.getChildren();
            String key = assetData.getTypeId() + assetData.getName();
            if (CollUtil.isNotEmpty(children)) {
                // 当前应用模板不是上次模板 或者 与上次模板一致但不包括上次资产分类
                if (!flag || !typeIds.contains(assetData.getTypeId())) {
                    // 处理分类
                    assetTypeInsert.add(DetermineAssetType.builder()
                            .operationId(dto.getOperationId())
                            .templateId(dto.getTemplateId())
                            .typeId(assetData.getTypeId())
                            .parentId(determineAssetData.getTypeId())
                            .typeName(assetData.getName())
                            .systemId(dto.getSystemId())
                            .level(assetData.getLevel())
                            .build());
                }
                collectDetermineData(assetTypeInsert, assetDataInsert, assetData, dto, now, username, belongs, assetDataMap, flag, typeIds, assetNames, delTypeIds, delDataIds);
            } else {
                if (!flag || !assetNames.contains(key)) {
                    // 处理资产
                    assetDataInsert.add(DetermineAssetData.builder()
                            .operationId(dto.getOperationId())
                            .templateId(dto.getTemplateId())
                            .typeId(determineAssetData.getTypeId())
                            .name(assetData.getName())
                            .introduce(assetData.getIntroduce())
                            .sensitiveLevel(assetData.getSensitiveLevel())
                            .systemId(dto.getSystemId())
                            .belongs(path)
                            .dataTag(DataTagEnum.GENERAL.getTag())
                            .createTime(now)
                            .createBy(username)
                            .updateTime(now)
                            .updateBy(username)
                            .build());
                }
            }
        }
    }

    private void buildTree(AtomicInteger id, List<DetermineAssetDataVO> result, List<AssetType> assetTypeList, List<AssetType> nodes,
                           Map<Integer, List<AssetData>> assetDataMap, Map<String, DetermineAssetData> determineAssetDataMap) {
        for (AssetType assetType : assetTypeList) {
            List<DetermineAssetDataVO> childList = new ArrayList<>();
            DetermineAssetDataVO vo = new DetermineAssetDataVO();
            vo.setId(id.getAndIncrement());
            vo.setTypeId(assetType.getId());
            vo.setName(assetType.getName());
            vo.setChecked(Boolean.FALSE);
            vo.setLevel(assetType.getLevel());
            if (CollUtil.isNotEmpty(assetDataMap.get(assetType.getId()))) {
                List<AssetData> assetDataList = assetDataMap.get(assetType.getId());
                for (AssetData assetData : assetDataList) {
                    DetermineAssetDataVO dataVO = new DetermineAssetDataVO();
                    String key = assetData.getTypeId() + assetData.getName();
                    if (determineAssetDataMap.containsKey(key)) {
                        dataVO.setId(determineAssetDataMap.get(key).getId());
                    } else {
                        dataVO.setId(id.getAndIncrement());
                    }
                    dataVO.setTypeId(assetData.getTypeId());
                    dataVO.setName(assetData.getName());
                    dataVO.setIntroduce(assetData.getIntroduce());
                    dataVO.setSensitiveLevel(assetData.getSensitiveLevel());
                    dataVO.setChecked(Boolean.FALSE);
                    childList.add(dataVO);
                }
            }
            List<AssetType> children = nodes.stream().filter(a -> a.getParentId().equals(assetType.getId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(children)) {
                buildTree(id, childList, children, nodes, assetDataMap, determineAssetDataMap);
            }
            vo.setChildren(childList);
            result.add(vo);
        }
    }

    @Override
    public void downloadOfflineTool(HttpServletResponse response) {
        // 获取用户目录（在某些情况下可以作为当前工作目录）
        String userDir = System.getProperty("user.dir");

        File checkDir = FileUtil.file(CharSequenceUtil.join(File.separator, userDir, "check"));
        FileUtil.mkdir(checkDir);
        File tempFile = FileUtil.file("tempFile");

        ClassPathResource jarPathResource = new ClassPathResource("tool/discovery/asset-discovery-check-1.0-SNAPSHOT.jar");
        ClassPathResource readmePathResource = new ClassPathResource("tool/discovery/README.md");
        ClassPathResource jrePathResource = new ClassPathResource("tool/jre.zip");
        try {
            // 复制jar文件
            File jarFile = FileUtil.file(CharSequenceUtil.join(File.separator, checkDir.getPath(), jarPathResource.getFilename()));
            FileUtil.touch(jarFile);
            Files.copy(jarPathResource.getInputStream(), jarFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            // 复制redme文件
            File readmeFile = FileUtil.file(CharSequenceUtil.join(File.separator, checkDir.getPath(), readmePathResource.getFilename()));
            FileUtil.touch(readmeFile);
            Files.copy(readmePathResource.getInputStream(), readmeFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            // 复制jre文件
            File jreFile = FileUtil.file(CharSequenceUtil.join(File.separator, checkDir.getPath(), jrePathResource.getFilename()));
            FileUtil.touch(jreFile);
            Files.copy(jrePathResource.getInputStream(), jreFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            // 获取统一数源包
            String path = System.getProperty(DatasourceConstant.MCDATASOURCE_HOME);
            PathUtil.copy(FileUtil.file(path).toPath(), checkDir.toPath(), StandardCopyOption.REPLACE_EXISTING);

            File zipPath = ZipUtil.zip(tempFile, true, checkDir);
            Func.write(response, zipPath, "check.zip");
        } catch (IOException e) {
            log.error("下载离线工具失败", e);
            throw new ServiceException("下载离线工具失败!");
        } finally {
            org.apache.commons.io.FileUtils.deleteQuietly(tempFile);
            org.apache.commons.io.FileUtils.deleteQuietly(checkDir);
        }
    }
}
