package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.PathUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.algorithms.SnowFlake;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.excel.WaterMarkHandler;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.excel.AuthorityUserExportExcel;
import com.dcas.common.model.excel.DataAuthorityExcel;
import com.dcas.common.utils.*;
import com.dcas.common.utils.file.FileUtils;
import com.dcas.common.utils.sign.Base64;
import com.dcas.common.model.excel.AuthorityExportExcel;
import com.dcas.common.model.param.UserAuthoritySaveParam;
import com.dcas.common.model.req.*;
import com.dcas.system.service.DatabaseService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.*;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.vo.*;
import com.dcas.common.mapper.*;
import com.dcas.system.service.CoAuthorityService;
import com.mchz.mcdatasource.core.DatasourceConstant;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据权限服务层
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoAuthorityServiceImpl extends ServiceImpl<CoAuthorityMapper, CoAuthority> implements CoAuthorityService {

    private final CoAuthorityMapper coAuthorityMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoLicenseMapper coLicenseMapper;
    private final DatabaseService databaseService;
    private final CoConstantMapper coConstantMapper;
    private final AuthorityRulesMapper authorityRulesMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final CoAssetViewServiceImpl coAssetViewService;
    private final CoAssetUserServiceImpl coAssetUserService;

    public static final String IMAGE_PNG_BASE64 = "data:image/png;base64,";
    private final SourceConfigMapper sourceConfigMapper;
    private final DiscoverySourceMapper discoverySourceMapper;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final CoAssetUserMapper coAssetUserMapper;
    private final CoDbSecurityMapper coDbSecurityMapper;

    /**
     * 添加数据权限记录
     *
     * @param dto request
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void insertAuthority(RequestModel<AuthorityAddReq> dto) {
        //入参校验
        AuthorityAddReq authorityAddReq = dto.getPrivator();
        CheckUtil.checkParams(authorityAddReq);
        CoAuthority coAuthority = Func.toBean(authorityAddReq, CoAuthority.class);
        List<CoAssetView> coAssetViews = coAssetViewService.selectByQuery(authorityAddReq);
        if (CollUtil.isEmpty(coAssetViews)) {
            CoInventory inventory = coInventoryMapper.selectOne(new QueryWrapper<CoInventory>()
                    .eq("operation_id", coAuthority.getOperationId())
                    //.eq("db_config", dbConfig)
                    .eq("schema_name", coAuthority.getSchemaName())
                    .eq("data_asset", coAuthority.getDataAsset()));
            if (Objects.isNull(inventory))
                throw new ServiceException("资产视图中不存在该表资产");
            StringBuilder pri = new StringBuilder();
            if (Objects.equals(coAuthority.getCrudTable(), "1") || isCurlPri(coAuthority)) {
                pri.append("DBA");
                coAuthority.setInsertTable("1");
                coAuthority.setUpdateTable("1");
                coAuthority.setDeleteTable("1");
                coAuthority.setDropTable("1");
                coAuthority.setSelectTable("1");
            } else {
                if (Objects.equals(coAuthority.getInsertTable(), "1"))
                    pri.append(PriEnum.INSERT.getName());
                if (Objects.equals(coAuthority.getUpdateTable(), "1"))
                    pri.append(StrUtil.COMMA).append(PriEnum.UPDATE.getName());
                if (Objects.equals(coAuthority.getDeleteTable(), "1"))
                    pri.append(StrUtil.COMMA).append(PriEnum.DELETE.getName());
                if (Objects.equals(coAuthority.getDropTable(), "1"))
                    pri.append(StrUtil.COMMA).append(PriEnum.DROP.getName());
                if (Objects.equals(coAuthority.getSelectTable(), "1"))
                    pri.append(StrUtil.COMMA).append(PriEnum.SELECT.getName());
            }
            coAuthority.setPri(pri.toString());
            //coAuthority.setDbConfig(dbConfig);
            coAuthorityMapper.insert(coAuthority);
            saveToAssetView(coAuthority.getOperationId(), null, Collections.singletonList(coAuthority), Boolean.FALSE);
        } else {
            throw new ServiceException("Not support yet");
        }
    }

    private Boolean isCurlPri(CoAuthority coAuthority) {
        String pri = coAuthority.getInsertTable() + coAuthority.getSelectTable()
                + coAuthority.getUpdateTable() + coAuthority.getDeleteTable() + coAuthority.getDropTable();
        return Objects.equals(pri, "11111");
    }

    /**
     * 删除数据权限记录
     *
     * @param dto request
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void deleteAuthorityByIdList(RequestModel<PrimaryKeyListDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        List<String> viewiIdList = dto.getPrivator().getIdList();
        //删除用户权限表
        coAssetUserService.remove(new QueryWrapper<CoAssetUser>().in("asset_id", viewiIdList));
        //删除资产视图表
        coAssetViewService.removeByIds(viewiIdList);
    }

    @Override
    public void deleteByJobId(Long jobId) {
        QueryWrapper<CoAuthority> delete = new QueryWrapper<>();
        delete.eq("job_id", jobId);
        coAuthorityMapper.delete(delete);
    }

    /**
     * 修改数据权限记录
     *
     * @param dto request
     * @return * @return int
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int updateAuthorityById(RequestModel<UpdateAuthorityDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());

        CoAuthority coAuthority = new CoAuthority();
        //复制对象
        BeanUtils.copyProperties(dto.getPrivator(), coAuthority);

        return coAuthorityMapper.updateById(coAuthority);
    }

    /**
     * 查询数据权限记录
     *
     * @param dto request
     * @return * @return List<ConInventoryEntity>
     */
    @Override
    public PageInfo<AuthorityPageVO> queryAuthorityByParams(RequestModel<QueryAuthorityDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        //分页
        try (Page<Object> ignore = PageMethod.startPage(dto.getPageNum(), dto.getPageSize())) {
            //查询结果
            List<AuthorityPageVO> list = coAuthorityMapper.queryAuthorityByParams(dto.getPrivator());
            //设置分页条件，根据list分页
            return new PageInfo<>(list);
        }

    }

    @Override
    @SchemaSwitch(String.class)
    @Transactional(rollbackFor = Exception.class)
    public void fileImport(String operationId, MultipartFile file) throws IOException {
        if (!FileUtils.isExcelFile(file.getOriginalFilename())) {
            throw new ServiceException("不支持的文件格式");
        }

        List<CoDbUserAuthority> authAssetList = new ArrayList<>();
        List<CoDbUserAuthority> authorityList = new ArrayList<>();

        importUserAuthorityList(file, operationId, authAssetList, authorityList);

        // 数据库权限转化规则 -- 放里面来注解才能生效
        Map<Integer, AuthTransformDTO> transfromMap = authorityRulesMapper.selectAllEnabled().stream()
                .map(au -> AuthTransformDTO.builder()
                        .sourceType(au.getSourceType())
                        .superPri(getPris(au.getSuperPri()))
                        .addPri(getPris(au.getAddPri()))
                        .deletePri(getPris(au.getDeletePri()))
                        .dropPri(getPris(au.getDropPri()))
                        .updatePri(getPris(au.getUpdatePri()))
                        .selectPri(getPris(au.getSelectPri()))
                        .build())
                .collect(Collectors.toMap(AuthTransformDTO::getSourceType, o -> o));
        final List<CoDbUserAuthority> authorityTransform = authorityList.stream().filter(p -> StrUtil.isNotEmpty(p.getPri())).map(a -> {
            // ORACLE 数据库不需要做权限转化
            if (Objects.equals(DataSourceType.ORACLE.getCode(), a.getDbType().longValue())
                    || Objects.equals(DataSourceType.DM.getCode(), a.getDbType().longValue())) {
                a.setPri(a.getPri().toUpperCase());
                return a;
            }
            // 其他数据库转为 ORACLE 数据库相应权限
            return authorityTransform(a, transfromMap.get(a.getDbType()));
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 过滤符合的权限
        List<CoDbUserAuthority> userAuthorityList = authorityTransform.stream().filter(a -> PriEnum.MAP.containsKey(a.getPri())).collect(Collectors.toList());
        if (CollUtil.isEmpty(userAuthorityList)) {
            log.warn("无符合权限规则的数据");
        }

        Map<DbConfig, List<CoDbUserAuthority>> assetMap = authAssetList.stream().collect(Collectors.groupingBy(a -> (DbConfig) a.getDbConfig()));
        Map<DbConfig, List<CoDbUserAuthority>> authorityMap = userAuthorityList.stream().collect(Collectors.groupingBy(a -> (DbConfig) a.getDbConfig()));

        List<CoAssetView> coAssetViewList = new ArrayList<>();
        List<CoAssetUser> coAssetUserList = new ArrayList<>();
        List<CoAssetUser> nonPriUser = new ArrayList<>();

        for (Map.Entry<DbConfig, List<CoDbUserAuthority>> assetEntry : assetMap.entrySet()) {
            DbConfig key = assetEntry.getKey();
            boolean flag = Objects.equals(key.getConfigType(), DataSourceType.ORACLE.getCode().toString())
                    || Objects.equals(key.getConfigType(), DataSourceType.DM.getCode().toString());
            List<CoDbUserAuthority> userAuthorities = authorityMap.get(key);
            Map<SchemaTableDTO, Set<AuthorityUsrDTO>> authoritySchemaMap = userAuthorities.stream().collect(Collectors.groupingBy(a ->
                            SchemaTableDTO.getInstance(a.getSchemaName(), a.getObjectName()),
                    Collectors.mapping(a -> new AuthorityUsrDTO(a.getUsername(), a.getPri()), Collectors.toSet())));
            for (CoDbUserAuthority asset : assetEntry.getValue()) {
                String id = SnowFlake.getId();
                CoAssetView assetView = new CoAssetView();
                assetView.setViewId(id);
                assetView.setOperationId(operationId);
                assetView.setLabelId(LabelEnum.SJQX.getCode());
                assetView.setSchemaName(asset.getSchemaName());
                assetView.setDataAsset(asset.getObjectName());
                assetView.setDbType(key.getConfigType());
                DbConfig dbConfig = Func.toBean(key, DbConfig.class);
                dbConfig.setSchemaName(asset.getSchemaName());
                assetView.setDbConfig(dbConfig);
                assetView.setBusSystem(asset.getBusSystem());
                assetView.setJobId(-1L);
                coAssetViewList.add(assetView);
                Set<AuthorityUsrDTO> userPriList = new HashSet<>();
                if (flag)
                    userPriList.add(new AuthorityUsrDTO(asset.getSchemaName(), PriEnum.DBA.getName()));
                Set<AuthorityUsrDTO> authorityUsers= authoritySchemaMap.getOrDefault(SchemaTableDTO.ALL_SCHEMA, new HashSet<>());
                if (CollUtil.isNotEmpty(authorityUsers))
                    userPriList.addAll(authorityUsers);
                // 加入对当前schema所有table都有权限的用户
                Set<AuthorityUsrDTO> schemaAllTables = authoritySchemaMap.get(SchemaTableDTO.getInstance(asset.getSchemaName(), SchemaTableDTO.ALL));
                if (CollUtil.isNotEmpty(schemaAllTables))
                    userPriList.addAll(schemaAllTables);
                SchemaTableDTO authKey = SchemaTableDTO.getInstance(asset.getSchemaName(), asset.getObjectName());
                if (authoritySchemaMap.containsKey(authKey))
                    userPriList.addAll(authoritySchemaMap.get(authKey));
                if (CollUtil.isNotEmpty(userPriList)) {
                    coAssetUserList.addAll(buildAssetUser(assetView, userPriList));
                }
            }
            Set<String> userAccountSet = userAuthorities.stream().map(CoDbUserAuthority::getUsername).collect(Collectors.toSet());
            Set<String> havaAssetUser = coAssetUserList.stream().map(CoAssetUser::getUsername).collect(Collectors.toSet());
            List<String> noPriUserList = userAccountSet.stream().filter(u ->
                    !havaAssetUser.contains(u)).collect(Collectors.toList());

            // 保存没有资产权限的用户
            if (CollUtil.isNotEmpty(noPriUserList)) {
                DbConfig dbConfig = DbConfig.builder()
                        .host(key.getHost())
                        .port(key.getPort())
                        .configType(key.getConfigType())
                        .dbName(StrUtil.isEmpty(key.getDbName()) ? StrUtil.EMPTY : key.getDbName())
                        // 无资产权限的的用户用这个字段存业务系统
                        .schemaName(assetEntry.getValue().get(0).getBusSystem())
                        .build();
                nonPriUser.addAll(noPriUserList.stream().map(u ->
                        CoAssetUser.builder()
                                .operationId(operationId)
                                .assetId("-1")
                                .username(u)
                                .insertPri(Boolean.FALSE)
                                .deletePri(Boolean.FALSE)
                                .updatePri(Boolean.FALSE)
                                .dropPri(Boolean.FALSE)
                                .selectPri(Boolean.FALSE)
                                .crudPri(Boolean.FALSE)
                                .jobId(-1L)
                                .dbConfig(dbConfig)
                                .build()
                ).collect(Collectors.toList()));
            }
        }
        coAssetViewService.deleteByOperationIdAndJobId(operationId, -1L);
        coAssetUserService.remove(new QueryWrapper<CoAssetUser>().eq("operation_id", operationId).eq("job_id", -1));

        PartitionUtils.part(nonPriUser, coAssetUserService::saveBatch);
        PartitionUtils.part(coAssetViewList, coAssetViewService::saveBatch);
        PartitionUtils.part(coAssetUserList, coAssetUserService::saveBatch);
    }

    private void importUserAuthorityList(MultipartFile file, String operationId, List<CoDbUserAuthority> authAssetList,
                                         List<CoDbUserAuthority> authorityList) throws IOException {
        List<DataAuthorityExcel> dataAssetList = Func.fileAttestationPart(file, DataAuthorityExcel.class, 0L);
        if (CollUtil.isEmpty(dataAssetList)) {
            throw new ServiceException("导入模板资产不能为空");
        }
        final Map<String, Long> map = Arrays.stream(DataSourceType.values()).collect(
                Collectors.toMap(DataSourceType::getName, DataSourceType::getCode));
        Map<String, Long> systemMap = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode())
                .stream().collect(Collectors.toMap(TreeLabelDTO::getTreeName, TreeLabelDTO::getTreeId));
        final Map<String, DbConfig> dbConfigMap = new HashMap<>();
        AtomicInteger index = new AtomicInteger(1);
        authAssetList.addAll(dataAssetList.stream().map(i -> {
            if (StrUtil.isEmpty(i.getConfigType()))
                throw new ServiceException(String.format("第%s行，数据源类型不能为空", index));
            if (StrUtil.isEmpty(i.getHost()))
                throw new ServiceException(String.format("第%s行，IP不能为空", index));
            if (StrUtil.isEmpty(i.getPort()))
                throw new ServiceException(String.format("第%s行，端口不能为空", index));
            if (StrUtil.isEmpty(i.getSchemaName()))
                throw new ServiceException(String.format("第%s行，模式名不能为空", index));
            if (StrUtil.isEmpty(i.getObjectName()))
                throw new ServiceException(String.format("第%s行，表名不能为空", index));
            Long sourceType = map.get(i.getConfigType());
            if (Objects.isNull(sourceType))
                throw new ServiceException(String.format("第%s行，不支持的数据源类型", index));
            if (StrUtil.isEmpty(i.getBusSystem()))
                throw new ServiceException(String.format("第%s行，业务系统不能为空", index));
            CoDbUserAuthority bean = Func.toBean(i, CoDbUserAuthority.class);
            if (!systemMap.containsKey(i.getBusSystem()))
                throw new ServiceException(String.format("第%s行，业务系统不存在", index));
            bean.setBusSystem(i.getBusSystem());
            bean.setDbType(sourceType.intValue());
            bean.setSchemaName(i.getSchemaName());
            String dbName = StrUtil.isEmpty(i.getDbName()) ? StrUtil.EMPTY : i.getDbName();
            String key = StrUtil.join(StrUtil.COMMA, i.getHost(), i.getPort(), sourceType, dbName);
            if (!dbConfigMap.containsKey(key))
                dbConfigMap.put(key, new DbConfig(i.getHost(), i.getPort(), sourceType.toString(), dbName, null));
            bean.setDbConfig(dbConfigMap.get(key));
            return bean;
        }).collect(Collectors.toList()));

        List<DataAuthorityExcel> importList = Func.fileAttestationPart(file, DataAuthorityExcel.class, 1L);
        if (CollUtil.isEmpty(importList)) {
            throw new ServiceException("导入模板权限不能为空");
        }
        AtomicInteger index2 = new AtomicInteger(1);
        authorityList.addAll(importList.stream().map(i -> {
            int in = index2.incrementAndGet();
            if (StrUtil.isEmpty(i.getConfigType()))
                throw new ServiceException(String.format("第%s行，数据源类型不能为空", in));
            if (StrUtil.isEmpty(i.getHost()))
                throw new ServiceException(String.format("第%s行，IP不能为空", in));
            if (StrUtil.isEmpty(i.getPort()))
                throw new ServiceException(String.format("第%s行，端口不能为空", in));
            if (StrUtil.isEmpty(i.getSchemaName()))
                throw new ServiceException(String.format("第%s行，模式名不能为空", in));
            if (StrUtil.isEmpty(i.getObjectName()))
                throw new ServiceException(String.format("第%s行，表名不能为空", in));
            if (StrUtil.isEmpty(i.getUsername()))
                throw new ServiceException(String.format("第%s行，用户不能为空", in));
            if (StrUtil.isEmpty(i.getPri()))
                throw new ServiceException(String.format("第%s行，权限字符不能为空", in));
            Long sourceType = map.get(i.getConfigType());
            if (Objects.isNull(sourceType))
                throw new ServiceException(String.format("第%s行，不支持的数据源类型", in));
            CoDbUserAuthority bean = Func.toBean(i, CoDbUserAuthority.class);
            bean.setDbType(sourceType.intValue());
            bean.setSchemaName(i.getSchemaName());
            String dbName = StrUtil.isEmpty(i.getDbName()) ? StrUtil.EMPTY : i.getDbName();
            String key = StrUtil.join(StrUtil.COMMA, i.getHost(), i.getPort(), sourceType, dbName);
            if (!dbConfigMap.containsKey(key))
                dbConfigMap.put(key, new DbConfig(i.getHost(), i.getPort(), sourceType.toString(), dbName, null));
            bean.setDbConfig(dbConfigMap.get(key));
            return bean;
        }).collect(Collectors.toList()));
    }

    /**
     * 数据导入
     *
     * @Date 2022/6/2 11:41
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @SchemaSwitch(value = String.class)
    public void importData(List<CoDbUserAuthority> userAuthorities, String operationId, CoDbSecurity job) {
        Set<String> userAccountSet = userAuthorities.stream().map(CoDbUserAuthority::getUsername).collect(Collectors.toSet());
        // 数据库权限转化规则 -- 放里面来注解才能生效
        Map<Integer, AuthTransformDTO> transfromMap = authorityRulesMapper.selectAllEnabled().stream()
                .map(au -> AuthTransformDTO.builder()
                        .sourceType(au.getSourceType())
                        .superPri(getPris(au.getSuperPri()))
                        .addPri(getPris(au.getAddPri()))
                        .deletePri(getPris(au.getDeletePri()))
                        .dropPri(getPris(au.getDropPri()))
                        .updatePri(getPris(au.getUpdatePri()))
                        .selectPri(getPris(au.getSelectPri()))
                        .build())
                .collect(Collectors.toMap(AuthTransformDTO::getSourceType, o -> o));
        final List<CoDbUserAuthority> authorityTransform = userAuthorities.stream().filter(p -> StrUtil.isNotEmpty(p.getPri())).map(a -> {
            // ORACLE 数据库不需要做权限转化
            if (Objects.equals(DataSourceType.ORACLE.getCode(), a.getDbType().longValue())
                    || Objects.equals(DataSourceType.DM.getCode(), a.getDbType().longValue())) {
                a.setPri(a.getPri().toUpperCase());
                return a;
            }
            // 其他数据库转为 ORACLE 数据库相应权限
            return authorityTransform(a, transfromMap.get(a.getDbType()));
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 过滤符合的权限
        List<CoDbUserAuthority> userAuthorityList = authorityTransform.stream().filter(a -> PriEnum.MAP.containsKey(a.getPri())).collect(Collectors.toList());

        SourceConfig sourceConfig = sourceConfigMapper.selectById(job.getConfigId());
        String busSystem = sourceConfigMapper.queryBusSystemByConfigId(sourceConfig.getId());
        if (StrUtil.isEmpty(busSystem))
            busSystem = sourceConfigMapper.queryBusSystemByConfigIdWithPre(sourceConfig.getId());

        if (CollUtil.isEmpty(userAuthorityList)) {
            log.warn("无符合权限规则的数据");
            return;
        }

        List<CoAssetView> assetViews = new ArrayList<>();
        List<CoAssetUser> assetUsers = new ArrayList<>();
        if (Objects.nonNull(job.getConfigId())) {
            // oracle或者达梦需要添加用户名为当前schema的用户DBA权限
            boolean flag = Objects.equals(sourceConfig.getConfigType().longValue(), DataSourceType.ORACLE.getCode())
                    || Objects.equals(sourceConfig.getConfigType().longValue(), DataSourceType.DM.getCode());
            DiscoverySource discoverySource = discoverySourceMapper.selectOne(new QueryWrapper<DiscoverySource>().eq("source_id", sourceConfig.getId()));
            String schemas = null;
            if (Objects.nonNull(discoverySource) && StrUtil.isNotEmpty(discoverySource.getSchemas())) {
                schemas = String.join(StrUtil.COMMA, JSONUtil.parseArray(discoverySource.getSchemas()).toList(String.class));
            }
            if (StrUtil.isEmpty(schemas)) {
                PreSourceConfig preSourceConfig = preSourceConfigMapper.selectOne(new QueryWrapper<PreSourceConfig>().eq("config_id", job.getConfigId()));
                schemas = String.join(StrUtil.COMMA, JSONUtil.parseArray(preSourceConfig.getSchemas()).toList(String.class));
            }
            List<SchemaTableDTO> schemaTableList = new ArrayList<>();
            if ((long)sourceConfig.getConfigType() == (DataSourceType.DM.getCode())) {
                if (StrUtil.isEmpty(schemas)) {
                    schemaTableList = databaseService.listSourceTable(sourceConfig.getId(), null, Boolean.FALSE);
                } else {
                    List<String> stringList = StrUtil.split(schemas, StrUtil.COMMA);
                    for (String schema : stringList) {
                        schemaTableList.addAll(databaseService.listSourceTable(sourceConfig.getId(), schema, Boolean.FALSE));
                    }
                }
            } else {
                schemaTableList = databaseService.listSourceTable(sourceConfig.getId(), StrUtil.isEmpty(schemas) ? null : schemas, Boolean.FALSE);
            }
            Map<String, List<String>> schemaTableMap = schemaTableList.stream().collect(
                    Collectors.groupingBy(SchemaTableDTO::getSchemaName, Collectors.mapping(SchemaTableDTO::getTableName, Collectors.toList())));
            Map<SchemaTableDTO, Set<AuthorityUsrDTO>> authorityMap = userAuthorityList.stream().collect(Collectors.groupingBy(a ->
                            SchemaTableDTO.getInstance(a.getSchemaName(), a.getObjectName()),
                    Collectors.mapping(a -> new AuthorityUsrDTO(a.getUsername(), a.getPri()), Collectors.toSet())));
            for (Map.Entry<String, List<String>> entry : schemaTableMap.entrySet()) {
                String schemaName = entry.getKey();
                for (String tableName : entry.getValue()) {
                    CoAssetView coAssetView = buildAssetView(operationId, sourceConfig, busSystem, job.getId(), tableName, schemaName);
                    assetViews.add(coAssetView);
                    Set<AuthorityUsrDTO> userPriList = new HashSet<>();
                    if (flag && userAccountSet.contains(schemaName))
                        userPriList.add(new AuthorityUsrDTO(schemaName, PriEnum.DBA.getName()));
                    Set<AuthorityUsrDTO> authorityUsers= authorityMap.getOrDefault(SchemaTableDTO.ALL_SCHEMA, new HashSet<>());
                    if (CollUtil.isNotEmpty(authorityUsers))
                        userPriList.addAll(authorityUsers);
                    // 加入对当前schema所有table都有权限的用户
                    Set<AuthorityUsrDTO> schemaAllTable = authorityMap.get(SchemaTableDTO.getInstance(schemaName, SchemaTableDTO.ALL));
                    if (CollUtil.isNotEmpty(schemaAllTable))
                        userPriList.addAll(schemaAllTable);
                    SchemaTableDTO key = SchemaTableDTO.getInstance(schemaName, tableName);
                    if (authorityMap.containsKey(key))
                        userPriList.addAll(authorityMap.get(key));
                    if (CollUtil.isNotEmpty(userPriList)) {
                        assetUsers.addAll(buildAssetUser(coAssetView, userPriList));
                    }
                }
            }
        }


        Set<String> havaAssetUser = assetUsers.stream().map(CoAssetUser::getUsername).collect(Collectors.toSet());
        List<String> noPriUserList = userAccountSet.stream().filter(u ->
                !havaAssetUser.contains(u)).collect(Collectors.toList());

        // 保存没有资产权限的用户
        if (CollUtil.isNotEmpty(noPriUserList)) {
            DbConfig dbConfig = DbConfig.builder()
                    .host(sourceConfig.getHost())
                    .port(sourceConfig.getPort())
                    .configType(sourceConfig.getConfigType().toString())
                    .dbName(StrUtil.isEmpty(sourceConfig.getDbName()) ? StrUtil.EMPTY : sourceConfig.getDbName())
                    // 无资产权限的的用户用这个字段存业务系统
                    .schemaName(busSystem)
                    .build();
            List<CoAssetUser> nonPriUser = noPriUserList.stream().map(u ->
                    CoAssetUser.builder()
                            .operationId(operationId)
                            .assetId("-1")
                            .username(u)
                            .insertPri(Boolean.FALSE)
                            .deletePri(Boolean.FALSE)
                            .updatePri(Boolean.FALSE)
                            .dropPri(Boolean.FALSE)
                            .selectPri(Boolean.FALSE)
                            .crudPri(Boolean.FALSE)
                            .jobId(job.getId())
                            .dbConfig(dbConfig)
                            .build()
            ).collect(Collectors.toList());
            coAssetUserService.remove(new QueryWrapper<CoAssetUser>().eq("operation_id", operationId).eq("job_id", job.getId()));
            PartitionUtils.part(nonPriUser, coAssetUserService::saveBatch);
        }

        coAssetViewService.deleteByOperationIdAndJobId(operationId, job.getId());

        PartitionUtils.part(assetViews, coAssetViewService::saveBatch);
        PartitionUtils.part(assetUsers, coAssetUserService::saveBatch);
    }

    private List<CoAssetUser> buildAssetUser(CoAssetView coAssetView, Set<AuthorityUsrDTO> authorityUsers) {
        Map<String, Set<String>> userAuthMap = authorityUsers.stream().collect(
                Collectors.groupingBy(AuthorityUsrDTO::getUsername, Collectors.mapping(AuthorityUsrDTO::getPri, Collectors.toSet())));
        return buildAssetUser(coAssetView, userAuthMap);
    }

    private CoAssetView buildAssetView(String operationId, SourceConfig sourceConfig, String busSystem, Long id, String tableName, String schemaName) {
        CoAssetView assetView = new CoAssetView();
        String viewId = SnowFlake.getId();
        assetView.setViewId(viewId);
        assetView.setOperationId(operationId);
        assetView.setJobId(id);
        assetView.setLabelId(LabelEnum.SJQX.getCode());
        assetView.setSchemaName(schemaName);
        assetView.setDataAsset(tableName);
        assetView.setBusSystem(busSystem);
        DbConfig dbConfig = new DbConfig();
        dbConfig.setHost(sourceConfig.getHost());
        dbConfig.setPort(sourceConfig.getPort());
        dbConfig.setSchemaName(schemaName);
        dbConfig.setConfigType(sourceConfig.getConfigType().toString());
        dbConfig.setDbName(Objects.isNull(sourceConfig.getDbName()) ? StrUtil.EMPTY : sourceConfig.getDbName());
        assetView.setDbConfig(dbConfig);
        assetView.setDbType(dbConfig.getConfigType());
        return assetView;
    }

    private List<CoAssetUser> buildAssetUser(CoAssetView assetView, Map<String, Set<String>> userAuthMap) {
        return userAuthMap.entrySet().stream().map(ua -> {
            CoAssetUser coAssetUser = new CoAssetUser();
            coAssetUser.setOperationId(assetView.getOperationId());
            coAssetUser.setAssetId(assetView.getViewId());
            coAssetUser.setJobId(assetView.getJobId());
            coAssetUser.setDbConfig(assetView.getDbConfig());
            coAssetUser.setUsername(ua.getKey());
            boolean isAdmin = ua.getValue().stream().anyMatch(p -> Objects.equals(p, PriEnum.DBA.getName()));
            coAssetUser.setCrudPri(isAdmin);
            coAssetUser.setInsertPri(isAdmin ? Boolean.TRUE : ua.getValue().stream().anyMatch(p -> Objects.equals(p, PriEnum.INSERT.getName()) || Objects.equals(p, PriEnum.INSERT_ANY_TABLE.getName())));
            coAssetUser.setDeletePri(isAdmin ? Boolean.TRUE : ua.getValue().stream().anyMatch(p -> Objects.equals(p, PriEnum.DELETE.getName()) || Objects.equals(p, PriEnum.DELETE_ANY_TABLE.getName())));
            coAssetUser.setUpdatePri(isAdmin ? Boolean.TRUE : ua.getValue().stream().anyMatch(p -> Objects.equals(p, PriEnum.UPDATE.getName()) || Objects.equals(p, PriEnum.UPDATE_ANY_TABLE.getName())));
            coAssetUser.setDropPri(isAdmin ? Boolean.TRUE : ua.getValue().stream().anyMatch(p -> Objects.equals(p, PriEnum.DROP.getName()) || Objects.equals(p, PriEnum.DROP_ANY_TABLE.getName())));
            coAssetUser.setSelectPri(isAdmin ? Boolean.TRUE : ua.getValue().stream().anyMatch(p -> Objects.equals(p, PriEnum.SELECT.getName()) || Objects.equals(p, PriEnum.SELECT_ANY_TABLE.getName())));
            coAssetUser.setCrudPri(coAssetUser.getInsertPri() && coAssetUser.getDeletePri() && coAssetUser.getUpdatePri() && coAssetUser.getDropPri() && coAssetUser.getSelectPri());
            return coAssetUser;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveToAssetView(String operationId, Long jobId, List<CoAuthority> coAuthorityList, Boolean needDelete) {
        //2.将数据权限表根据data_asset、db_type、bus_system分组插入资产视图表
        List<CoAssetUser> coAssetUsers = new ArrayList<>();
        List<CoAssetView> coAssetViewList = new ArrayList<>();
        Map<DbConfig, List<CoAuthority>> assetGroup = coAuthorityList.stream().collect(Collectors.groupingBy(a -> (DbConfig) a.getDbConfig()));
        assetGroup.forEach((dbConfig, coAuthorities) -> {
            String configType = dbConfig.getConfigType();

            Map<Pair<String, String>, List<CoAuthority>> businessAssetGroup = coAuthorities.stream().collect(Collectors.groupingBy(a -> new Pair<>(a.getDataAsset(), a.getBusSystem())));

            businessAssetGroup.forEach((pair, businessAsset) -> {
                CoAssetView assetView = new CoAssetView();
                String assetId = SnowFlake.getId();
                assetView.setViewId(assetId);
                assetView.setOperationId(operationId);
                assetView.setLabelId(LabelEnum.SJQX.getCode());
                assetView.setJobId(jobId);
                assetView.setAuthorityIds(StringUtils.join(businessAsset.stream().map(CoAuthority::getAuthorityId).toArray(), ","));
                //分组后盘点id是只有一条，确认是不是只有一条
                assetView.setDataAsset(pair.getKey());
                assetView.setDbType(configType);
                assetView.setDbConfig(dbConfig);
                assetView.setBusSystem(pair.getValue());
                coAssetUsers.addAll(collectUserAuth(assetId, businessAsset));
                coAssetViewList.add(assetView);
            });
        });
        if (needDelete) {
            coAssetViewService.getBaseMapper().deleteByOperationIdAndDbConfig(operationId, assetGroup.keySet());
        }
        PartitionUtils.part(coAssetViewList, coAssetViewService::saveBatch);
        PartitionUtils.part(coAssetUsers, coAssetUserService::saveBatch);
    }

    private List<CoAssetUser> collectUserAuth(String assetId, List<CoAuthority> authorities) {
        return authorities.stream().map(a ->
             CoAssetUser.builder()
                    .assetId(assetId)
                    .operationId(a.getOperationId())
                    .username(a.getUsername())
                    .insertPri(a.hasInsertPermission())
                    .deletePri(a.hasDeletePermission())
                    .dropPri(a.hasDropPermission())
                    .updatePri(a.hasUpdatePermission())
                    .selectPri(a.hasSelectPermission())
                    .crudPri(a.hasCrudPermission())
                    .build()
        ).collect(Collectors.toList());
    }

    /**
     * 转化能匹配到的权限
     *
     * @param auth 数据权限（来源可能是前台导入，也可能是连接查询）
     * @param rule 转化规则
     * @return 转化后的数据权限
     */
    private CoDbUserAuthority authorityTransform(CoDbUserAuthority auth, AuthTransformDTO rule) {
        if (Objects.isNull(rule))
            return null;
        // 数据库查询权限
        String pri = auth.getPri().toUpperCase();
        String str = String.join(StrUtil.COMMA, pri, auth.getSchemaName(), auth.getObjectName());
        if (rule.getAddPri().contains(str)) {
            auth.setPri(PriEnum.INSERT_ANY_TABLE.getName());
            return auth;
        } else if (rule.getDeletePri().contains(str)) {
            auth.setPri(PriEnum.DELETE_ANY_TABLE.getName());
            return auth;
        } else if (rule.getDropPri().contains(str)) {
            auth.setPri(PriEnum.DROP_ANY_TABLE.getName());
            return auth;
        } else if (rule.getUpdatePri().contains(str)) {
            auth.setPri(PriEnum.UPDATE_ANY_TABLE.getName());
            return auth;
        } else if (rule.getSelectPri().contains(str)) {
            auth.setPri(PriEnum.SELECT_ANY_TABLE.getName());
            return auth;
        } else if (rule.getSuperPri().stream().anyMatch(r -> r.contains(pri))) {
            auth.setPri(PriEnum.DBA.getName());
            return auth;
        }
        auth.setPri(pri);
        return auth;
    }

    private Set<String> getPris(String priStr) {
        return StrUtil.split(priStr, StrUtil.AT).stream().map(String::toUpperCase).collect(Collectors.toSet());
    }

    /**
     * 分析结果--数据权限分析结果展示
     *
     * @param dto request
     * @return * @return String
     */
    @Override
    public List<ReportAuthorityStatusQuoVo> queryStatusQuo(RequestModel<OperationIdDto> dto) {
        CheckUtil.checkParams(dto.getPrivator());
        List<ReportAuthorityStatusQuoVo> voList = new ArrayList<>();
        AuthorityOverviewDTO userPermission = coAuthorityMapper.queryStatusQuo(dto.getPrivator().getOperationId());
        int totalNum = userPermission.getTotalNum();
        voList.add(new ReportAuthorityStatusQuoVo(ReportAuthorityPriEnum.CRUD_TABLE.getInfo(), userPermission.getCurdPri(), totalNum));
        voList.add(new ReportAuthorityStatusQuoVo(ReportAuthorityPriEnum.INSERT_TABLE.getInfo(), userPermission.getInsertPri(), totalNum));
        voList.add(new ReportAuthorityStatusQuoVo(ReportAuthorityPriEnum.DELETE_TABLE.getInfo(), userPermission.getDeletePri(), totalNum));
        voList.add(new ReportAuthorityStatusQuoVo(ReportAuthorityPriEnum.DROP_TABLE.getInfo(), userPermission.getDropPri(), totalNum));
        voList.add(new ReportAuthorityStatusQuoVo(ReportAuthorityPriEnum.UPDATE_TABLE.getInfo(), userPermission.getUpdatePri(), totalNum));
        voList.add(new ReportAuthorityStatusQuoVo(ReportAuthorityPriEnum.SELECT_TABLE.getInfo(), userPermission.getSelectPri(), totalNum));
        return voList;
    }


    /**
     * 分析结果--用户权限分类统计结果
     * @param dto  request
     * @return * @return List<ReportAuthorityByUserVo>
     */
    @Override
    public List<ReportAuthorityByUserVo> queryAuthorityByUser(RequestModel<QueryAuthorityReportDto> dto) {
        CheckUtil.checkParams(dto.getPrivator());
        String operationId = dto.getPrivator().getOperationId();
        int highest;
        QueryWrapper<CoConstant> queryWrapper = new QueryWrapper<>();
        String customerId = coOperationMapper.selectCustomIdByOperationId(operationId);
        queryWrapper.eq("customer_id", customerId);
        CoConstant coConstant = coConstantMapper.selectOne(queryWrapper);
        if (Objects.nonNull(coConstant) && coConstant.getHighestSensitiveLevel() != 0)
            highest = coConstant.getHighestSensitiveLevel();
        else {
            highest = 8;
        }
        Integer authorityType = dto.getPrivator().getAuthorityType();
        // 查询用户
        List<String> usernameList = coAuthorityMapper.queryUserTableNum(operationId);

        // 按业务系统分组
        Map<String, List<UserAuthorityDTO>> busSystemGroup = coAuthorityMapper.queryUserAuthority(operationId, authorityType)
                .stream().collect(Collectors.groupingBy(UserAuthorityDTO::getBusSystem));

        List<ReportAuthorityByUserVo> resultList = new ArrayList<>();
        busSystemGroup.forEach((busSystem, list)->{
            Map<String, List<UserAuthorityDTO>> userAuthorityGroup =
                list.stream().collect(Collectors.groupingBy(UserAuthorityDTO::getUsername));
            // 根据用户分组查询敏感表数量
            List<ReportAuthorityByUserVo> result = usernameList.stream().map(u -> {
                ReportAuthorityByUserVo vo = new ReportAuthorityByUserVo();
                vo.setUsername(u);
                if (!userAuthorityGroup.containsKey(u)) {
                    vo.setTableNum(0);
                    vo.setSensitiveTableNum(0);
                    return vo;
                }
                List<UserAuthorityDTO> coInventoryList = userAuthorityGroup.get(u);
                long sensitiveTableNum = coInventoryList.stream().filter(v -> v.getSensitive() == 1).count();


                vo.setTableNum(coInventoryList.size());
                vo.setSensitiveTableNum((int) sensitiveTableNum);
                vo.setBusSystem(busSystem);
                //每个用户的敏感等级比例
                List<Proportion> proportionList = new ArrayList<>();
                for (SensitiveLevelEnum sensitiveLevelEnum : SensitiveLevelEnum.values()) {
                    if (sensitiveLevelEnum.getCode() > highest)
                        continue;
                    long count = coInventoryList.stream().filter(a -> a.getSensitiveLevel().equals(sensitiveLevelEnum.getCode())).count();
                    double proportionValue = !coInventoryList.isEmpty() ? (double) count / coInventoryList.size() * 100 : 0;
                    Proportion proportion = new Proportion();
                    proportion.setLevel(sensitiveLevelEnum.getInfo());
                    proportion.setProportion(Arith.round(proportionValue, 2));
                    proportion.setCount(count);
                    proportionList.add(proportion);
                }
                vo.setProportionList(proportionList);
                return vo;
            }).collect(Collectors.toList());
            resultList.addAll(result);
        });
        return resultList.stream().sorted(Comparator.comparing(ReportAuthorityByUserVo::getTableNum).reversed())
            .limit(10).collect(Collectors.toList());
    }


    /**
     * 分析结果-用户权限-资产数量一览
     *
     * @param dto request
     * @return * @return List<AuthorityAnalysisUserAssetsInsider>
     */
    @Override
    public List<ReportAuthorityUserAssetVo> queryUserAssetsResult(RequestModel<OperationIdDto> dto) {
        CheckUtil.checkParams(dto.getPrivator());
        String operationId = dto.getPrivator().getOperationId();
        return coAuthorityMapper.queryUserAssetsResult(operationId);
    }

    /**
     * 导出word
     *
     * @param response request
     * @throws IOException IO异常
     * @Date 2022/7/27 15:45
     */
    @Override
    public void exportWord(HttpServletResponse response, RequestModel<ExportWordDto> dto) throws IOException {

        ClassPathResource classPathResource = new ClassPathResource("template/AuthorityExportTemplate.docx");
        InputStream inputStream = classPathResource.getInputStream();

        //数据模型
        Map<String, Object> model = new HashMap<>();

        //准备数据
        String operationId = dto.getPrivator().getOperationId();
        //1、2阶段查询
        QueryProjectOperationExportVo poVo = coOperationMapper.queryOperationExport(dto.getPrivator().getOperationId());

        //文本
        model.put("customerName", poVo.getCustomerName());
        model.put("projectName", poVo.getProjectName());
        model.put("date", LocalDate.now());
        model.put("relatedSystem", StringUtils.replace(poVo.getRelatedSystem(),",","、"));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        model.put("createTime", simpleDateFormat.format(poVo.getCreateTime()));
        model.put("completedDate", ObjectUtils.isEmpty(poVo.getCompletedDate()) ? null : simpleDateFormat.format(poVo.getCompletedDate()));
        model.put("customerDirector", poVo.getCustomerDirector());
        model.put("projectManager", poVo.getProjectManager());
        model.put("executor", poVo.getExecutorAccount());
        model.put("reviewer", poVo.getReviewerAccount());


        //文本3.1.2数据资产分析
        List<String> relatedSystemList = ObjectUtils.isEmpty(poVo.getRelatedSystem()) ? new ArrayList<>() : Arrays.asList(poVo.getRelatedSystem().split(","));
        model.put("relatedSystemNum",relatedSystemList.size());
        //查询资产盘点表
        QueryWrapper<CoInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper);
        //数据资产总条数
        int dataAssetsTotal = coInventoryList.size();
        //高敏感资产
        List<CoInventory> highSensitiveAssetsList = coInventoryList.stream().filter(p -> p.getSensitive().intValue() == NumEnum.ONE.getCode()).collect(Collectors.toList());
        //高敏感资产占比
        double highSensitiveAssetsProportion = Arith.round(Double.valueOf(highSensitiveAssetsList.size()) / Double.valueOf(dataAssetsTotal) * 100, 2);
        model.put("dataAssetsNum", dataAssetsTotal);
        model.put("highSensitiveAssetsNum",highSensitiveAssetsList.size());
        model.put("highSensitiveAssetsProportion", highSensitiveAssetsProportion);


        //3.2.2 高危数据权限分析
        RequestModel<OperationIdDto> requestModel = new RequestModel<>();
        OperationIdDto operationIdDto = new OperationIdDto();
        operationIdDto.setOperationId(operationId);
        requestModel.setPrivator(operationIdDto);
        List<ReportAuthorityStatusQuoVo> statusQuoList = this.queryStatusQuo(requestModel);
        int crudTableNum = 0;
        int insertTableNum = 0;
        int deleteTableNum = 0;
        int dropTableNum = 0;
        int updateTableNum = 0;
        int selectTableNum = 0;
        int usernameTotal = 0;
        for (ReportAuthorityStatusQuoVo s : statusQuoList) {
            if(Objects.equals(s.getName(), ReportAuthorityPriEnum.CRUD_TABLE.getInfo())){
                crudTableNum = s.getUsernameNum();
            }else if(Objects.equals(s.getName(), ReportAuthorityPriEnum.INSERT_TABLE.getInfo())){
                insertTableNum = s.getUsernameNum();
            }else if(Objects.equals(s.getName(), ReportAuthorityPriEnum.DELETE_TABLE.getInfo())){
                deleteTableNum = s.getUsernameNum();
            }else if(Objects.equals(s.getName(), ReportAuthorityPriEnum.DROP_TABLE.getInfo())){
                dropTableNum = s.getUsernameNum();
            }else if(Objects.equals(s.getName(), ReportAuthorityPriEnum.UPDATE_TABLE.getInfo())){
                updateTableNum = s.getUsernameNum();
            }else if(Objects.equals(s.getName(), ReportAuthorityPriEnum.SELECT_TABLE.getInfo())){
                selectTableNum = s.getUsernameNum();
            }
            usernameTotal = s.getTotal();
        }

        double crudTableNumProportion = usernameTotal > 0 ?  Double.valueOf(crudTableNum)/Double.valueOf(usernameTotal) : 0;
        double dropTableNumProportion = usernameTotal > 0 ?  Double.valueOf(dropTableNum)/Double.valueOf(usernameTotal) : 0;
        double deleteTableNumProportion = usernameTotal > 0 ?  Double.valueOf(deleteTableNum)/Double.valueOf(usernameTotal) : 0;
        double updateTableNumProportion = usernameTotal > 0 ?  Double.valueOf(updateTableNum)/Double.valueOf(usernameTotal) : 0;
        double insertTableNumProportion = usernameTotal > 0 ?  Double.valueOf(insertTableNum)/Double.valueOf(usernameTotal) : 0;
        double selectTableNumProportion = usernameTotal > 0 ?  Double.valueOf(selectTableNum)/Double.valueOf(usernameTotal) : 0;
        model.put("usernameTotal",usernameTotal);
        model.put("crudTableNum", crudTableNum);
        model.put("dropTableNum", dropTableNum);
        model.put("deleteTableNum", deleteTableNum);
        model.put("updateTableNum", updateTableNum);
        model.put("insertTableNum", insertTableNum);
        model.put("selectTableNum", selectTableNum);
        model.put("crudTableNumProportion", Arith.round(crudTableNumProportion * 100, 2));
        model.put("dropTableNumProportion", Arith.round(dropTableNumProportion * 100, 2));
        model.put("deleteTableNumProportion", Arith.round(deleteTableNumProportion * 100, 2));
        model.put("updateTableNumProportion", Arith.round(updateTableNumProportion * 100, 2));
        model.put("insertTableNumProportion", Arith.round(insertTableNumProportion * 100, 2));
        model.put("selectTableNumProportion", Arith.round(selectTableNumProportion * 100, 2));


        //敏感数据占比图表 从入参中获取
        List<ExportWordChart> chartList = dto.getPrivator().getChartList();
        List<ExportWordChart> sensitiveChartList = chartList.stream().filter(s -> "敏感数据占比图表".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userAssetChartList = chartList.stream().filter(s -> "用户权限-资产数量一览".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userAddChartList = chartList.stream().filter(s -> "用户权限分类统计结果(新增)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userDeleteDataChartList = chartList.stream().filter(s -> "用户权限分类统计结果(删除数据)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userDeleteTableChartList = chartList.stream().filter(s -> "用户权限分类统计结果(删除表)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userUpdateChartList = chartList.stream().filter(s -> "用户权限分类统计结果(修改)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userQueryChartList = chartList.stream().filter(s -> "用户权限分类统计结果(查询)".equals(s.getName())).collect(Collectors.toList());

        String imageBase64Data1 = sensitiveChartList.get(0).getPicture();
        String imageBase64Data2 = userAssetChartList.get(0).getPicture();
        String imageBase64Data3 = userAddChartList.get(0).getPicture();
        String imageBase64Data4 = userDeleteDataChartList.get(0).getPicture();
        String imageBase64Data5 = userDeleteTableChartList.get(0).getPicture();
        String imageBase64Data6 = userUpdateChartList.get(0).getPicture();
        String imageBase64Data7 = userQueryChartList.get(0).getPicture();
        imageBase64Data1 = imageBase64Data1.substring(IMAGE_PNG_BASE64.length());
        imageBase64Data2 = imageBase64Data2.substring(IMAGE_PNG_BASE64.length());
        imageBase64Data3 = imageBase64Data3.substring(IMAGE_PNG_BASE64.length());
        imageBase64Data4 = imageBase64Data4.substring(IMAGE_PNG_BASE64.length());
        imageBase64Data5 = imageBase64Data5.substring(IMAGE_PNG_BASE64.length());
        imageBase64Data6 = imageBase64Data6.substring(IMAGE_PNG_BASE64.length());
        imageBase64Data7 = imageBase64Data7.substring(IMAGE_PNG_BASE64.length());
        //base64解码

        ByteArrayInputStream bis1 = new ByteArrayInputStream(Base64.decode(imageBase64Data1));
        ByteArrayInputStream bis2 = new ByteArrayInputStream(Base64.decode(imageBase64Data2));
        ByteArrayInputStream bis3 = new ByteArrayInputStream(Base64.decode(imageBase64Data3));
        ByteArrayInputStream bis4 = new ByteArrayInputStream(Base64.decode(imageBase64Data4));
        ByteArrayInputStream bis5 = new ByteArrayInputStream(Base64.decode(imageBase64Data5));
        ByteArrayInputStream bis6 = new ByteArrayInputStream(Base64.decode(imageBase64Data6));


        model.put("sensitiveAssetsProportionImg", Pictures.ofStream(bis1, PictureType.PNG)
                .size(550, 200).create());
        model.put("userAuthorityTypeAddImg", Pictures.ofStream(bis2, PictureType.PNG)
                .size(550, 650).create());
        model.put("userAuthorityTypeUpdateImg", Pictures.ofStream(bis3, PictureType.PNG)
                .size(550, 650).create());
        model.put("userAuthorityTypeSelectImg", Pictures.ofStream(bis4, PictureType.PNG)
                .size(550, 650).create());
        model.put("userAuthorityTypeDeleteImg", Pictures.ofStream(bis5, PictureType.PNG)
                .size(550, 650).create());
        model.put("userAuthorityTypeDropImg", Pictures.ofStream(bis6, PictureType.PNG)
                .size(550, 650).create());


        //3.2.4	用户-资产权限分布分析
        List<ReportAuthorityUserAssetVo> reportAuthorityUserAssetVos = this.queryUserAssetsResult(requestModel);
        AtomicInteger i = new AtomicInteger();
        reportAuthorityUserAssetVos.stream().forEach(p -> {
            p.setSort(i.getAndIncrement()+1);
        });
        model.put("userAssetAuthorityList", reportAuthorityUserAssetVos);


        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder().bind("userAssetAuthorityList", policy)
                .bind("tabList1", policy)
                .bind("tabList2", policy)
                .bind("loopholeByIPSystemList", policy)
                .bind("legalList", policy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);


        //输出结果
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        template.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, bos, out);

    }

    /**
     * 导出EXCEL
     *
     * @param response    request
     * @param operationId request
     */
    @Override
    @SneakyThrows
    public void exportExcel(HttpServletResponse response, String operationId) {
        List<AuthorityUserExportExcel> dataList = new ArrayList<>();
        List<AuthorityUserExportExcel> authorityExportExcels = coAuthorityMapper.queryAuthorityByOperationId(operationId);
        Map<AuthorityExportExcel, List<AuthorityUserExportExcel>> userAuthorities = authorityExportExcels.stream().collect(
                Collectors.groupingBy(a -> Func.toBean(a, AuthorityExportExcel.class)));
        if (CollUtil.isNotEmpty(userAuthorities)) {
            for (Map.Entry<AuthorityExportExcel, List<AuthorityUserExportExcel>> entry : userAuthorities.entrySet()) {
                AuthorityExportExcel key = entry.getKey();
                List<AuthorityUserExportExcel> value = entry.getValue();
                Map<String, List<AuthorityUserExportExcel>> userPriList = value.stream().collect(Collectors.groupingBy(AuthorityUserExportExcel::getUsername));
                for (Map.Entry<String, List<AuthorityUserExportExcel>> userPri : userPriList.entrySet()) {
                    AuthorityUserExportExcel authorityUserExportExcel = new AuthorityUserExportExcel();
                    BeanUtils.copyProperties(key, authorityUserExportExcel);
                    authorityUserExportExcel.setUsername(userPri.getKey());
                    authorityUserExportExcel.setDbType(DataSourceType.getType(key.getDbType()).getName());
                    authorityUserExportExcel.setInsertPri(userPri.getValue().stream().anyMatch(a -> "t".equalsIgnoreCase(a.getInsertPri())) ? "是" : "否");
                    authorityUserExportExcel.setDeletePri(userPri.getValue().stream().anyMatch(a -> "t".equalsIgnoreCase(a.getDeletePri())) ? "是" : "否");
                    authorityUserExportExcel.setDropPri(userPri.getValue().stream().anyMatch(a -> "t".equalsIgnoreCase(a.getDropPri())) ? "是" : "否");
                    authorityUserExportExcel.setUpdatePri(userPri.getValue().stream().anyMatch(a -> "t".equalsIgnoreCase(a.getUpdatePri())) ? "是" : "否");
                    authorityUserExportExcel.setSelectPri(userPri.getValue().stream().anyMatch(a -> "t".equalsIgnoreCase(a.getSelectPri())) ? "是" : "否");
                    authorityUserExportExcel.setCrudPri(userPri.getValue().stream().anyMatch(a -> "t".equalsIgnoreCase(a.getCrudPri())) ? "是" : "否");
                    dataList.add(authorityUserExportExcel);
                }
            }
        }
        Func.responseSetting(response,
                String.format("%s-%s.xlsx", "数据权限导出", DateUtils.getExportDateStr(System.currentTimeMillis())));
        EasyExcel.write(response.getOutputStream(), AuthorityUserExportExcel.class)
                .registerWriteHandler(WaterMarkHandler.simple(coLicenseMapper.queryCustomName()))
                .sheet().doWrite(() -> dataList);
    }

    @Override
    public List<CoAssetUser> queryUserAuthority(String assetId) {
        return coAssetUserService.list(new QueryWrapper<CoAssetUser>().eq("asset_id", assetId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAndUpdateUserAuthority(UserAuthoritySaveParam param) {
        // 删除当前表资产下的所有用户权限
        List<UserAuthoritySaveParam.AssetUser> assetUserNew = param.getAssetUserList();
        if (CollUtil.isEmpty(assetUserNew)) {
            coAssetUserService.remove(new QueryWrapper<CoAssetUser>().eq("asset_id", param.getAssetId()));
            return;
        }
        Map<String, UserAuthoritySaveParam.AssetUser> userMap = assetUserNew.stream().collect(
                Collectors.toMap(a -> a.getUsername().trim(), Function.identity(), (k1, k2) -> k1));
        if (userMap.size() != assetUserNew.size())
            throw new ServiceException("存在同名用户");
        final List<CoAssetUser> insertList = new ArrayList<>();
        final List<CoAssetUser> updateList = new ArrayList<>();
        // 查询出数据库中当前资产的用户权限
        Map<String, CoAssetUser> assetUserMap = coAssetUserService.list(new QueryWrapper<CoAssetUser>().eq("asset_id", param.getAssetId()))
                .stream().collect(Collectors.toMap(CoAssetUser::getUsername, Function.identity()));
        userMap.forEach((username, assetUser) -> {
            CoAssetUser bean = Func.toBean(assetUser, CoAssetUser.class);
            if (bean.getCrudPri()) {
                bean.setDropPri(Boolean.TRUE);
                bean.setInsertPri(Boolean.TRUE);
                bean.setDeletePri(Boolean.TRUE);
                bean.setUpdatePri(Boolean.TRUE);
                bean.setSelectPri(Boolean.TRUE);
            } else if (bean.getUpdatePri() && bean.getDeletePri() && bean.getInsertPri() && bean.getDropPri() && bean.getSelectPri()) {
                bean.setCrudPri(Boolean.TRUE);
            }
            if (assetUserMap.containsKey(username)) {
                // 更新
                bean.setId(assetUserMap.get(username).getId());
                updateList.add(bean);
            } else {
                // 新增
                bean.setOperationId(param.getOperationId());
                bean.setAssetId(param.getAssetId());
                insertList.add(bean);
            }
        });
        // 过滤出assetUserMap中存在但 userMap 中不存在的用户 删除
        List<Integer> delIds = CollUtil.subtract(assetUserMap.keySet(), userMap.keySet()).stream().map(a -> assetUserMap.get(a).getId()).collect(Collectors.toList());
        coAssetUserService.removeByIds(delIds);
        coAssetUserService.updateBatchById(updateList);
        coAssetUserService.saveBatch(insertList);
    }

    @Override
    public PageResult<AssetUserViewVO> queryAssetUserView(AssetUserDTO dto) {
        try (Page<Object> page = PageHelper.startPage(dto.getCurrentPage(), dto.getPageSize())) {
            List<AssetUserViewVO> list = baseMapper.queryAssetUserView(dto);
            return PageResult.ofPage(page.getTotal(), list);
        }
    }

    @Override
    public void deleteAssetUserView(AssetUserDelReq req) {
        baseMapper.deleteAssetUserView(req);
    }

    @Override
    public PageResult<UserAuthVO> assetUserViewDetail(AssetUserQueryDTO dto) {
        try (Page<Object> page = PageHelper.startPage(dto.getCurrentPage(), dto.getPageSize())) {
            List<UserAuthVO> list = baseMapper.assetUserViewDetail(dto);
            return PageResult.ofPage(page.getTotal(), list);
        }
    }

    @Override
    public void assetUserViewDetailDelete(UserAssetDeleteReq req) {
        coAssetUserService.remove(new QueryWrapper<CoAssetUser>().in("asset_id", req.getAssetIds()));
    }

    @Override
    public void assetUserViewDetailUpdate(UserAssetUpdateReq req) {
        if (StrUtil.isEmpty(req.getAssetId()))
            throw new ServiceException("资产id不能为空");
        CoAssetUser coAssetUser = coAssetUserService.getBaseMapper().selectOne(new QueryWrapper<CoAssetUser>()
                .eq("operation_id", req.getOperationId()).eq("asset_id", req.getAssetId()).eq("username", req.getUsername()));
        if (Objects.isNull(coAssetUser))
            throw new ServiceException("用户权限资产不存在");
        coAssetUser.setInsertPri(req.getInsertPri());
        coAssetUser.setDeletePri(req.getDeletePri());
        coAssetUser.setDropPri(req.getDropPri());
        coAssetUser.setUpdatePri(req.getUpdatePri());
        coAssetUser.setSelectPri(req.getSelectPri());
        coAssetUser.setCrudPri(req.getCrudPri());
        coAssetUserService.updateById(coAssetUser);
    }

    @Override
    public void assetUserDetailAdd(UserAssetAddReq req) {
        if (StrUtil.isEmpty(req.getDataAsset()))
            throw new ServiceException("资产名称不能为空");
        Set<String> existsAssetIds = coAssetUserService.list(new QueryWrapper<CoAssetUser>()
                .eq("operation_id", req.getOperationId())
                .eq("username", req.getUsername())).stream().map(CoAssetUser::getAssetId).collect(Collectors.toSet());
        List<CoAssetView> dataAssets = coAssetViewService.list(new QueryWrapper<CoAssetView>()
                .eq("operation_id", req.getOperationId()).eq("data_asset", req.getDataAsset()));
        if (CollUtil.isEmpty(dataAssets))
            throw new ServiceException("资产不存在");
        List<CoAssetUser> assetUsers = dataAssets.stream().filter(a -> !existsAssetIds.contains(a.getViewId())).map(asset -> CoAssetUser.builder()
                .operationId(req.getOperationId())
                .assetId(asset.getViewId())
                .username(req.getUsername())
                .insertPri(req.getInsertPri())
                .updatePri(req.getUpdatePri())
                .deletePri(req.getDeletePri())
                .dropPri(req.getDropPri())
                .selectPri(req.getSelectPri())
                .crudPri(req.getCrudPri()).build()).collect(Collectors.toList());
        PartitionUtils.part(assetUsers, coAssetUserService::saveBatch);
    }

    @Override
    public List<BusSystemAuthorityOverviewDTO> queryBusSystemAuthorityOverview(String operationId) {
        return baseMapper.queryBusSystemAuthorityOverview(operationId);
    }

    @Override
    public List<ReportAuthorityUserAssetVo> queryUserAssetsResultWithBusSystem(
        RequestModel<OperationIdDto> dto) {
        CheckUtil.checkParams(dto.getPrivator());
        String operationId = dto.getPrivator().getOperationId();
        return coAuthorityMapper.queryUserAssetsResultWithBusSystem(operationId);
    }

    @SchemaSwitch
    @Override
    public void downloadOfflineTool(HttpServletResponse response) {
        // 获取用户目录（在某些情况下可以作为当前工作目录）
        String userDir = System.getProperty("user.dir");

        File checkDir = FileUtil.file(CharSequenceUtil.join(File.separator, userDir, "check"));
        FileUtil.mkdir(checkDir);
        File tempFile = FileUtil.file("tempFile");

        ClassPathResource jarPathResource = new ClassPathResource("tool/authority/data-authority-check-1.0-SNAPSHOT.jar");
        ClassPathResource readmePathResource = new ClassPathResource("tool/authority/README.md");
        ClassPathResource jrePathResource = new ClassPathResource("tool/jre.zip");
        try {
            // 复制jar文件
            File jarFile = FileUtil.file(CharSequenceUtil.join(File.separator, checkDir.getPath(), jarPathResource.getFilename()));
            FileUtil.touch(jarFile);
            Files.copy(jarPathResource.getInputStream(), jarFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            // 复制redme文件
            File readmeFile = FileUtil.file(CharSequenceUtil.join(File.separator, checkDir.getPath(), readmePathResource.getFilename()));
            FileUtil.touch(readmeFile);
            Files.copy(readmePathResource.getInputStream(), readmeFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            // 复制jre文件
            File jreFile = FileUtil.file(CharSequenceUtil.join(File.separator, checkDir.getPath(), jrePathResource.getFilename()));
            FileUtil.touch(jreFile);
            Files.copy(jrePathResource.getInputStream(), jreFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            // 获取统一数源包
            String path = System.getProperty(DatasourceConstant.MCDATASOURCE_HOME);
            PathUtil.copy(FileUtil.file(path).toPath(), checkDir.toPath(), StandardCopyOption.REPLACE_EXISTING);

            // 获取配置文件内容
            List<AuthoritySqlDTO> authoritySql  = coAuthorityMapper.queryAuthoritySql();
            if (CollUtil.isNotEmpty(authoritySql)){
                authoritySql.forEach(authoritySqlDTO -> {
                    String sqlpath = authoritySqlDTO.getSql();
                    try {
                        String sql = IOUtils.toString(Files.newInputStream(FileUtil.file(sqlpath).toPath()));
                        authoritySqlDTO.setSql(SecurityUtils.encryptAes(sql));
                    } catch (IOException e) {
                        log.error("获取权限sql失败", e);
                        throw new RuntimeException(e);
                    }
                });
            }
            File file = FileUtil.file(CharSequenceUtil.join(File.separator, userDir, "check", "conf.json"));
            FileUtil.writeString(JSONUtil.toJsonStr(authoritySql), file, StandardCharsets.UTF_8);

            File zipPath = ZipUtil.zip(tempFile, true, checkDir);
            Func.write(response, zipPath, "check.zip");

        } catch (IOException e) {
            log.error("下载离线工具失败", e);
            throw new ServiceException("下载离线工具失败!");
        } finally {
            org.apache.commons.io.FileUtils.deleteQuietly(tempFile);
            org.apache.commons.io.FileUtils.deleteQuietly(checkDir);

        }
    }
}
