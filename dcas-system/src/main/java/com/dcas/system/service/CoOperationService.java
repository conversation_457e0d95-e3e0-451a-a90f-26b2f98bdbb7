package com.dcas.system.service;

import com.dcas.common.core.domain.TreeSelect;
import com.dcas.common.model.param.ModelParam;
import com.dcas.common.model.vo.*;
import com.github.pagehelper.PageInfo;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.model.dto.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 作业管理服务层
 *
 * <AUTHOR>
 * @Date 2022/5/24 16:32
 * @ClassName OperationManagerService
 */
public interface CoOperationService {
    /**
     * 创建作业，复制作业需要指定作业的知识库版本号
     */
    String add(RequestModel<OperationAddDTO> dto, String version);

    /**
     * fetch
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:39
     */
    int remove(RequestModel<PrimaryKeyListDTO> dto);

    /**
     * 修改作业管理记录
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/7/21 17:00
     */
    int edit(UpdateOperationDto dto);

    /**
     * 查询作业主表
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     */
    PageInfo<QueryOperationVo> query(RequestModel<QueryOperationDTO> dto);

    /**
     * 更新进度
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/8/11 14:21
     */
    void editProgress(RequestModel<CommonDto> dto);

    void editVerProcess(String operationId);

    /**
     * 查询进度
     *
     * @param dto request
     * @return * @return String
     * @Date 2022/8/11 15:16
     */
    QueryProgressVo queryProgress(RequestModel<PrimaryKeyDTO> dto);

    /**
     * 查询涉及行业
     *
     * @param industryName request
     * @return * @return List<String>
     * @Date 2022/8/26 16:10
     */
    List<String> queryIndustry(String industryName);

    /**
     * 获取评估作业下拉树列表
     * @Date 2022/10/18 11:32
     * @param dto  request
     * @return * @return ResponseApi<List<TreeSelect>>
     */
    List<TreeSelect> treeSelect(RequestModel<OperationIdDto> dto);

    List<LabelVO> qryTreeLabel();

    LabelVO qryTemplate(TemplateQueryDTO dto);

    Boolean operationType(String operationId);

    /**
     * 导出word
     *
     * @param response request
     * @param requestModel request
     * @throws IOException
     * @Date 2022/7/27 15:45
     */
    void exportWord(HttpServletResponse response, RequestModel<ExportOperationWordDto> requestModel) throws IOException;

    OperationContentVO detail(String operationId);

    /**
     * 复制作业接口
     * @param dto RequestModel<OperationCopyDTO>
     * @return {@link OperationCopyVO}
     */
    OperationCopyVO copyJob(RequestModel<OperationCopyDTO> dto);

    /**
     * 应用风险评估模板
     * @param modelParam
     */
    Boolean useTemplate(ModelParam modelParam);

    /**
     * 获取可用风险评估模板列表
     * @return
     * @param operationId 作业ID
     */
    List<SelectedKeyValueVO<Long, String>> listTemplate(String operationId);

    /**
     * 获取风险评估结果
     * @param operationId 作业ID
     * @param pageNum
     * @param pageSize
     * @param searchType
     * @param searchContent
     * @return {@link RiskAnalysisResultVO}
     */
    RiskAnalysisResultVO getRiskAnalysisResult(String operationId, Integer pageNum, Integer pageSize, String searchType,
        String searchContent);

    void update(OperationUpdateDTO dto);

    void updateQuestionnaire(CommonDto dto);

    /**
     * 获取专项评估列表
     * @return
     */
    List<SpecVO> getSpecList(TemplateQueryDTO dto);
}
