package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.hash.MurmurHash;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.enums.TemplateTypeEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.Func;
import com.dcas.common.utils.PageResult;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.file.FileUtils;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.excel.LawItemExcel;
import com.dcas.common.model.excel.LawRelevanceExcel;
import com.dcas.common.model.req.*;
import com.dcas.common.model.vo.ArticleItemVO;
import com.dcas.common.mapper.ArticleItemRelevanceMapper;
import com.dcas.common.mapper.LawItemMapper;
import com.dcas.common.mapper.LawMapper;
import com.dcas.common.mapper.LawTemplateRelevanceMapper;
import com.dcas.system.service.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/3 16:53
 * @since 1.2.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LawServiceImpl implements LawService {

    private final TagService tagService;
    private final ItemService itemService;
    private final QuestionService questionService;
    private final ArticleItemRelevanceService articleItemRelevanceService;

    private final LawMapper lawMapper;
    private final LawItemMapper lawItemMapper;
    private final ArticleItemRelevanceMapper articleItemRelevanceMapper;
    private final LawTemplateRelevanceMapper lawTemplateRelevanceMapper;

    @Override
    public List<Law> listAll() {
        return lawMapper.selectList(new QueryWrapper<>());
    }

    @Override
    public PageResult<Law> pageQuery(Integer currentPage, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(currentPage, pageSize);
        List<Law> laws = lawMapper.selectList(new QueryWrapper<>());
        return PageResult.ofPage(page.getTotal(), laws);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void addLaw(LawFileReq req) {
        String name = req.getName().trim();
        QueryWrapper<Law> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        Integer count = lawMapper.selectCount(queryWrapper);
        if (count != 0)
            throw new ServiceException("法律文件已存在，请勿重复添加");
        Law law = BeanUtil.copyProperties(req, Law.class);
        law.setName(name);
        law.setEnable(Boolean.TRUE);
        law.setCreateTime(new Date());
        law.setCreateBy(SecurityUtils.getAccount());
        lawMapper.insert(law);
        if (Objects.nonNull(req.getContentType()) && req.getContentType() == 2) {
            if (StrUtil.isEmpty(req.getUrl()))
                throw new ServiceException("文件上传路径不能为空");
            MultipartFile file = FileUtils.createMultipartFile(req.getUrl());
            produceItemsByFile(law.getId(), file);
        }
    }

    private void produceItemsByFile(Integer docId, MultipartFile file) throws IOException {
        List<ArticleItemRelevance> result = Lists.newArrayList();
        List<LawRelevanceExcel> lawItemRelevanceExcels = Func.fileAttestation(file, LawRelevanceExcel.class, null, true);
        Map<Long, Item> itemMap = itemService.listAll().stream().collect(
                Collectors.toMap(i -> MurmurHash.hash64(i.getTitle()), i -> i));
        Iterator<LawRelevanceExcel> iterator = lawItemRelevanceExcels.iterator();
        final BeanCopier copier = BeanCopier.create(LawItemExcel.class, LawItem.class, false);
        while (iterator.hasNext()) {
            LawRelevanceExcel excel = iterator.next();
            if (StrUtil.isEmpty(excel.getItem())) {
                continue;
            }
            long itemKey = MurmurHash.hash64(excel.getItem());
            if (StrUtil.isEmpty(excel.getContent()) || !itemMap.containsKey(itemKey)) {
                iterator.remove();
                continue;
            }
            // 提前设置计算好的哈希值
            excel.setItem(String.valueOf(itemKey));
        }
        Set<String> tags = lawItemRelevanceExcels.stream().flatMap(e -> {
            List<String> split = StrUtil.split(e.getTags(), StrUtil.COMMA);
            split.addAll(StrUtil.split(e.getMatchTags(), StrUtil.COMMA));
            return split.stream();
        }).collect(Collectors.toSet());
        tagService.saveBatch(tags);
        final Map<String, Long> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getName, Tag::getId));
        // 条文 -> 核查项哈希表
        Map<LawItemExcel, List<LawRelevanceExcel>> itemRelevance = lawItemRelevanceExcels.stream().collect(
                Collectors.groupingBy(item -> BeanUtil.copyProperties(item, LawItemExcel.class)));
        itemRelevance.forEach((item, relevance) -> {
            LawItem lawItem = new LawItem();
            copier.copy(item, lawItem, null);
            lawItem.setDocId(docId);
            lawItem.setTags(questionService.transformTagId(lawItem.getTags(), tagsMap));
            lawItemMapper.insert(lawItem);
            List<ArticleItemRelevance> articleItemRelevance = relevance.stream().filter(e -> StrUtil.isNotEmpty(e.getItem())).map(r -> {
                ArticleItemRelevance articleItem = new ArticleItemRelevance();
                articleItem.setArticleId(lawItem.getId());
                articleItem.setItemId(itemMap.get(Long.valueOf(r.getItem())).getId());
                articleItem.setMatchTags(questionService.transformTagId(r.getMatchTags(), tagsMap));
                return articleItem;
            }).collect(Collectors.toList());
            result.addAll(articleItemRelevance);
        });
        articleItemRelevanceService.saveList(result);
    }

    @Override
    public void updateLaw(LawFileReq req) {
        if (Objects.isNull(req.getId()))
            throw new ServiceException("更新主键不能为空");
        Law law = lawMapper.selectById(req.getId());
        if (Objects.isNull(law))
            throw new ServiceException("更新对象不存在，id:{}", req.getId());
        Law copy = BeanUtil.copyProperties(req, Law.class);
        copy.setId(law.getId());
        copy.setUpdateBy(SecurityUtils.getAccount());
        copy.setUpdateTime(new Date());
        lawMapper.updateById(copy);
    }

    @Override
    public void deleteLaw(IdsReq req) {
        List<LawTemplateRelevance> lawTemplateRelevance = lawTemplateRelevanceMapper.selectList(new QueryWrapper<>());
        Set<Integer> docIds = lawTemplateRelevance.stream().map(LawTemplateRelevance::getDocId).collect(Collectors.toSet());
        if (!CollUtil.intersectionDistinct(docIds, req.getIds()).isEmpty())
            throw new ServiceException("待删除列表中存在与模板关联的合规文件，请先取消关联");
        lawMapper.deleteBatchIds(req.getIds());
        QueryWrapper<LawItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("doc_id", req.getIds());
        List<LawItem> lawItems = lawItemMapper.selectList(queryWrapper);
        Set<String> itemIds = lawItems.stream().map(LawItem::getId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(itemIds)) {
            lawItemMapper.deleteBatchIds(itemIds);
            QueryWrapper<ArticleItemRelevance> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.in("article_id", itemIds);
            articleItemRelevanceMapper.delete(deleteWrapper);
        }
    }

    @Override
    public void enable(Integer id) {
        Law law = lawMapper.selectById(id);
        if (Objects.isNull(law))
            throw new ServiceException("合规文件不存在");
        Law build = Law.builder().id(law.getId()).enable(!law.getEnable()).build();
        lawMapper.updateById(build);
    }

    @Override
    public PageResult<LawItem> pageQueryLawItem(Integer id, Integer currentPage, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(currentPage, pageSize);
        QueryWrapper<LawItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("doc_id", id);
        List<LawItem> lawItems = lawItemMapper.selectList(queryWrapper);
        final Map<Long, String> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        lawItems.forEach(lawItem -> {
            lawItem.setTags(questionService.transformTagName(lawItem.getTags(), tagsMap));
        });
        return PageResult.ofPage(page.getTotal(), lawItems);
    }

    @Override
    public void addLawItem(LawItemReq req) {
        LawItem lawItem = BeanUtil.copyProperties(req, LawItem.class);
        lawItem.setTags(questionService.transformTagId(lawItem.getTags()));
        lawItemMapper.insert(lawItem);
    }

    @Override
    public void updateLawItem(LawItemUpdateReq req) {
        LawItem lawItem = BeanUtil.copyProperties(req, LawItem.class);
        lawItem.setTags(questionService.transformTagId(lawItem.getTags()));
        lawItemMapper.updateById(lawItem);
    }

    @Override
    public void deleteLawItems(ExcludeItemReq req) {
        lawItemMapper.deleteBatchIds(req.getItemIds());
        QueryWrapper<ArticleItemRelevance> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.in("article_id", req.getItemIds());
        articleItemRelevanceMapper.delete(deleteWrapper);
    }

    @Override
    public List<ArticleItemVO> qryArticleItems(String articleId) {
        final Map<Long, String> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        List<ArticleItemVO> articleItems = articleItemRelevanceMapper.selectByArticleId(articleId);
        articleItems.forEach(item -> {
            item.setMatchTags(questionService.transformTagName(item.getMatchTags(), tagsMap));
        });
        return articleItems;
    }

    @Override
    public void addArticleItem(RelevanceInsertReq req) {
        QueryWrapper<ArticleItemRelevance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("article_id", req.getId());
        Set<String> existItemIds = articleItemRelevanceMapper.selectList(queryWrapper)
                .stream().map(ArticleItemRelevance::getItemId).collect(Collectors.toSet());
        List<ArticleItemRelevance> relevance = req.getItemIds().stream().filter(i -> !existItemIds.contains(i)).map(item ->
                ArticleItemRelevance.builder()
                        .articleId(req.getId())
                        .itemId(item)
                        .matchTags("0")
                        .build()).collect(Collectors.toList());
        articleItemRelevanceService.saveList(relevance);
    }

    @Override
    public void deleteArticleItem(Integer id) {
        articleItemRelevanceMapper.deleteById(id);
    }

    @Override
    public List<Law> search(LawFileReq req) {
        return lawMapper.search(req);
    }

    @Override
    public List<LawItem> searchItem(LawItemReq req) {
        List<LawItem> lawItems = new ArrayList<>();
        final Map<Long, String> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        if (StrUtil.isNotEmpty(req.getTags())) {
            final Map<String, Long> map = tagsMap.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
            if (Objects.isNull(map.get(req.getTags())))
                return lawItems;
            QueryWrapper<LawItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("tags", map.get(req.getTags()));
            lawItems = lawItemMapper.selectList(queryWrapper);
        } else {
            lawItems = lawItemMapper.search(req);
        }
        lawItems.forEach(item -> {
            item.setTags(questionService.transformTagName(item.getTags(), tagsMap));
        });
        return lawItems;
    }
}
