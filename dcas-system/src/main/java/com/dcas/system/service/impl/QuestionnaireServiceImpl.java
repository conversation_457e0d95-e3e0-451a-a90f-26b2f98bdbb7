package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.hash.MurmurHash;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.constant.CacheConstants;
import com.dcas.common.core.redis.RedisCache;
import com.dcas.common.enums.AbilityType;
import com.dcas.common.enums.AssessModuleType;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.req.AbilityItemReq;
import com.dcas.common.utils.Func;
import com.dcas.common.utils.PartitionUtils;
import com.dcas.common.utils.WordReader;
import com.dcas.common.utils.file.FileUtils;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.param.FileRelationParam;
import com.dcas.common.model.req.QuestionnaireContentReq;
import com.dcas.common.model.vo.*;
import com.dcas.system.holder.RegionCodeCacheHolder;
import com.dcas.common.mapper.*;
import com.dcas.system.service.QuestionnaireContentService;
import com.dcas.system.service.QuestionnaireService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.util.PoitlIOUtils;
import com.google.common.collect.Lists;
import com.mchz.dcas.client.enums.TagType;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/14 15:11
 * @since 1.2.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QuestionnaireServiceImpl extends ServiceImpl<QuestionnaireMapper, Questionnaire> implements QuestionnaireService {

    private final static String ALL_INDUSTRY = "全行业";
    private final static String ALL_REGION = "100000";
    private final QuestionnaireContentService questionnaireContentService;

    private final RedisCache redisCache;

    private final LawMapper lawMapper;
    private final ItemMapper itemMapper;
    private final StandardMapper standardMapper;
    private final TemplateMapper templateMapper;
    private final QuestionMapper questionMapper;
    private final CoProgressMapper coProgressMapper;
    private final CoOperationMapper coOperationMapper;
    private final QuestionnaireMapper questionnaireMapper;
    private final TemplateContentMapper templateContentMapper;
    private final CoSurveyObjectTypeMapper coSurveyObjectTypeMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final QuestionnaireManageMapper questionnaireManageMapper;
    private final QuestionnaireContentMapper questionnaireContentMapper;

    private final QuestionnaireImportServiceImpl questionnaireImportService;
    private final QuestionnaireItemDegreeServiceImpl questionnaireItemDegreeService;
    private final QuestionnaireContentImportServiceImpl questionnaireContentImportService;
    private final TagMapper tagMapper;
    private final TemplateCombinationMapper templateCombinationMapper;
    private final MkAppJobMapper mkAppJobMapper;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final DetectionResultMapper detectionResultMapper;

    @Override
    @SchemaSwitch(value = CommonDto.class)
    public QuestionnaireContentVO selectQuestionnaire(CommonDto dto) {
        QuestionnaireContentVO res = new QuestionnaireContentVO();
        Long labelId = dto.getLabelId();
        String operationId = dto.getOperationId();
        CoOperation coOperation = coOperationMapper.selectById(operationId);

        TemplateCombination templateCombination = templateCombinationMapper.selectById(coOperation.getTemplateId());
        // 是否为组合模板，兼容旧作业
        List<Long> templateIds = Objects.isNull(templateCombination) ? Collections.singletonList(coOperation.getTemplateId()) : Arrays.asList(templateCombination.getTemplateIds());
        List<Template> templates = templateMapper.selectBatchIds(templateIds);
        final Set<Template> finalTemplates = new HashSet<>(templates);
        // 基础调研模板->行业模板->地域模板->专属模板
        List<Long> sortTemplateIds = sortTemplate(templates);
        List<Questionnaire> questionnaires = questionnaireMapper.selectQuestionnaireSort(operationId, labelId, dto.getPreview());

        final Map<String, Set<FileLocateDTO>> itemFileMap = itemFileRelation(new FileRelationParam(dto.getOperationId(),
                coOperation.getRelatedDistrict(), coOperation.getRelatedIndustry(), Boolean.TRUE));

        // 条件标签
        final Map<Long, String> abilityTagMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("type_id", TagType.GRADE.getCode())).stream().collect(Collectors.toMap(Tag::getId, Tag::getName));

        // 核查项能力标签
        final Map<String, String> abilityItemMap = itemMapper.selectList(new QueryWrapper<Item>().eq("status", 0))
                .stream().filter(i -> StrUtil.isNotEmpty(i.getAbility())).collect(Collectors.toMap(Item::getId, Item::getAbility));

        Tag tag = tagMapper.selectOne(new QueryWrapper<Tag>().eq("name", "个人信息").eq("type_id", 1));
        // 问卷中没有说明是第一次调研，查询模板
        if (CollUtil.isEmpty(questionnaires)) {

            Tag carTag = tagMapper.selectOne(new QueryWrapper<Tag>().eq("name", "汽车").eq("type_id", 2));
            boolean carIndustry = Objects.nonNull(carTag) &&
                    finalTemplates.stream().anyMatch(template -> StrUtil.split(template.getIndustryCode(), StrUtil.COMMA).contains(carTag.getId().toString()));

            // 映射具体系统调研id到真实id
            Long srcTreeId = dynamicProcessTreeMapper.selectSrcTreeId(operationId, labelId);
            List<TemplateContent> contentList = templateContentMapper.selectTemplateContent(sortTemplateIds, srcTreeId.toString());
            List<ContentVO> details = buildDetails(contentList, itemFileMap, sortTemplateIds, coOperation.getInapplicable(), tag.getId(), carIndustry, abilityTagMap, abilityItemMap);
            res.setTotal(contentList.stream().map(TemplateContent::getQuestionId).collect(Collectors.toSet()).size());
            res.setCompleted(0);
            res.setProgress(0.0);
            res.setDetails(details);
        } else {
            int completed = (int)questionnaires.stream().filter(Questionnaire::getFinished).count();
            List<QuestionnairePersonContentVO> questionnaireContents = questionnaireContentMapper.selectQuestionnaireContent(operationId, labelId, dto.getPreview());
            res.setTotal(questionnaires.size());
            res.setCompleted(completed);
            res.setProgress(NumberUtil.div(completed, questionnaires.size(), 2, RoundingMode.HALF_UP));
            Long srcTreeId = dynamicProcessTreeMapper.selectSrcTreeId(operationId, labelId);
            // 查不到为旧作业
            if (Objects.isNull(srcTreeId))
                srcTreeId = labelId;
            QueryWrapper<CoSurveyObjectType> query = new QueryWrapper<>();
            query.eq("label_id", srcTreeId);
            CoSurveyObjectType coSurveyObjectType = coSurveyObjectTypeMapper.selectOne(query);
            res.setComment(coSurveyObjectType.getSurveyObjectComment());
            // 根据itemId映射，相同itemId取更大的degree
            QueryWrapper<QuestionnaireItemDegree> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("operation_id", operationId);
            queryWrapper.eq("object_id", labelId);
            if (Objects.nonNull(dto.getPreview()))
                queryWrapper.eq("type", 2);
            else
                queryWrapper.eq("type", 1);
            Map<String, Integer> degreeMap = questionnaireItemDegreeService.list(queryWrapper)
                    .stream().collect(Collectors.toMap(QuestionnaireItemDegree::getItemId, QuestionnaireItemDegree::getDegree, Math::max));
            List<ContentVO> details = buildDetails(questionnaires, questionnaireContents, itemFileMap, tag.getId(), degreeMap, abilityTagMap, abilityItemMap);
            res.setDetails(details);
        }
        return res;
    }

    private List<Long> sortTemplate(List<Template> templates) {
        List<Long> list = new ArrayList<>();
        Iterator<Template> iterator = templates.iterator();

        int i = templates.size();
        while (iterator.hasNext() || i > 0) {
            Template template = iterator.next();
            // 基础调研模板
            if (Boolean.TRUE.equals(template.getUnderstand())) {
                list.add(template.getId());
                iterator.remove();
            }
            i--;
        }
        // 行业模板
        Iterator<Template> industryIterator = templates.iterator();
        i = templates.size();
        while (industryIterator.hasNext()  || i > 0) {
            Template template = industryIterator.next();
            if (CharSequenceUtil.equals(template.getRegionCode(), ALL_REGION) &&
                !(CharSequenceUtil.equals(template.getIndustryCode(), ALL_INDUSTRY) || CharSequenceUtil.equals(template.getIndustryCode(), "594"))){
                list.add(template.getId());
                industryIterator.remove();
            }
            i--;
        }
        // 地域模板
        Iterator<Template> regionIterator = templates.iterator();
        i = templates.size();
        while (regionIterator.hasNext() || i > 0) {
            Template template = regionIterator.next();
            if (!CharSequenceUtil.equals(template.getRegionCode(), ALL_REGION) &&
                (CharSequenceUtil.equals(template.getIndustryCode(), ALL_INDUSTRY) || CharSequenceUtil.equals(template.getIndustryCode(), "594"))){
                list.add(template.getId());
                regionIterator.remove();
            }
            i--;
        }
        // 专属模板
        Iterator<Template> specIterator = templates.iterator();
        i = templates.size();
        while (specIterator.hasNext() || i > 0) {
            Template template = specIterator.next();
            list.add(template.getId());
            specIterator.remove();
            i--;
        }
        return list;
    }

    /**
     * 是否初次对该对象进行调研
     */
    private List<Questionnaire> getQuestionnaire (Long labelId, String operationId) {
        // 查询问卷调研表
        QueryWrapper<Questionnaire> query1 = new QueryWrapper<>();
        query1.eq("operation_id", operationId);
        query1.eq("object_id", labelId);
        return questionnaireMapper.selectList(query1);
    }

    /**
     * 核查项引用文件
     * 根据作业区域和行业进行过滤引用的文件
     *
     * @return key 核查项id value 文件名
     */
    @Override
    @SchemaSwitch(value = FileRelationParam.class)
    public Map<String, Set<FileLocateDTO>> itemFileRelation(FileRelationParam param) {
        String key = CacheConstants.ITEM_FILE_KEY + param.getOperationId();
        final Map<String, String> industryMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("type_id", 2)
            .eq("status", 0)).stream().collect(Collectors.toMap(t -> t.getId().toString(), Tag::getName));
        if (redisCache.hasKey(key)) {
            Map<String, Set<FileLocateDTO>> cacheMap = redisCache.getCacheMap(key);
            return param.getNeedFilter() ? filterByRegionAndIndustry(cacheMap, param.getRegionCode(), param.getIndustry(), industryMap) : cacheMap;
        }
        // 合法合规文件关联核查项映射 key 核查项id value 文件名
        Map<String, Set<FileLocateDTO>> lawMap = lawMapper.queryItemFile().stream().collect(
                Collectors.groupingBy(ItemFileDTO::getItemId, Collectors.mapping(i -> BeanUtil.copyProperties(i, FileLocateDTO.class), Collectors.toSet())));
        Map<String, Set<FileLocateDTO>> standardMap = standardMapper.queryItemFile().stream().collect(
                Collectors.groupingBy(ItemFileDTO::getItemId, Collectors.mapping(i -> BeanUtil.copyProperties(i, FileLocateDTO.class), Collectors.toSet())));
        Map<String, Set<FileLocateDTO>> stringSetMap = Func.mergeMaps(lawMap, standardMap);
        redisCache.setCacheMap(key, stringSetMap);
        return param.getNeedFilter() ? filterByRegionAndIndustry(stringSetMap, param.getRegionCode(), param.getIndustry(), industryMap) : stringSetMap;
    }

    private Map<String, Set<FileLocateDTO>> filterByRegionAndIndustry(Map<String, Set<FileLocateDTO>> itemFileMap,
        String regionCode, String industry, Map<String, String> industryMap) {
        Set<String> regionSet =
            StrUtil.split(regionCode, ";").stream().map(RegionCodeCacheHolder.REGION_PARENT_MAP::get)
                .collect(Collectors.toSet());
        Set<String> industrySet =
                new HashSet<>(StrUtil.split(industry, StrUtil.COMMA));
        for (Map.Entry<String, Set<FileLocateDTO>> entry : itemFileMap.entrySet()) {
            Set<FileLocateDTO> fileLocates = entry.getValue();
            Iterator<FileLocateDTO> iterator = fileLocates.iterator();
            while (iterator.hasNext()) {
                FileLocateDTO next = iterator.next();
                List<String> industryList = StrUtil.split(next.getIndustry(), StrUtil.COMMA).stream().map(industryMap::get).collect(Collectors.toList());
                List<String> regionCodeList = StrUtil.split(next.getRegionCode(), StrUtil.COMMA);

                // 全行业全地域过滤 老版本兼容
                if (ALL_INDUSTRY.equals(next.getIndustry()) && ALL_REGION.equals(next.getRegionCode()))
                    continue;
                // 全地域全行业 新版本
                if (industryList.contains(ALL_INDUSTRY) && regionCodeList.contains(ALL_REGION))
                    continue;
                // 全行业 && 任意地域匹配
                if (industryList.contains(ALL_INDUSTRY) && regionSet.stream().anyMatch(regionCodeList::contains)){
                    continue;
                }
                // 任意行业匹配 && 全地域
                if (industrySet.stream().anyMatch(industryList::contains) && regionCodeList.contains(ALL_REGION)) {
                    continue;
                }
                // 任意行业、地域匹配
                if (industrySet.stream().anyMatch(industryList::contains) && regionSet.stream().anyMatch(regionCodeList::contains)){
                    continue;
                }
                if (regionSet.stream().noneMatch(regionCodeList::contains)) {
                    iterator.remove();
                    continue;
                }
                if (industrySet.stream().noneMatch(industryList::contains)) {
                    iterator.remove();
                }
            }
        }
        return itemFileMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAndUpdate(QuestionnaireContentReq req) {
        // 使用Redis简单锁防止并发重复保存
        String lockKey = String.format("questionnaire:save:%s:%s", req.getOperationId(), req.getLabelId());
        String lockValue = String.valueOf(System.currentTimeMillis());

        try {
            // 尝试获取锁，30秒过期
            boolean lockAcquired = tryAcquireLock(lockKey, lockValue, 30);
            if (!lockAcquired) {
                throw new ServiceException("系统繁忙，请稍后重试");
            }

            saveAndUpdateInternal(req);
        } finally {
            // 释放锁
            releaseLock(lockKey, lockValue);
        }
    }

    /**
     * 尝试获取Redis锁
     */
    private boolean tryAcquireLock(String lockKey, String lockValue, int expireSeconds) {
        try {
            String existingValue = redisCache.getCacheObject(lockKey);
            if (existingValue == null) {
                redisCache.setCacheObject(lockKey, lockValue, expireSeconds, TimeUnit.SECONDS);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.warn("Failed to acquire lock: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 释放Redis锁
     */
    private void releaseLock(String lockKey, String lockValue) {
        try {
            String existingValue = redisCache.getCacheObject(lockKey);
            if (lockValue.equals(existingValue)) {
                redisCache.deleteObject(lockKey);
            }
        } catch (Exception e) {
            log.warn("Failed to release lock: {}", lockKey, e);
        }
    }

    /**
     * 内部保存更新方法，已被分布式锁保护
     */
    private void saveAndUpdateInternal(QuestionnaireContentReq req) {
        CoOperation coOperation = coOperationMapper.selectById(req.getOperationId());
        List<ContentVO> questions = req.getDetails();
        BeanCopier questionnaireCopier = BeanCopier.create(ContentVO.class, Questionnaire.class, false);
        BeanCopier contentCopier = BeanCopier.create(ContentVO.ItemVO.class, QuestionnaireContent.class, false);
        List<Questionnaire> questionnaires = questions.stream().map(q -> {
            Questionnaire o1 = new Questionnaire();
            q.setMatchTags(q.getMatchTags());
            questionnaireCopier.copy(q, o1, null);
            o1.setOperationId(req.getOperationId());
            o1.setObjectId(req.getLabelId());
            return o1;
        }).collect(Collectors.toList());

        List<QuestionnaireItemDegree> degreeList = new ArrayList<>();
        List<QuestionnaireContent> questionnaireContents = new ArrayList<>();
        for (ContentVO vo : questions) {
            List<ContentVO.ItemVO> items = vo.getItems();
            for (ContentVO.ItemVO item : items) {
                QuestionnaireContent questionnaireContent = copyQuestionnaireContent(req, vo, item, contentCopier, coOperation, degreeList);
                questionnaireContents.add(questionnaireContent);
                if (CollUtil.isNotEmpty(item.getChildren())) {
                    for (ContentVO.ItemVO child : item.getChildren()) {
                        QuestionnaireContent childQuestionnaireContent = copyQuestionnaireContent(req, vo, child, contentCopier, coOperation, degreeList);
                        questionnaireContents.add(childQuestionnaireContent);
                    }
                }
            }
        }

        // 在锁保护下重新检查是否存在记录
        List<Questionnaire> existingQuestionnaire = getQuestionnaire(req.getLabelId(), req.getOperationId());
        if (CollUtil.isEmpty(existingQuestionnaire)) {
            this.saveBatch(questionnaires);
            questionnaireContentService.saveList(questionnaireContents);
            log.info("Successfully saved new questionnaire for operation: {}, object: {}", req.getOperationId(), req.getLabelId());
        } else {
            this.updateBatchById(questionnaires);
            questionnaireContentService.updateList(questionnaireContents);
            log.info("Successfully updated existing questionnaire for operation: {}, object: {}", req.getOperationId(), req.getLabelId());
        }

        if (CollUtil.isNotEmpty(degreeList)) {
            Collection<QuestionnaireItemDegree> values = degreeList.stream().collect(Collectors.toMap(
                    QuestionnaireItemDegree::getItemId, Function.identity(), (v1, v2) -> v1.getDegree() >= v2.getDegree() ? v1 : v2)).values();
            questionnaireItemDegreeService.remove(new QueryWrapper<QuestionnaireItemDegree>()
                    .eq("operation_id", req.getOperationId()).eq("object_id", req.getLabelId()).eq("type", 1));
            questionnaireItemDegreeService.saveBatch(values);
        }
    }

    private QuestionnaireContent copyQuestionnaireContent(QuestionnaireContentReq req, ContentVO q, ContentVO.ItemVO i, BeanCopier contentCopier, CoOperation coOperation, List<QuestionnaireItemDegree> degreeList) {
        QuestionnaireContent o2 = new QuestionnaireContent();
        contentCopier.copy(i, o2, null);
        o2.setOperationId(req.getOperationId());
        o2.setObjectId(req.getLabelId());
        // 如果问题为不适用，将不适用的原因传递给核查项
        if (Objects.nonNull(q.getInapplicable()) && q.getInapplicable()) {
            o2.setInapplicable(true);
            o2.setInapplicableReason(q.getInapplicableReason());
        }
        if (!StrUtil.equals("00000000000000000000000000000000", i.getItemId()) && Objects.nonNull(i.getDegree())) {
            QuestionnaireItemDegree itemDegree = new QuestionnaireItemDegree();
            itemDegree.setObjectId(req.getLabelId());
            itemDegree.setItemId(i.getItemId());
            itemDegree.setTemplateId(coOperation.getTemplateId());
            itemDegree.setOperationId(req.getOperationId());
            itemDegree.setDegree(i.getDegree());
            itemDegree.setType(1);
            degreeList.add(itemDegree);
        }
        return o2;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importForTemp(QuestionnaireContentReq req) {
        List<ContentVO> questions = req.getDetails();
        CoOperation coOperation = coOperationMapper.selectById(req.getOperationId());
        List<QuestionnaireItemDegree> degreeList = new ArrayList<>();
        BeanCopier questionnaireCopier = BeanCopier.create(ContentVO.class, QuestionnaireImport.class, false);
        BeanCopier contentCopier = BeanCopier.create(ContentVO.ItemVO.class, QuestionnaireContentImport.class, false);
        List<QuestionnaireImport> questionnaires = questions.stream().map(q -> {
            QuestionnaireImport o1 = new QuestionnaireImport();
            q.setMatchTags(q.getMatchTags());
            questionnaireCopier.copy(q, o1, null);
            o1.setOperationId(req.getOperationId());
            o1.setObjectId(req.getLabelId());
            return o1;
        }).collect(Collectors.toList());
        // 问题下的选项填充不适用属性
        List<QuestionnaireContentImport> questionnaireContents = questions.stream().flatMap(q -> q.getItems().stream().map(i -> {
            QuestionnaireContentImport o2 = new QuestionnaireContentImport();
            contentCopier.copy(i, o2, null);
            o2.setOperationId(req.getOperationId());
            o2.setObjectId(req.getLabelId());
            // 如果问题为不适用，将不适用的原因传递给核查项
            if (Objects.nonNull(q.getInapplicable()) && q.getInapplicable()) {
                o2.setInapplicable(true);
                o2.setInapplicableReason(q.getInapplicableReason());
            }
            if (!StrUtil.equals("00000000000000000000000000000000", i.getItemTitle()) && Objects.nonNull(i.getDegree())) {
                QuestionnaireItemDegree itemDegree = new QuestionnaireItemDegree();
                itemDegree.setItemId(i.getItemId());
                itemDegree.setObjectId(req.getLabelId());
                itemDegree.setTemplateId(coOperation.getTemplateId());
                itemDegree.setOperationId(req.getOperationId());
                itemDegree.setDegree(i.getDegree());
                itemDegree.setType(2);
                degreeList.add(itemDegree);
            }
            return o2;
        })).collect(Collectors.toList());
        PartitionUtils.part(questionnaires, questionnaireImportService::saveBatch);
        PartitionUtils.part(questionnaireContents, questionnaireContentImportService::saveBatch);
        PartitionUtils.part(degreeList, questionnaireItemDegreeService::saveBatch);
    }

    @Override
    @SneakyThrows
    @SchemaSwitch(String.class)
    public void offlineQuestionnaireExport(String operationId, HttpServletResponse response) {
        List<Long> templateIds = new ArrayList<>();
        CoOperation coOperation = coOperationMapper.selectById(operationId);
        if (coOperation.getTemplateId() >= CoOperation.COMBINATION_TEMPLATE_ID) {
            TemplateCombination templateCombination = templateCombinationMapper.selectById(coOperation.getTemplateId());
            templateIds.addAll(new ArrayList<>(Arrays.asList(templateCombination.getTemplateIds())));
        } else {
            templateIds.add(coOperation.getTemplateId());
        }
        List<QuestionnaireExportDTO> questionnaireExportList = templateContentMapper.selectExportTemplate(templateIds);
        if (CollUtil.isEmpty(questionnaireExportList))
            throw new ServiceException("调研模板内容为空");
        Map<Long, Map<String, List<QuestionnaireExportDTO>>> questionnaireGroup = questionnaireExportList.stream()
                .collect(Collectors.groupingBy(QuestionnaireExportDTO::getObjectId,
                        // 企业版系统调研的Id从1开始，组织调要放到系统调研前面，故出此下策
                        () -> new TreeMap<>(Comparator.<Long>comparingInt(k -> k > 100L ? 0 : 1)
                                .thenComparing(Long::compareTo)),
                        Collectors.groupingBy(QuestionnaireExportDTO::getQuestion)));

        final Map<Long, TreeLabelDTO> systemMap = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode())
                .stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId, Function.identity()));

        if (questionnaireGroup.containsKey(LabelEnum.XTDY.getCode())) {
            Map<String, List<QuestionnaireExportDTO>> questionMap = questionnaireGroup.get(LabelEnum.XTDY.getCode());
            systemMap.entrySet().stream().filter(entry -> Objects.equals(LabelEnum.XTDY.getCode(), entry.getValue().getSrcTreeId())).forEach(e -> {
                questionnaireGroup.put(e.getKey(), questionMap);
            });
            questionnaireGroup.remove(LabelEnum.XTDY.getCode());
        }

        Map<String, Object> model = new HashMap<>();
        model.put("operationName", coOperation.getOperationName());

        //TextRenderData unSelect = new TextRenderData(WordReader.symbol, new Style("Wingdings 2",10.5));

        XWPFDocument document = new XWPFDocument();

        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun titleFun = titleParagraph.createRun();
        titleFun.setText(WordReader.unCheckSymbol);
        titleFun.setFontSize(11);
        titleFun.setFontFamily("宋体");

        final List<Map<String, Object>> surveyList = new ArrayList<>();
        questionnaireGroup.forEach((systemId, questions) -> {
            Map<String, Integer> questionSortMap = templateContentMapper.selectQuestionSort(templateIds,
                    Objects.equals(systemId, LabelEnum.ZZDY.getCode()) ?
                            LabelEnum.ZZDY.getCode().toString() : LabelEnum.XTDY.getCode().toString()).stream().collect(Collectors.toMap(OfflineQuestionSortDTO::getTitle, OfflineQuestionSortDTO::getSort, (q1, q2) -> q1 <= q2 ? q1 : q2));
            Map<String, Object> surveyMap = new HashMap<>();
            String system = Objects.equals(LabelEnum.ZZDY.getCode(), systemId) ? LabelEnum.ZZDY.getName() : systemMap.get(systemId).getTreeName();
            surveyMap.put("survey", system);

            final List<Map<String, Object>> questionList = new ArrayList<>();
            questions.forEach((q, i) -> {
                Map<String, Object> questionMap = new HashMap<>();
                questionMap.put("question", q);
                questionMap.put("sort", questionSortMap.get(q));

                final List<Map<String, Object>> itemList = new ArrayList<>();
                i.stream().sorted(Comparator.comparing(QuestionnaireExportDTO::getItemId).reversed()).forEach(item -> {
                    Map<String, Object> itemMap = new HashMap<>();
                    itemMap.put("actionItem", titleFun);
                    itemMap.put("item", item.getItem());
                    itemList.add(itemMap);
                });
                questionMap.put("itemList", itemList);
                questionList.add(questionMap);
            });

            surveyMap.put("questionList", questionList.stream().sorted(Comparator.comparing(obj -> Integer.parseInt(obj.get("sort").toString()))).collect(Collectors.toList()));
            surveyList.add(surveyMap);
        });

        model.put("surveyList", surveyList);

        ClassPathResource classPathResource = new ClassPathResource("template/offlineQuestionnaire.docx");
        InputStream inputStream = classPathResource.getInputStream();

        XWPFTemplate template = XWPFTemplate.compile(inputStream).render(model);
        XWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        String realFileName = String.format("%s调研问卷.docx", coOperation.getOperationName());
        FileUtils.setAttachmentResponseHeader(response, realFileName);

        //输出结果
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
    }

    @Override
    @SneakyThrows
    @SchemaSwitch(String.class)
    @Transactional(rollbackFor = Exception.class)
    public void offlineQuestionnaireImport(String operationId, MultipartFile file) {
        // 校验文件是否是docx格式文件
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isEmpty(originalFilename) || !originalFilename.endsWith(".docx"))
            throw new ServiceException("仅支持.docx格式文件");
        // 文件大小不能超过5M
        if (file.getSize() > 5 * 1024 * 1024)
            throw new ServiceException("文件大小不能超过5M");
        CoOperation coOperation = coOperationMapper.selectById(operationId);
        if (Objects.isNull(coOperation))
            throw new ServiceException("作业不存在");
        boolean operationInapplicable = coOperation.getInapplicable();
        questionnaireValid(operationId);
        List<WordReader.StyleTextVO> textList = WordReader.readDocXWithMultiFile(file);
        // 离线问卷
        List<OfflineQuestionnaire> offlineQuestionnaires = buildOfflineQuestionnaire(textList);

        if (offlineQuestionnaires.stream().anyMatch(q -> !q.getFinish()))
            throw new ServiceException("离线调研问卷未填写完整");

        List<Long> templateIds = new ArrayList<>();
        if (coOperation.getTemplateId() >= CoOperation.COMBINATION_TEMPLATE_ID) {
            TemplateCombination templateCombination = templateCombinationMapper.selectById(coOperation.getTemplateId());
            templateIds.addAll(new ArrayList<>(Arrays.asList(templateCombination.getTemplateIds())));
        } else {
            templateIds.add(coOperation.getTemplateId());
        }
        // 调研问卷模板
        List<OfflineQuestionnaireTempDTO> offlineQuestionnaireTempList = templateContentMapper.selectOfflineQuestionnaire(templateIds);
        Map<Long, Map<Integer, List<OfflineQuestionnaireTempDTO>>> questionnaireGroup = offlineQuestionnaireTempList.stream()
                .collect(Collectors.groupingBy(OfflineQuestionnaireTempDTO::getObjectId,
                        TreeMap::new,
                        Collectors.groupingBy(dto -> MurmurHash.hash32(dto.getQuestionTitle()))));

        final Map<Long, TreeLabelDTO> systemMap = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode())
                .stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId, Function.identity()));

        if (questionnaireGroup.containsKey(LabelEnum.XTDY.getCode())) {
            Map<Integer, List<OfflineQuestionnaireTempDTO>> questionMap = questionnaireGroup.get(LabelEnum.XTDY.getCode());
            systemMap.entrySet().stream().filter(entry -> Objects.equals(LabelEnum.XTDY.getCode(), entry.getValue().getSrcTreeId())).forEach(e -> {
                questionnaireGroup.put(e.getKey(), questionMap);
            });
            questionnaireGroup.remove(LabelEnum.XTDY.getCode());
        }
        List<Questionnaire> questionnaires = new ArrayList<>();
        List<QuestionnaireContent>  questionnaireContents = new ArrayList<>();
        List<QuestionnaireItemDegree> questionnaireItemDegrees = new ArrayList<>();

        // 如选择个人信息不适用，离线导入作业默认对个人标签核查项不适用
        Tag tag = tagMapper.selectOne(new QueryWrapper<Tag>().eq("name", "个人信息"));

        questionnaireGroup.forEach((objectId, questionList) -> {
            String systemName = Objects.equals(objectId, LabelEnum.ZZDY.getCode()) ? LabelEnum.ZZDY.getName() : systemMap.get(objectId).getTreeName();
            Map<Integer, OfflineQuestionnaire> answersMap = offlineQuestionnaires.stream().filter(q ->
                    Objects.equals(q.getGroup(), systemName)).collect(Collectors.toMap(OfflineQuestionnaire::getHash, Function.identity()));
            if (answersMap.size() != questionList.size())
                throw new ServiceException("调研问卷问题数量与调研模板不一致");
            questionList.forEach((qHash, items) -> {
                OfflineQuestionnaireTempDTO o = items.get(0);
                if (!answersMap.containsKey(qHash))
                    throw new ServiceException(String.format("离线调研问卷问题{%s}与调研模板不一致", o.getQuestionTitle()));
                OfflineQuestionnaire questionnaire = answersMap.get(qHash);
                Map<String, OfflineQuestionnaire> children = questionnaire.getChildren().stream().collect(Collectors.toMap(obj -> obj.getTitle().trim(), Function.identity()));
                if (items.size() != children.size())
                    throw new ServiceException(String.format("离线调研问卷问题{%s}核查项数量与调研模板不一致", o.getQuestionTitle()));
                Questionnaire q = new Questionnaire();
                q.setOperationId(operationId);
                q.setObjectId(objectId);
                q.setQuestionId(o.getQuestionId());
                q.setQuestionTitle(o.getQuestionTitle());
                q.setQuestionDesc(o.getQuestionDesc());
                q.setMatchTags(o.getMatchTags());
                q.setFinished(questionnaire.getFinish());
                q.setInapplicable(Boolean.FALSE);
                q.setSort(questionnaire.getSort());
                questionnaires.add(q);

                boolean flag = questionnaire.getChildren().stream().filter(c -> c.getTitle().contains("以上均不符合")).anyMatch(OfflineQuestionnaire::getFinish);
                for (OfflineQuestionnaireTempDTO item : items) {
                    if (!children.containsKey(item.getItemTitle().trim()))
                        throw new ServiceException(String.format("离线调研问卷问题{%s}核查项{%s}与调研模板不一致", o.getQuestionTitle(), item.getItemTitle()));
                    boolean isInapplicable = item.getItemTag().contains(String.valueOf(tag.getId()));
                    OfflineQuestionnaire child = children.get(item.getItemTitle());
                    QuestionnaireContent c = new QuestionnaireContent();
                    c.setOperationId(operationId);
                    c.setObjectId(objectId);
                    c.setQuestionId(o.getQuestionId());
                    c.setItemId(item.getItemId());
                    c.setItemTitle(item.getItemTitle());
                    c.setItemDesc(item.getItemDesc());
                    if (operationInapplicable && isInapplicable) {
                        c.setChecked(Boolean.FALSE);
                        c.setInapplicable(Boolean.TRUE);
                    } else {
                        c.setChecked(flag && !Objects.equals(item.getItemId(), "00000000000000000000000000000000") ? Boolean.FALSE : child.getFinish());
                    }
                    c.setReverse(Boolean.FALSE);
                    questionnaireContents.add(c);

                    if (coOperation.getRelatedIndustry().contains("汽车")) {
                        QuestionnaireItemDegree degree = new QuestionnaireItemDegree();
                        degree.setOperationId(operationId);
                        degree.setTemplateId(coOperation.getTemplateId());
                        degree.setItemId(item.getItemId());
                        degree.setDegree(100);
                        degree.setType(1);
                        questionnaireItemDegrees.add(degree);
                    }
                }
            });
        });

        questionnaireMapper.delete(new QueryWrapper<Questionnaire>().eq("operation_id", operationId));
        PartitionUtils.part(questionnaires, this::saveBatch);

        questionnaireContentMapper.delete(new QueryWrapper<QuestionnaireContent>().eq("operation_id", operationId));
        PartitionUtils.part(questionnaireContents, questionnaireContentService::saveList);

        questionnaireItemDegreeService.remove(new QueryWrapper<QuestionnaireItemDegree>().eq("operation_id", operationId).eq("type", 1));
        PartitionUtils.part(questionnaireItemDegrees, questionnaireItemDegreeService::saveBatch);

    }

    @Override
    public synchronized String onlineQuestionnaireGenerate(String operationId, Integer jobType) {
        QuestionnaireManage manage = questionnaireManageMapper.selectOne(new QueryWrapper<QuestionnaireManage>()
                .eq("operation_id", operationId).eq("job_type", jobType));
        if (Objects.nonNull(manage)) {
            manage.setTimes(5);
            questionnaireManageMapper.updateById(manage);
            return manage.getPath();
        }
        QuestionnaireManage insert = new QuestionnaireManage();
        insert.setJobType(jobType);
        insert.setOperationId(operationId);
        insert.setPath(StrUtil.format("/#/questionnaire/{}/{}",jobType, operationId));
        insert.setTimes(5);
        questionnaireManageMapper.insert(insert);
        return insert.getPath();
    }

    @Override
    @SchemaSwitch(CommonDto.class)
    public OnlineQuestionContentVO selectOnlineQuestionnaire(CommonDto dto) {
        OnlineQuestionContentVO vo = new OnlineQuestionContentVO();
        QuestionnaireManage manage = questionnaireManageMapper.selectOne(
                new QueryWrapper<QuestionnaireManage>().eq("operation_id", dto.getOperationId()).eq("job_type", dto.getJobType()));
        if (Objects.isNull(manage))
            throw new ServiceException("非法操作");
        Map<Long, String> systemIds = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(dto.getOperationId(),
                LabelEnum.XTDY.getCode()).stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId, TreeLabelDTO::getTreeName, (k1, k2) -> k1, TreeMap::new));
        systemIds.put(LabelEnum.ZZDY.getCode(), LabelEnum.ZZDY.getName());
        List<OnlineQuestionContentVO.OnlineQuestionnaire> collect = systemIds.keySet().stream().map(id -> {
            dto.setLabelId(id);
            List<ContentVO> details = selectQuestionnaire(dto).getDetails();
            OnlineQuestionContentVO.OnlineQuestionnaire questionnaire = new OnlineQuestionContentVO.OnlineQuestionnaire();
            questionnaire.setLabelId(id);
            questionnaire.setLabelName(systemIds.get(id));
            questionnaire.setDetails(details);
            return questionnaire;
        }).collect(Collectors.toList());
        vo.setQuestionnaires(collect);
        vo.setOperationId(dto.getOperationId());
        vo.setTimes(manage.getTimes());
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void onlineQuestionnaireSave(OnlineQuestionContentVO vo) {
        QuestionnaireManage manage = questionnaireManageMapper.selectOne(
                new QueryWrapper<QuestionnaireManage>().eq("operation_id", vo.getOperationId()).eq("job_type", 1));
        if (Objects.isNull(manage))
            throw new ServiceException("非法操作");
        if (manage.getTimes() <= 0)
            throw new ServiceException("提交次数耗尽");
        manage.setTimes(manage.getTimes() - 1);
        String operationId = vo.getOperationId();
        questionnaireImportService.getBaseMapper().delete(new QueryWrapper<QuestionnaireImport>().eq("operation_id", vo.getOperationId()));
        questionnaireContentImportService.getBaseMapper().delete(new QueryWrapper<QuestionnaireContentImport>().eq("operation_id", vo.getOperationId()));
        questionnaireItemDegreeService.getBaseMapper().delete(new QueryWrapper<QuestionnaireItemDegree>().eq("operation_id", vo.getOperationId()).eq("type", 2));
        vo.getQuestionnaires().stream().map(q -> {
            QuestionnaireContentReq req = new QuestionnaireContentReq();
            req.setOperationId(operationId);
            req.setLabelId(q.getLabelId());
            req.setDetails(q.getDetails());
            return req;
        }).forEach(this::importForTemp);
        questionnaireManageMapper.updateById(manage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onlineQuestionnaireImport(String operationId) {
        questionnaireValid(operationId);
        int count1 = questionnaireImportService.count(new QueryWrapper<QuestionnaireImport>().eq("operation_id", operationId));
        if (count1 > 0) {
            int count2 = questionnaireContentImportService.count(new QueryWrapper<QuestionnaireContentImport>().eq("operation_id", operationId));
            if (count2 <= 0)
                throw new ServiceException("当前作业不存在在线调研问卷");
        } else {
            throw new ServiceException("当前作业不存在在线调研问卷");
        }
        questionnaireMapper.delete(new QueryWrapper<Questionnaire>().eq("operation_id", operationId));
        questionnaireContentMapper.delete(new QueryWrapper<QuestionnaireContent>().eq("operation_id", operationId));
        // 删除当前调研文件中的degreeList
        questionnaireItemDegreeService.getBaseMapper().delete(new QueryWrapper<QuestionnaireItemDegree>().eq("operation_id", operationId).eq("type", 1));
        questionnaireImportService.getBaseMapper().copyTempData(operationId);
        questionnaireContentImportService.getBaseMapper().copyTempData(operationId);
        // 导入的degreeList修改为问卷的
        UpdateWrapper<QuestionnaireItemDegree> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("type", 1);
        updateWrapper.eq("operation_id", operationId);
        updateWrapper.eq("type", 2);
        questionnaireItemDegreeService.update(new QuestionnaireItemDegree(), updateWrapper);
        questionnaireImportService.remove(new QueryWrapper<QuestionnaireImport>().eq("operation_id", operationId));
        questionnaireContentImportService.remove(new QueryWrapper<QuestionnaireContentImport>().eq("operation_id", operationId));
    }

    @Override
    @SchemaSwitch(AbilityItemReq.class)
    public ItemAbilityVO queryAbilityItem(AbilityItemReq req) {
        ItemAbilityVO result = new ItemAbilityVO();
        List<ItemAbilityVO.Config> techList = new ArrayList<>();
        List<ItemAbilityVO.Config> dbList = new ArrayList<>();
        result.setTechList(techList);
        result.setDbList(dbList);
        QueryWrapper<PreSourceConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", req.getOperationId());
        queryWrapper.eq("current_step", 4);
        if (!Objects.equals(LabelEnum.ZZDY.getCode(), req.getObjectId()))
            queryWrapper.eq("system_id", req.getObjectId());
        List<PreSourceConfig> preSourceConfigs = preSourceConfigMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(preSourceConfigs))
            return result;
        // 能力标签id
        Set<Long> appIds = preSourceConfigs.stream().flatMapToLong(p -> Arrays.stream(StrUtil.splitToLong(p.getAppIds(), StrUtil.COMMA))).filter(Objects::nonNull).boxed().collect(Collectors.toSet());
        Map<Integer, MkAppJob> mkAppJobMap = mkAppJobMapper.selectBatchIds(appIds).stream().filter(mkAppJob -> mkAppJob.getType() != -1).collect(Collectors.toMap(MkAppJob::getId, Function.identity()));
        List<Long> abilityIds = StrUtil.split(req.getAbilityIds(), StrUtil.COMMA).stream().mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
        if (CollUtil.isEmpty(abilityIds))
            return result;
        List<Tag> tags = tagMapper.selectBatchIds(abilityIds);

        for (PreSourceConfig sourceConfig : preSourceConfigs) {
            ItemAbilityVO.Config config = new ItemAbilityVO.Config();
            config.setName(sourceConfig.getConfigName());
            techList.add(config);
            // 当前数据源的能力作业
            Map<Integer, MkAppJob> sourceJobMap = StrUtil.split(sourceConfig.getAppIds(), StrUtil.COMMA).stream()
                .filter(i -> mkAppJobMap.containsKey(Integer.parseInt(i)))
                .map(i -> mkAppJobMap.get(Integer.parseInt(i)))
                .collect(Collectors.toMap(MkAppJob::getType, Function.identity()));
            // 数据库检测项
            if (AssessModuleType.contains(sourceConfig.getAccessModule(), AssessModuleType.BASIC)) {
                ItemAbilityVO.Config config2 = new ItemAbilityVO.Config();
                config2.setName(sourceConfig.getConfigName());
                dbList.add(config2);
                List<DetectionResultDTO> detectionResultDTOS = detectionResultMapper.queryAbilityItem(req.getOperationId(), abilityIds, sourceConfig.getConfigId());
                List<ItemAbilityVO.Item> collect = detectionResultDTOS.stream()
                    .collect(Collectors.groupingBy(DetectionResultDTO::getName))
                    .entrySet().stream()
                    .map(entry -> {
                        ItemAbilityVO.Item item = new ItemAbilityVO.Item();
                        item.setName(entry.getKey());

                        Set<Boolean> results = entry.getValue().stream()
                            .map(d -> Objects.nonNull(d.getResult()) && d.getResult())
                            .collect(Collectors.toSet());

                        String status;
                        if (results.size() == 1) {
                            status = results.contains(Boolean.TRUE) ? "具备" : "不具备";
                        } else {
                            status = "部分具备";
                        }

                        item.setResult(status);
                        return item;
                    })
                    .collect(Collectors.toList());
                config2.setItems(collect);
            }
            List<ItemAbilityVO.Item> techItemList = new ArrayList<>();
            config.setItems(techItemList);
            // 技术检测项
            for (Tag t : tags) {
                String introduce = t.getIntroduce();
                if (!introduce.contains(StrUtil.COLON))
                    continue;
                String key = StrUtil.subBefore(introduce, StrUtil.COLON, false);
                AbilityType type = AbilityType.getByKey(key);
                if (Objects.isNull(type))
                    continue;
                ItemAbilityVO.Item item = new ItemAbilityVO.Item();
                MkAppJob mkAppJob = sourceJobMap.get(type.getCode());
                if (mkAppJob == null || Objects.isNull(mkAppJob.getParamOut()))
                    continue;
                ApiJobQueryVO bean = JSONUtil.toBean(JSONUtil.toJsonStr(mkAppJob.getParamOut()), ApiJobQueryVO.class);
                item.setName(t.getName());
                item.setResult(transformResult(type, Double.parseDouble(bean.getResult().toString())));
                techItemList.add(item);
            }
        }
        return result;
    }

    @Override
    public List<ItemAbilityDTO> selectItemAbilityWithLifecycleTag(String operationId) {
        return questionnaireMapper.selectItemAbilityWithLifecycleTag(operationId);
    }

    private String transformResult(AbilityType type, Double result) {
        switch (type) {
            case DESENSITIZATION :
            case ENCRYPT:
                if (result == 0)
                    return "不具备";
                else if (result == 0.5)
                    return "部分具备";
                else
                    return "具备";
            case PCAP:
                if (result == 0)
                    return "具备";
                else
                    return "不具备";
            default:
                return StrUtil.EMPTY;
        }
    }

    private void questionnaireValid(String operationId) {
        List<CoProgress> progressList = coProgressMapper.selectList(new QueryWrapper<CoProgress>().eq("operation_id", operationId));
        if (progressList.stream().anyMatch(p -> p.getLabelId().toString().startsWith(LabelEnum.ZZDY.getCode().toString())
                || p.getLabelId().toString().startsWith(LabelEnum.XTDY.getCode().toString())))
            throw new ServiceException("当前作业存在已提交的调研问卷，请取消后重新导入");
    }

    private List<OfflineQuestionnaire> buildOfflineQuestionnaire(List<WordReader.StyleTextVO> textList) {
        List<OfflineQuestionnaire> result = new ArrayList<>();
        String group = StrUtil.EMPTY;
        AtomicInteger sort = new AtomicInteger(1);
        for (int i = 0; i < textList.size();) {
            WordReader.StyleTextVO text = textList.get(i);
            if (text.getLevel() == 3) {
                group = text.getText();
                ++i;
                continue;
            }
            if (text.getLevel() != 4) {
                ++i;
                continue;
            }
            boolean qFinish = Boolean.FALSE;
            OfflineQuestionnaire question = OfflineQuestionnaire.builder().title(text.getText()).group(group).finish(qFinish).hash(MurmurHash.hash32(text.getText())).build();
            List<OfflineQuestionnaire> children = new ArrayList<>();
            while (++i < textList.size() && textList.get(i).getLevel() == -1) {
                boolean iFinish = textList.get(i).getText().contains(WordReader.checkedSymbol);
                if (iFinish)
                    qFinish = Boolean.TRUE;
                textList.get(i).setText(StrUtil.sub(textList.get(i).getText(), 2, textList.get(i).getText().length()));
                OfflineQuestionnaire item = OfflineQuestionnaire.builder().title(textList.get(i).getText()).finish(iFinish).build();
                if (children.stream().anyMatch(r -> Objects.equals(r.getTitle(), item.getTitle())))
                    throw new ServiceException(String.format("问题{%s}核查项{%s}重复", question.getTitle(), item.getTitle()));
                children.add(item);
            }
            question.setFinish(qFinish);
            question.setChildren(children);
            question.setSort(sort.getAndIncrement());
            result.add(question);
        }
        return result;
    }

    private List<ContentVO> buildDetails(List<TemplateContent> contentList, Map<String, Set<FileLocateDTO>> itemFileMap, List<Long> templateIds,
                                         Boolean inapplicable, Long tagId, boolean carIndustry, Map<Long, String> abilityTagMap, Map<String, String> abilityItemMap) {
        final List<ContentVO> res = Lists.newArrayList();
        final Set<String> questionIds = new HashSet<>();
        final Set<String> itemIds = new HashSet<>();
        if (CollUtil.isEmpty(contentList))
            return res;
        contentList.forEach(content -> {
            questionIds.add(content.getQuestionId());
            itemIds.add(content.getItemId());
        });
        List<QuestionSortVO> questionSortList = questionMapper.selectQuesSort(templateIds, questionIds);
        Map<String, QuestionSortVO> questionMap = new HashMap<>();
        int accumulatedSort = 0; // 累计的排序偏移量
        // 按照模板顺序存放问题列表
        for (Long templateId : templateIds) {
            List<QuestionSortVO> templateQuestionSortList = questionSortList.stream().filter(q -> Objects.nonNull(q.getTemplateId())
                    && q.getTemplateId().equals(templateId)).sorted(Comparator.comparing(QuestionSortVO::getSort)).collect(Collectors.toList());
            for (QuestionSortVO questionSortVO : templateQuestionSortList) {
                String questionId = questionSortVO.getId();
                // 如果该问题已处理过，则跳过
                if (questionMap.containsKey(questionId)) {
                    continue;
                }
                questionSortVO.setSort(accumulatedSort + questionSortVO.getSort());
                questionMap.put(questionSortVO.getId(), questionSortVO);
            }
            // 更新累计偏移量
            if (!templateQuestionSortList.isEmpty()) {
                int maxSortInTemplate = templateQuestionSortList.stream()
                        .mapToInt(QuestionSortVO::getSort)
                        .max()
                        .orElse(0);
                accumulatedSort += maxSortInTemplate + 1; // 保证下一个模板的序号不重叠
            }
        }

        List<QuestionSortVO> nonSortQuestion = questionSortList.stream().filter(q -> Objects.isNull(q.getTemplateId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(nonSortQuestion)) {
            for (QuestionSortVO vo : nonSortQuestion) {
                questionMap.put(vo.getId(), vo);
            }
        }


        QueryWrapper<Item> itemQuery = new QueryWrapper<>();
        itemQuery.eq("status", 0);
        itemQuery.in("id", itemIds);
        Map<String, Item> itemMap = itemMapper.selectList(itemQuery).stream().collect(Collectors.toMap(Item::getId, i -> i));

        Map<String, List<TemplateContent>> map = contentList.stream().collect(Collectors.groupingBy(TemplateContent::getQuestionId));
        map.forEach((key, value) -> {
            ContentVO content = buildContent(questionMap.get(key));
            // 组合模板 先对同一个问题下的核查项进行去重,组合条件标签
            value = value.stream().collect(Collectors.toMap(TemplateContent::getItemId, Function.identity(), (v1, v2) -> {
                Set<String> conditionTags = new HashSet<>(StrUtil.split(v1.getConditionTags(), StrUtil.COMMA));
                conditionTags.addAll(StrUtil.split(v2.getConditionTags(), StrUtil.COMMA));
                v1.setConditionTags(String.join(StrUtil.COMMA, conditionTags));
                return v1;
            })).values().stream().distinct().collect(Collectors.toList());
            content.setItems(buildItems(value, itemMap, itemFileMap, inapplicable, tagId, carIndustry, abilityTagMap, abilityItemMap));
            content.setFinished(content.getItems().stream().anyMatch(ContentVO.ItemVO::getIsPersonal));
            res.add(content);
        });

        return res.stream().sorted(Comparator.comparing(ContentVO::getSort)).collect(Collectors.toList());
    }

    private List<ContentVO.ItemVO> buildItems(List<TemplateContent> value, Map<String, Item> itemMap,
                                              Map<String, Set<FileLocateDTO>> itemFileMap, boolean inapplicable, Long tagId, boolean carIndustry, Map<Long, String> abilityTagMap, Map<String, String> abilityItemMap) {
        final List<ContentVO.ItemVO> list = new ArrayList<>();
        final Map<String, TemplateContent> contentMap = value.stream().collect(Collectors.toMap(TemplateContent::getItemId, Function.identity()));
        // 核查项父子关系
        final Map<String, Set<TemplateContent>> itemRelationMap = value.stream().filter(i -> itemMap.containsKey(i.getItemId()))
                .collect(Collectors.groupingBy(i -> Optional.ofNullable(itemMap.get(i.getItemId()).getParentId()).orElse(StrUtil.EMPTY), Collectors.toSet()));
        itemRelationMap.forEach((parentId, children) -> {
            // 排除有子节点的核查项，属于第二种场景，避免重复
            List<ContentVO.ItemVO> collect = children.stream().filter(c -> !itemRelationMap.containsKey(c.getItemId())).map(c -> buildItem(itemFileMap, inapplicable, tagId, carIndustry, abilityTagMap, itemMap.get(c.getItemId()), c, abilityItemMap)).collect(Collectors.toList());
            // 如果没有父节点，或者父节点的核查项不在该调研问题中，视为一级核查项
            if (StrUtil.isEmpty(parentId) || !contentMap.containsKey(parentId))
                list.addAll(collect);
            else {
                ContentVO.ItemVO parent = buildItem(itemFileMap, inapplicable, tagId, carIndustry, abilityTagMap, itemMap.get(parentId), contentMap.get(parentId), abilityItemMap);
                parent.setChildren(collect);
                list.add(parent);
            }
        });
        return list.stream().sorted(Comparator.comparing(ContentVO.ItemVO::getItemId).reversed()).collect(Collectors.toList());
    }

    private ContentVO.ItemVO buildItem(Map<String, Set<FileLocateDTO>> itemFileMap, boolean inapplicable,
                                  Long tagId, boolean carIndustry, Map<Long, String> abilityTagMap, Item item, TemplateContent content, Map<String, String> abilityItemMap) {
        Set<FileLocateDTO> fileLocateDTOS = itemFileMap.get(item.getId());
        Set<String> reference = new HashSet<>();
        if (Objects.nonNull(fileLocateDTOS) && !fileLocateDTOS.isEmpty())
            reference = fileLocateDTOS.stream().map(FileLocateDTO::getFileName).collect(Collectors.toSet());
        boolean isPersonal = item.getTagIds().contains(String.valueOf(tagId));
        return ContentVO.ItemVO.builder()
                .questionId(content.getQuestionId())
                .itemId(content.getItemId())
                .itemTitle(item.getTitle())
                .itemDesc(item.getDescribe())
                .checked(Boolean.FALSE)
                .exc(item.getExc())
                .excDesc(item.getExcDesc())
                .reverse(item.getReverse())
                .reference(reference)
                .inapplicable(inapplicable && isPersonal)
                .isPersonal(isPersonal)
                .degree(carIndustry ? 100 : null)
                .conditionTags(content.getConditionTags())
                .conditionTagNames(StrUtil.isEmpty(content.getConditionTags()) ? StrUtil.EMPTY : StrUtil.split(content.getConditionTags(), StrUtil.COMMA).stream().filter(StrUtil::isNotEmpty).map(t ->
                        abilityTagMap.get(Long.valueOf(t))).filter(Objects::nonNull).collect(Collectors.joining(StrUtil.COMMA)))
                .abilityIds(abilityItemMap.get(content.getItemId()))
                .build();
    }

    private List<ContentVO> buildDetails(List<Questionnaire> questionnaires, List<QuestionnairePersonContentVO> questionnaireContents,
                                         Map<String, Set<FileLocateDTO>> itemFileMap, Long tagId, Map<String, Integer> degreeMap, Map<Long, String> abilityTagMap, Map<String, String> abilityItemMap) {
        // key : questionId value : item 信息
        final Map<String, List<QuestionnairePersonContentVO>> map = questionnaireContents.stream().collect(
                Collectors.groupingBy(QuestionnairePersonContentVO::getQuestionId));
        Map<String, Item> itemMap = itemMapper.selectList(new QueryWrapper<Item>().eq("status", 0)).stream().collect(Collectors.toMap(Item::getId, Function.identity()));
        return questionnaires.stream().map(q -> {
            ContentVO contentVO = buildContent(q);
            contentVO.setItems(buildItems(q.getQuestionId(), map, itemFileMap, tagId, degreeMap, abilityTagMap, itemMap, abilityItemMap));
            return contentVO;
        }).sorted(Comparator.comparing(ContentVO::getSort)).collect(Collectors.toList());
    }

    private List<ContentVO.ItemVO> buildItems(String questionId, Map<String, List<QuestionnairePersonContentVO>> map, Map<String, Set<FileLocateDTO>> itemFileMap,
                                              Long tagId, Map<String, Integer> degreeMap, Map<Long, String> abilityTagMap, Map<String, Item> itemMap, Map<String, String> abilityItemMap) {
        final List<ContentVO.ItemVO> list = new ArrayList<>();
        List<QuestionnairePersonContentVO> questionnairePersonContentVOS = map.get(questionId);
        final Map<String, QuestionnairePersonContentVO> contentMap = questionnairePersonContentVOS.stream().collect(Collectors.toMap(QuestionnairePersonContentVO::getItemId, Function.identity()));
        // 核查项父子关系
        final Map<String, Set<QuestionnairePersonContentVO>> itemRelationMap = contentMap.values().stream().filter(i -> itemMap.containsKey(i.getItemId()))
                .collect(Collectors.groupingBy(i -> Optional.ofNullable(itemMap.get(i.getItemId()).getParentId()).orElse(StrUtil.EMPTY), Collectors.toSet()));
        itemRelationMap.forEach((parentId, children) -> {
            List<ContentVO.ItemVO> collect = children.stream().filter(c -> !itemRelationMap.containsKey(c.getItemId())).map(c -> buildItem(itemFileMap, tagId, degreeMap, abilityTagMap, c, abilityItemMap)).collect(Collectors.toList());
            // 如果没有父节点，或者父节点的核查项不在该调研问题中，视为一级核查项
            if (StrUtil.isEmpty(parentId) || !contentMap.containsKey(parentId))
                list.addAll(collect);
            else {
                ContentVO.ItemVO parent = buildItem(itemFileMap, tagId, degreeMap, abilityTagMap, contentMap.get(parentId), abilityItemMap);
                parent.setChildren(collect);
                list.add(parent);
            }
        });
        return list.stream().sorted(Comparator.comparing(ContentVO.ItemVO::getItemId).reversed()).collect(Collectors.toList());
    }

    private static ContentVO.ItemVO buildItem(Map<String, Set<FileLocateDTO>> itemFileMap, Long tagId, Map<String, Integer> degreeMap,
                                              Map<Long, String> abilityTagMap, QuestionnairePersonContentVO content, Map<String, String> abilityItemMap) {
        Set<FileLocateDTO> fileLocateDTOS = itemFileMap.get(content.getItemId());
        Set<String> reference = new HashSet<>();
        if (Objects.nonNull(fileLocateDTOS) && !fileLocateDTOS.isEmpty())
            reference = fileLocateDTOS.stream().map(FileLocateDTO::getFileName).collect(Collectors.toSet());
        return ContentVO.ItemVO.builder()
                .id(content.getId())
                .questionId(content.getQuestionId())
                .itemId(content.getItemId())
                .itemTitle(content.getItemTitle())
                .itemDesc(content.getItemDesc())
                .checked(content.getChecked())
                .exc(content.getExc())
                .excDesc(content.getExcDesc())
                .reverse(content.getReverse())
                .explain(content.getExplain())
                .inapplicable(content.getInapplicable())
                .inapplicableType(content.getInapplicableType())
                .isPersonal(content.getItemTag().contains(String.valueOf(tagId)))
                .inapplicableReason(content.getInapplicableReason())
                .degree(degreeMap.get(content.getItemId()))
                .reference(reference)
                .conditionTags(content.getConditionTags())
                .conditionTagNames(StrUtil.isEmpty(content.getConditionTags()) ? StrUtil.EMPTY : StrUtil.split(content.getConditionTags(), StrUtil.COMMA).stream().filter(StrUtil::isNotEmpty).map(t ->
                        abilityTagMap.get(Long.valueOf(t))).filter(Objects::nonNull).collect(Collectors.joining(StrUtil.COMMA)))
                .abilityIds(abilityItemMap.get(content.getItemId()))
                .build();
    }

    private ContentVO buildContent(QuestionSortVO q) {
        return ContentVO.builder()
                .questionId(q.getId())
                .questionTitle(q.getTitle())
                .questionDesc(q.getDescribe())
                .matchTags(q.getTagIds())
                .finished(Boolean.FALSE)
                // 没有设置序号的往后排
                .sort(Objects.isNull(q.getSort()) ? 999 : q.getSort())
                .build();
    }

    private ContentVO buildContent(Questionnaire q) {
        return ContentVO.builder()
                .id(q.getId())
                .questionId(q.getQuestionId())
                .questionTitle(q.getQuestionTitle())
                .questionDesc(q.getQuestionDesc())
                .followed(q.getFollowed())
                .matchTags(q.getMatchTags())
                .finished(q.getFinished())
                .inapplicable(q.getInapplicable())
                .inapplicableReason(q.getInapplicableReason())
                .sort(Objects.isNull(q.getSort()) ? 999 : q.getSort())
                .build();
    }

}
