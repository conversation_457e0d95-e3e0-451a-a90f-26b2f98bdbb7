package com.dcas.system.service;

import com.dcas.common.core.param.DataSourceParam;
import com.dcas.common.domain.entity.PreSourceConfig;
import com.dcas.common.model.dto.SchemaTableDTO;
import com.dcas.common.model.query.CompareColumnQuery;
import com.dcas.common.model.req.*;
import com.dcas.common.model.vo.*;
import com.dcas.system.domain.resp.TestConnectionResponse;
import com.mchz.datasource.cli.RsResult;

import java.util.List;

/**
 * <p>元数据服务接口</p>
 *
 * <AUTHOR>
 * @Date 2022/12/23 9:27
 * @Version 1.0
 * @Description
 */
public interface DatabaseService {

    /**
     * 测试链接
     *
     * @param config 连接配置
     * @return true
     */
    TestConnectionResponse testConnection(DataSourceParam config);

    /**
     * sql查询
     *
     * @param param 数据源配置信息
     * @param sql 查询sql
     * @return 查询结果
     * @throws Exception 查询异常
     */
    RsResult openQuery(DataSourceParam param, String sql) throws Exception;

    /**
     * 关闭连接
     *
     * @param key 键
     */
    void close(String key);

    /**
     * 补充配置信息 & 解密连接信息
     *
     * @param config 数据源配置
     *        needDecryptPwd 是否需要解密密码
     */
    void updateConfig(DataSourceParam config, boolean needDecryptPwd);

    /**
     * buildKey
     *
     * @param param 数据源连接配置
     * @return key
     */
    String buildKey(DataSourceParam param);

    /**
     * 添加数据源连接
     */
    Integer addSourceConfig(SourceConfigAddReq request);

    /**
     * 删除数据源连接
     *
     * @param id 数据源连接id
     */
    void deleteSourceConfig(Integer id);

    /**
     * 应用数据源连接
     *
     * @param req 请求参数
     */
    SourceConfigResultVO applySourceConfig(SourcePageConfirmReq req);

    List<SchemaTableDTO> listSourceSchema(Integer id, String operationId);

    List<SchemaTableDTO> listSourceTable(Integer id, String schema, Boolean isPreSource);

    SourceConfigUpdateReq querySourceConfigById(Integer id);

    void confirmAssessRange(SourceRangeConfirmReq req);

    List<PreSourceConfigVO> queryPreSourceConfigList(PreSourceConfigRequest request);

    void updateSourceConfig(SourceConfigUpdateReq req);

    ConnectionTaskVO queryConnectionTask(String operationId);

    void startAssetJob(Long id);

    void stopAssetJob(Long id);

    void fillWorkConfig(WorkConfigFillReq req);

    Integer copyGlobalSourceConfig(Integer sourceId, String operationId);

    List<MkCompareColumnVO> listSourceTableColumn(CompareColumnQuery query);

    List<ColumnVO> listColumns(Integer id, String schema, String table);

    void batchDeleteSourceConfig(IdsReq ids);

    DataSourceParam toSourceConfigUsable(PreSourceConfig config);
}
