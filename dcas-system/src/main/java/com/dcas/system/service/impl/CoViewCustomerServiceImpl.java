package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.DataScope;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.constant.Constants;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.model.LoginUser;
import com.dcas.common.enums.*;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.req.ApiJobQueryReq;
import com.dcas.common.utils.Arith;
import com.dcas.common.utils.DcasUtil;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.StringUtils;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.market.app.constant.AppInterfaceConstants;
import com.dcas.system.calc.spec.BetweenBaseRule;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.param.GapAnalysisQuery;
import com.dcas.common.model.req.AnalysisCharReq;
import com.dcas.common.model.vo.*;
import com.dcas.common.mapper.*;
import com.dcas.system.report.ReportGroupEnum;
import com.dcas.system.report.ReportMainEntry;
import com.dcas.system.service.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.mchz.dcas.client.common.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 客户视图实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoViewCustomerServiceImpl implements CoViewCustomerService {

    private final CoCustomerMapper coCustomerMapper;
    private final CoProjectMapper coProjectMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final CoConstantMapper coConstantMapper;
    private final CoAuthorityService coAuthorityService;
    private final CoGapAnalysisService coGapAnalysisService;
    private final CoGapAnalysisMapper coGapAnalysisMapper;
    private final CoBasicEnMapper coBasicEnMapper;
    private final CoLegalMapper coLegalMapper;
    private final CoLifecycleMapper coLifecycleMapper;
    private final ComplianceTemplateMapper complianceTemplateMapper;
    private final CoWorkbenchViewServiceImpl coWorkbenchViewService;
    private final CoProjectService coProjectService;
    private final IRiskAnalysisService iRiskAnalysisService;
    private final ReportMainEntry reportMainEntry;
    private final ISysConfigService sysConfigService;
    private final SpecCalcResultService specCalcResultService;
    private final SpecialEvaluationConditionConfigMapper specialEvaluationConditionConfigMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final CoAssetUserMapper coAssetUserMapper;
    private final DetectionResultMapper detectionResultMapper;
    private final AdviseRiskMapper adviseRiskMapper;
    private final MkAppJobMapper mkAppJobMapper;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final CoPermissionMapper coPermissionMapper;

    public static final String IMAGE_PNG_BASE64 = "data:image/png;base64,";
    private static final String BASE64 = "base64";
    private static final String HEIGHT = "height";
    private static final String WIDTH = "width";
    private static final String ITEM_PREFIX = "不具备";


    /**
     * 查询客户业务情况
     *
     * @return * @return RestResponse
     * <AUTHOR>
     */
    @Override
    public QueryStatusView retrieveStatus(RequestModel<CustomerIdDto> dto) {
        //客户id
        CoCustomer coCustomer = coCustomerMapper.selectById(dto.getPrivator().getCustomerId());
        QueryStatusView vo = new QueryStatusView();

        /*
         * 项目情况
         */
        QueryWrapper<CoProject> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_id", dto.getPrivator().getCustomerId());
        List<CoProject> coProjectList = coProjectMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(coProjectList)) {
            return vo;
        }
        BeanUtils.copyProperties(coCustomer, vo);
        //待启动
        List<CoProject> collect1 = coProjectList.stream().filter(p -> p.getStatus() == 0).collect(Collectors.toList());
        //进行中
        List<CoProject> collect2 = coProjectList.stream().filter(p -> p.getStatus() == 1).collect(Collectors.toList());
        //已完成
        List<CoProject> collect3 = coProjectList.stream().filter(p -> p.getStatus() == 2).collect(Collectors.toList());

        vo.setWaitStart(collect1.size());
        vo.setOnGoing(collect2.size());
        vo.setCompleted(collect3.size());
        /*
         * 作业情况
         */
        List<String> projectIdList = coProjectList.stream().map(CoProject::getProjectId).distinct().collect(Collectors.toList());
        QueryWrapper<CoOperation> query = new QueryWrapper<>();
        query.in("project_id", projectIdList);
//        query.eq("create_by", SecurityUtils.getUserAccount());

        List<CoOperation> coOperationList = coOperationMapper.selectList(query);
        //评估中
        List<CoOperation> collect4 = coOperationList.stream().filter(p -> p.getStatus() == 1).filter(this::statusFilter).collect(Collectors.toList());
        //复核中
        List<CoOperation> collect5 = coOperationList.stream().filter(p -> p.getStatus() == 2).filter(this::statusFilter).collect(Collectors.toList());
        //已结项
        List<CoOperation> collect6 = coOperationList.stream().filter(p -> p.getStatus() == 3).collect(Collectors.toList());
        vo.setOnAssessing(collect4.size());
        vo.setOnReviewing(collect5.size());
        vo.setCompletedOperation(collect6.size());

        return vo;
    }

    private boolean statusFilter(CoOperation co) {
        BigDecimal progress = co.getProgress();
        if (Objects.isNull(progress)) {
            return false;
        }
        return progress.longValue() != 100L;
    }

    /**
     * 查询作业列表
     *
     * @param dto request
     */
    @Override
    @DataScope(deptAlias = "a", userAlias = "a")
    public List<QueryOperationView> selectOperationList(RequestModel<QueryOperationViewDto> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        return coOperationMapper.queryOperationView(dto.getPrivator());
    }

    /**
     * 数据资产分析
     *
     * @param dto request
     */
    @Override
    public QueryDataAssetAnalysisVo queryDataAssetsAnalysis(RequestModel<PrimaryKeyDTO> dto) {
        /*
         * 注：跟资产盘点有关
         * 注：当常量最高敏感等级>=3时，资产敏感等级>2的行数
         */
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        QueryDataAssetAnalysisVo vo = new QueryDataAssetAnalysisVo();
        QueryWrapper<CoInventory> query = new QueryWrapper<>();
        query.eq("operation_id", dto.getPrivator().getId());
        query.eq("label_id", LabelEnum.ZCPD.getCode());
        List<CoInventory> coInventories = coInventoryMapper.selectList(query);
        if (CollUtil.isEmpty(coInventories)) {
            return vo;
        }

        QueryWrapper<CoConstant> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_id", coOperationMapper.selectCustomIdByOperationId(dto.getPrivator().getId()));
        List<CoConstant> coConstants = coConstantMapper.selectList(queryWrapper);
        //客户最高敏感等级
        int level = coConstants.get(0).getHighestSensitiveLevel();

        // 区别高中低敏感资产
        //sensitiveAssetProcess(level,coInventories, highSensitiveAssetsList, mediumSensitiveAssetsList, lowSensitiveAssetsList);
        long coreAssetCount = coInventories.stream().filter(i -> Objects.equals(i.getDataTag(), DataTagEnum.CORE.getTag())).count();
        long importantAssetCount = coInventories.stream().filter(i -> Objects.equals(i.getDataTag(), DataTagEnum.IMPORTANT.getTag())).count();
        long generalAssetCount = coInventories.stream().filter(i -> Objects.equals(i.getDataTag(), DataTagEnum.GENERAL.getTag())).count();

        //敏感级别饼图
        Map<String, BigDecimal> map = new TreeMap<>(Comparator.comparing(String::valueOf).reversed());
        //不同级别总数
        int levelSum = 0;
        Map<Integer, List<CoInventory>> collect = coInventories.stream().filter(p -> p.getSensitiveLevel() != null).collect(Collectors.groupingBy(CoInventory::getSensitiveLevel));

        for (Integer key : collect.keySet()) {
            List<CoInventory> coInventoryList = collect.get(key);
            levelSum = levelSum + coInventoryList.size();
        }

        for (Integer key : collect.keySet()) {
            List<CoInventory> coInventoryList = collect.get(key);
            //级别key
            String sLevel = key + "级";
            //级别对应数量size/总数=占比
            double v = (double) coInventoryList.size() / levelSum * 100;
            BigDecimal proportion = BigDecimal.valueOf(Arith.round(v, 2));
            map.put(sLevel, proportion);
        }

        if (level == 0) {
            level = 8;
        }
        for (SensitiveLevelEnum sensitiveLevelEnum : SensitiveLevelEnum.values()) {
            if (sensitiveLevelEnum.getCode() > level || map.containsKey(sensitiveLevelEnum.getInfo())) {
                continue;
            }
            map.put(sensitiveLevelEnum.getInfo(), BigDecimal.ZERO);
        }

        vo.setDataAssetNum(coInventories.size());
        vo.setRelatedSystemNum((int)coInventories.stream().map(CoInventory::getBusSystem).distinct().count());
        vo.setHighSensitiveAssetsNum(coreAssetCount);
        vo.setMediumSensitiveAssetsNum(importantAssetCount);
        vo.setLowSensitiveAssetsNum(generalAssetCount);
        vo.setHighSensitiveAssetsProportion(BigDecimal.valueOf(Arith.round(coreAssetCount * 100.0 / coInventories.size(), 2)));
        vo.setMediumSensitiveAssetsProportion(BigDecimal.valueOf(Arith.round(importantAssetCount * 100.0 / coInventories.size(), 2)));
        vo.setLowSensitiveAssetsProportion(BigDecimal.valueOf(Arith.round(generalAssetCount * 100.0 / coInventories.size(), 2)));
        vo.setLevelMap(map);
        return vo;
    }

    private void sensitiveAssetProcess(int level, List<CoInventory> coInventories, List<CoInventory> highSensitiveAssetsList, List<CoInventory> mediumSensitiveAssetsList,
        List<CoInventory> lowSensitiveAssetsList) {
        // 设计一个哈希表存储level对应的敏感等级分类标准
        Map<Integer, int[]> sensitivityMapping = new HashMap<>();
        sensitivityMapping.put(3, new int[]{1, 2});
        sensitivityMapping.put(4, new int[]{1, 2});
        sensitivityMapping.put(5, new int[]{1, 3});
        sensitivityMapping.put(6, new int[]{2, 4});
        sensitivityMapping.put(7, new int[]{2, 5});
        sensitivityMapping.put(8, new int[]{2, 5});

        int[] sensitivityThresholds = sensitivityMapping.get(level);
        for (CoInventory coInventory : coInventories) {
            Integer sensitiveLevel = coInventory.getSensitiveLevel();
            if (sensitiveLevel != null) {
                if (sensitiveLevel <= sensitivityThresholds[0]) {
                    lowSensitiveAssetsList.add(coInventory);
                } else if (sensitiveLevel <= sensitivityThresholds[1]) {
                    mediumSensitiveAssetsList.add(coInventory);
                } else {
                    highSensitiveAssetsList.add(coInventory);
                }
            }
        }
    }

    /**
     * 权限现状分析
     *
     * @param dto request
     */
    @Override
    public ReportAuthorityVo authorityAnalysis(RequestModel<QueryAuthorityReportDto> dto) {

        ReportAuthorityVo analysis = new ReportAuthorityVo();
        return analysis;
    }

    @Override
    public List<ReportAuthorityStatusQuoVo> queryUserAuthResult(RequestModel<OperationIdDto> dto) {
        return coAuthorityService.queryStatusQuo(dto);
    }

    @Override
    public PageInfo<ReportAuthorityByUserVo> queryUserAuthType(RequestModel<QueryAuthorityReportDto> dto) {
        //分页
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        //查询结果
        List<ReportAuthorityByUserVo> voList = coAuthorityService.queryAuthorityByUser(dto);
        //设置分页条件，根据list分页
        return new PageInfo<>(voList);
    }

    @Override
    public List<ReportAuthorityUserAssetVo> queryUserAssetResult(RequestModel<OperationIdDto> dto) {
        return coAuthorityService.queryUserAssetsResult(dto);
    }


    /**
     * 能力差距分析--查询图表名称
     *
     * @param dto
     */
    @Override
    public List<String> queryChartName(OperationIdDto dto) {
        //调用图表模板查询
//        List<QueryChartModelVo> voList = coGapAnalysisService.queryChartModel(dto);
        GapAnalysisQuery gapAnalysisQuery = new GapAnalysisQuery();
        gapAnalysisQuery.setOperationId(dto.getOperationId());
        gapAnalysisQuery.setLabelId(LabelEnum.CJFX.getCode());
        List<QueryGapAnalysisVo> voList = coGapAnalysisMapper.queryByChartId(gapAnalysisQuery);
        return voList.stream().map(QueryGapAnalysisVo::getChartName).collect(Collectors.toList());
    }

    /**
     * 能力差距分析--查询分析图表
     *
     * @param dto request
     */
    @Override
    public List<CoGapAnalysis> queryGapAnalysisChart(RequestModel<QueryGapAnalysisChartViewDto> dto) {
        //入参校验
        QueryGapAnalysisChartViewDto request = dto.getPrivator();
        CheckUtil.checkParams(request);
        QueryWrapper<CoGapAnalysis> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", request.getOperationId());
        queryWrapper.eq("chart_name", request.getChartName());
        queryWrapper.notIn("indicator", Lists.newArrayList("管理能力", "技术能力"));
        queryWrapper.orderByAsc("sort");
        List<CoGapAnalysis> coGapAnalysisList = coGapAnalysisMapper.selectList(queryWrapper);
        //根据chartId-图表id找到不同的图表数据填充
        List<String> lifeCycleSort = Lists.newArrayList("数据采集安全", "数据传输安全", "数据存储安全", "数据处理安全", "数据交换安全", "数据销毁安全", "通用安全");
        List<String> dimensionSort = Lists.newArrayList("组织建设", "制度流程", "技术工具", "人员能力");
        if (request.getChartName().contains("生命周期")) {
            return coGapAnalysisList.stream().sorted(Comparator.comparing(c -> lifeCycleSort.indexOf(c.getIndicator()))).collect(Collectors.toList());
        } else if (request.getChartName().contains("过程域")) {
            return coGapAnalysisList.stream().sorted(Comparator.comparing(CoGapAnalysis::getIndicator)).collect(Collectors.toList());
        } else if (request.getChartName().contains("能力维度")) {
            return coGapAnalysisList.stream().sorted(Comparator.comparing(c -> dimensionSort.indexOf(c.getIndicator()))).collect(Collectors.toList());
        }
        return coGapAnalysisList;
    }

    /**
     * 查询分析图表符合情况
     *
     * @param dto request
     * @return * @return QueryViewGapAnalysisConformVo
     * @Date 2022/9/16 10:35
     */
    @Override
    public QueryViewGapAnalysisConformVo queryGapConformResult(RequestModel<QueryGapAnalysisChartViewDto> dto) {
        /**
         * ---总对标项数量 = totalNum--->sum
         * ---符合项比例 = countANum / totalNum---->sum/sum
         * ---不符合项比例 = countCNum / totalNum
         * ---部分符合项数量 = countBNum
         * ---部分符合项比例 countBFactor=countBNum / totalNum
         * -------------------------------有效对标率-------------------------
         * ---有效对标比例：effectiveFactor : totalNum-countDNum/totalNum
         * ---不涉及项（不适用项）数量：countDNum
         * ---不涉及项比例：countDNum / totalNum
         * ------------------------------管理能力-----------------------
         * ---是否要跟chartName关联还是直接计算？
         * ---管理能力总数量：totalNum
         * ---符合项比例： totalANum/totalNum
         * ---不符合项比例：totalCNum/totalNum
         */
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        QueryWrapper<CoGapAnalysis> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper.eq("chart_name", dto.getPrivator().getChartName());
        queryWrapper.notIn("indicator", Lists.newArrayList("管理能力", "技术能力"));
        List<CoGapAnalysis> coGapAnalysisList = coGapAnalysisMapper.selectList(queryWrapper);
        //总对标项
        //总对标项数量
        double totalNum = coGapAnalysisList.stream().mapToDouble(CoGapAnalysis::getTotal).sum();
        //符合项数量
        double countANum = coGapAnalysisList.stream().mapToDouble(CoGapAnalysis::getCountA).sum();
        //符合项比例
        BigDecimal countAFactor = totalNum > 0 ? BigDecimal.valueOf(Arith.round(countANum / totalNum * 100, 2)) : BigDecimal.ZERO;
        //不符合项数量
        double countCNum = coGapAnalysisList.stream().mapToDouble(CoGapAnalysis::getCountC).sum();
        //不符合项比例
        BigDecimal countCFactor = totalNum > 0 ? BigDecimal.valueOf(Arith.round(countCNum / totalNum * 100, 2)) : BigDecimal.ZERO;
        //部分符合项数量
        double countBNum = coGapAnalysisList.stream().mapToDouble(CoGapAnalysis::getCountB).sum();
        //部分符合项比例
        BigDecimal countBFactor = totalNum > 0 ? BigDecimal.valueOf(Arith.round(countBNum / totalNum * 100, 2)) : BigDecimal.ZERO;

        //有效对标率
        //不涉及项/不适用项数量
        double countDNum = coGapAnalysisList.stream().mapToDouble(CoGapAnalysis::getCountD).sum();
        //不涉及项比例
        BigDecimal countDFactor = totalNum > 0 ? BigDecimal.valueOf(Arith.round(countDNum / totalNum * 100, 2)) : BigDecimal.ZERO;
        //有效对标率=effectiveFactor : totalNum-countDNum/totalNum
        BigDecimal effectiveFactor = BigDecimal.valueOf(100).subtract(countDFactor);

        //管理能力
        QueryWrapper<CoGapAnalysis> query = new QueryWrapper<>();
        query.eq("operation_id", dto.getPrivator().getOperationId());
        query.eq("chart_name", dto.getPrivator().getChartName()); //饼图不用统计和sum
        query.eq("indicator", "管理能力");
        List<CoGapAnalysis> list = coGapAnalysisMapper.selectList(query);
        //管理能力总数量
        double managementTotalNum = list.stream().mapToDouble(CoGapAnalysis::getTotal).sum();
        //管理能力符合项数量
        double managementCountANum = list.stream().mapToDouble(CoGapAnalysis::getCountA).sum();
        //管理能力符合项比例
        BigDecimal managementCountAFactor = managementTotalNum > 0 ? BigDecimal.valueOf(Arith.round(managementCountANum / managementTotalNum * 100, 2)) : BigDecimal.ZERO;
        //管理能力不符合项数量
        double managementCountCNum = list.stream().mapToDouble(CoGapAnalysis::getCountC).sum();
        //管理能力不符合项比例
        BigDecimal managementCountCFactor = managementTotalNum > 0 ? BigDecimal.valueOf(Arith.round(managementCountCNum / managementTotalNum * 100, 2)) : BigDecimal.ZERO;

        //技术能力
        QueryWrapper<CoGapAnalysis> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper1.eq("chart_name", dto.getPrivator().getChartName()); //饼图不用统计和sum
        queryWrapper1.eq("indicator", "技术能力");
        List<CoGapAnalysis> gapAnalysisList = coGapAnalysisMapper.selectList(queryWrapper1);
        //技术能力总数量
        double technologyTotalNum = gapAnalysisList.stream().mapToDouble(CoGapAnalysis::getTotal).sum();
        //技术能力符合项数量
        double technologyCountANum = gapAnalysisList.stream().mapToDouble(CoGapAnalysis::getCountA).sum();
        //技术能力符合项比例
        BigDecimal technologyCountAFactor = technologyTotalNum > 0 ? BigDecimal.valueOf(Arith.round(technologyCountANum / technologyTotalNum * 100, 2)) : BigDecimal.ZERO;
        //技术能力不符合项数量
        double technologyCountCNum = gapAnalysisList.stream().mapToDouble(CoGapAnalysis::getCountC).sum();
        //技术能力不符合项比例
        BigDecimal technologyCountCFactor = technologyTotalNum > 0 ? BigDecimal.valueOf(Arith.round(technologyCountCNum / technologyTotalNum * 100, 2)) : BigDecimal.ZERO;

        //返回值
        QueryViewGapAnalysisConformVo vo = new QueryViewGapAnalysisConformVo();
        vo.setTotalNum(Double.valueOf(totalNum).intValue());
        vo.setCountAFactor(countAFactor);
        vo.setCountCFactor(countCFactor);
        vo.setCountBNum(Double.valueOf(countBNum).intValue());
        vo.setCountBFactor(countBFactor);
        vo.setEffectiveFactor(effectiveFactor);
        vo.setCountDNum(Double.valueOf(countDNum).intValue());
        vo.setCountDFactor(countDFactor);
        vo.setManagementTotalNum(Double.valueOf(managementTotalNum).intValue());
        vo.setManagementCountAFactor(managementCountAFactor);
        vo.setManagementCountCFactor(managementCountCFactor);
        vo.setTechnologyTotalNum(Double.valueOf(technologyTotalNum).intValue());
        vo.setTechnologyCountAFactor(technologyCountAFactor);
        vo.setTechnologyCountCFactor(technologyCountCFactor);
        return vo;
    }

    /**
     * 基础环境风险分析--查询漏洞比例
     *
     * @param dto request
     * @return * @return QueryViewLoopholeFactorVo
     * @Date 2022/9/19 9:19
     */
    @Override
    public QueryViewLoopholeFactorVo queryLoopholeFactor(RequestModel<CommonDto> dto) {

        QueryWrapper<CoBasicEn> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        List<CoBasicEn> coBasicEns = coBasicEnMapper.selectList(queryWrapper);
        //漏洞总数
        int total = coBasicEns.size();
        if (total == 0) {
            return QueryViewLoopholeFactorVo.builder()
                    .total(0)
                    .highestFactor(BigDecimal.ZERO)
                    .highFactor(BigDecimal.ZERO)
                    .mediumFactor(BigDecimal.ZERO)
                    .lowFactor(BigDecimal.ZERO)
                    .highestNum(0)
                    .highNum(0)
                    .mediumNum(0)
                    .lowNum(0)
                    .systemLoopholeFactor(BigDecimal.ZERO)
                    .commonLoopholeFactor(BigDecimal.ZERO)
                    .webLoopholeFactor(BigDecimal.ZERO)
                    .build();
        }
        //极高危险漏洞
        long highestNum = coBasicEns.stream().filter(p -> DangerLevelEnum.isHighest(p.getDangerLevel())).count();
        //极高危险漏洞占比
        BigDecimal highestFactor = BigDecimal.valueOf(NumberUtil.div(highestNum * 100, total, 2));
        //高危险漏洞
        long highNum = coBasicEns.stream().filter(p -> DangerLevelEnum.isHigh(p.getDangerLevel())).count();
        BigDecimal highFactor = BigDecimal.valueOf(NumberUtil.div(highNum * 100, total, 2));
        //中危险漏洞
        long mediumNum = coBasicEns.stream().filter(p -> DangerLevelEnum.isMedium(p.getDangerLevel())).count();
        BigDecimal mediumFactor = BigDecimal.valueOf(NumberUtil.div(mediumNum * 100, total, 2));
        //低危险漏洞
        long lowNum = coBasicEns.stream().filter(p -> DangerLevelEnum.isLow(p.getDangerLevel())).count();
        BigDecimal lowFactor = BigDecimal.valueOf(NumberUtil.div(lowNum * 100, total, 2));

        //系统漏洞
        long systemLoopholeNum = coBasicEns.stream().filter(p -> p.getLoopholeType().startsWith(LoopholeTypeEnum.XTLD.getInfo())).count();
        BigDecimal systemLoopholeFactor = BigDecimal.valueOf(NumberUtil.div(systemLoopholeNum * 100, total, 2));
        //通用漏洞
        long commonNum = coBasicEns.stream().filter(p -> p.getLoopholeType().startsWith(LoopholeTypeEnum.TYLD.getInfo())).count();
        BigDecimal commonLoopholeFactor = BigDecimal.valueOf(NumberUtil.div(commonNum * 100, total,  2));
        //WEB漏洞
        long webNum = coBasicEns.stream().filter(p -> p.getLoopholeType().startsWith(LoopholeTypeEnum.WEBLD.getInfo())).count();
        BigDecimal webLoopholeFactor = BigDecimal.valueOf(NumberUtil.div(webNum * 100, total, 2));

        return QueryViewLoopholeFactorVo.builder()
                .total(total)
                .highestFactor(highestFactor)
                .highFactor(highFactor)
                .mediumFactor(mediumFactor)
                .lowFactor(lowFactor)
                .highestNum((int)highestNum)
                .highNum((int)highNum)
                .mediumNum((int)mediumNum)
                .lowNum((int)lowNum)
                .systemLoopholeFactor(systemLoopholeFactor)
                .commonLoopholeFactor(commonLoopholeFactor)
                .webLoopholeFactor(webLoopholeFactor)
                .build();
    }

    /**
     * 基础环境风险分析--按数据库分析结果
     *
     * @param dto request
     * @return * @return ResponseApi<QueryViewLoopholeByDatabaseVo>
     * @Date 2022/9/19 9:17
     */
    @Override
    public List<QueryViewLoopholeByDatabaseVo> queryLoopholeByDatabase(RequestModel<CommonDto> dto) {

        QueryWrapper<CoBasicEn> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        Map<String, List<CoBasicEn>> basicMap = coBasicEnMapper.selectList(queryWrapper).stream().collect(Collectors.groupingBy(
                en -> StrUtil.join(StrUtil.DASHED, en.getIp(), en.getDbType())));

        return basicMap.values().stream().map(value -> {
            CoBasicEn coBasicEn = value.get(0);
            // 漏洞总数
            long size = value.size();
            long highest = value.stream().filter(c -> DangerLevelEnum.isHighest(c.getDangerLevel())).count();
            long high = value.stream().filter(c -> DangerLevelEnum.isHigh(c.getDangerLevel())).count();
            long medium = value.stream().filter(c -> DangerLevelEnum.isMedium(c.getDangerLevel())).count();
            long low = value.stream().filter(c -> DangerLevelEnum.isLow(c.getDangerLevel())).count();
            return QueryViewLoopholeByDatabaseVo
                    .builder()
                    .ip(coBasicEn.getIp())
                    .dbType(coBasicEn.getDbType())
                    .highestFactor(BigDecimal.valueOf(NumberUtil.div(highest * 100, size, 2)))
                    .highFactor(BigDecimal.valueOf(NumberUtil.div(high * 100, size, 2)))
                    .mediumFactor(BigDecimal.valueOf(NumberUtil.div(medium * 100, size, 2)))
                    .lowFactor(BigDecimal.valueOf(NumberUtil.div(low * 100, size, 2)))
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 基础环境风险分析--数据库主机漏洞分析结果
     *
     * @param dto request
     * @return * @return ResponseApi<QueryViewLoopholeByDatabaseVo>
     * @Date 2022/9/19 9:17
     */
    @Override
    public List<QueryViewLoopholeBySystemVo> queryLoopholeBySystem(RequestModel<CommonDto> dto) {

        //分组查询
        QueryWrapper<CoBasicEn> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("ip, operating_system_type as \"operatingSystemType\"");
        queryWrapper.groupBy("ip, operating_system_type");
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper.eq("label_id", dto.getPrivator().getLabelId());
        List<Map<String, Object>> mapList = coBasicEnMapper.selectMaps(queryWrapper);

        List<QueryViewLoopholeBySystemVo> voList = new ArrayList<>();

        mapList.stream().forEach(p -> {
            //entrySet转map
            Map<String, Object> map = p.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (k1, k2) -> k2));
            //map转实体
            CoBasicEn coBasicEn = JSON.parseObject(JSON.toJSONString(map), CoBasicEn.class);
            //根据分组结果作为条件遍历查询
            QueryWrapper<CoBasicEn> query = new QueryWrapper<>();
            query.eq("operation_id", dto.getPrivator().getOperationId());
            query.eq("label_id", dto.getPrivator().getLabelId());
            query.eq("ip", coBasicEn.getIp());
            query.eq("operating_system_type", coBasicEn.getOperatingSystemType());
            List<CoBasicEn> coBasicEnList = coBasicEnMapper.selectList(query);
            //漏洞总数
            Double total = Double.valueOf(coBasicEnList.size());
            //极高危险漏洞数
            List<CoBasicEn> highestList = coBasicEnList.stream().filter(c -> DangerLevelEnum.isHighest(c.getDangerLevel())).collect(Collectors.toList());
            Double highestNum = Double.valueOf(highestList.size());
            //高危险漏洞数
            List<CoBasicEn> highList = coBasicEnList.stream().filter(c -> DangerLevelEnum.isHigh(c.getDangerLevel())).collect(Collectors.toList());
            Double highNum = Double.valueOf(highList.size());
            //中危险漏洞数
            List<CoBasicEn> mediumList = coBasicEnList.stream().filter(c -> DangerLevelEnum.isMedium(c.getDangerLevel())).collect(Collectors.toList());
            Double mediumNum = Double.valueOf(mediumList.size());
            //低危险漏洞数
            List<CoBasicEn> lowList = coBasicEnList.stream().filter(c -> DangerLevelEnum.isLow(c.getDangerLevel())).collect(Collectors.toList());
            Double lowNum = Double.valueOf(lowList.size());

            //填充返回数据
            QueryViewLoopholeBySystemVo vo = new QueryViewLoopholeBySystemVo();
            vo.setIp(coBasicEn.getIp());
            vo.setOperatingSystemType(coBasicEn.getOperatingSystemType());
            vo.setHighestNum(highestNum.intValue());
            vo.setHighNum(highNum.intValue());
            vo.setMediumNum(mediumNum.intValue());
            vo.setLowNum(lowNum.intValue());
            vo.setTotalNum(total.intValue());
            voList.add(vo);
        });
        return voList;
    }

    /**
     * 合规风险分析--比例
     *
     * @param dto request
     * @return * @return QueryViewLegalFactorVo
     * @Date 2022/9/20 17:11
     */
    @Override
    public QueryViewLegalResultVo queryLegalProportion(RequestModel<OperationIdDto> dto) {
        /**
         * -------总对标文件--------------
         * 总对标(法律)文件：lawTotalNum = distinct lawName
         * 法律：article_code包含NL 统计 distinct lawName
         * 法规：article_code包含GL、DR、LL、LR、IR、RL 统计 distinct lawName
         * 标准：article_code包含 GS、NS、IS、LS、TS 统计 distinct lawName
         *--------总对标项----------------
         * 注：按item_num分组统计
         * 总对标项：itemTotalNum = group by item_num
         * 合格率：（“完全符合项”* 1+”部分符合项“*0.5）/（”总对标项“）*100%
         * -------完全符合项--------------
         * 注：按item_num分组统计
         * 完全符合项：aNum = 分组结果 = “完全符合”
         * 完全符合项占比 = aNum / itemTotalNum *100%
         * -------部分符合项--------------
         * 注：按item_num分组统计
         * -------不符合项---------
         * 注：按item_num分组统计
         * -------不涉及项（不适用）--------
         *
         */
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        QueryLegalDTO retrieveLegal = new QueryLegalDTO();
        retrieveLegal.setOperationId(dto.getPrivator().getOperationId());
        retrieveLegal.setLabelId(LabelEnum.HFHG.getCode());
        retrieveLegal.setLawName(LegalModelEnum.ZHMB.getInfo());
        QueryViewLegalResultVo vo = this.getLegalResultList(retrieveLegal);
        return vo;
    }

    /**
     * 合规风险分析--合规结果一览
     *
     * @param dto request
     * @return * @return QueryViewLegalResultVo
     * @Date 2022/9/21 15:02
     */
    @Override
    public List<QueryViewLegalResultVo> queryLegalResult(RequestModel<OperationIdDto> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        List<QueryViewLegalResultVo> voList = new ArrayList<>();
        List<String> lawNames = complianceTemplateMapper.selectFileNameEnableTemp(dto.getPrivator().getOperationId());
        for (String lawName : lawNames) {
            QueryLegalDTO retrieveLegal = new QueryLegalDTO();
            retrieveLegal.setOperationId(dto.getPrivator().getOperationId());
            retrieveLegal.setLabelId(LabelEnum.HFHG.getCode());
            retrieveLegal.setLawName(lawName);
            QueryViewLegalResultVo vo = this.getLegalResultList(retrieveLegal);
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取合规结果
     *
     * @param retrieveLegal request
     * @return * @return QueryViewLegalResultVo
     * @Date 2022/9/21 15:47
     */
    public QueryViewLegalResultVo getLegalResultList(QueryLegalDTO retrieveLegal) {

        List<CoLegal> coLegalList = coLegalMapper.queryLegalList(retrieveLegal);
        List<CoLegal> lawList = coLegalList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CoLegal::getLawName))),
                ArrayList::new
        ));
        //总对标文件数量
        List<CoLegal> lawNumList = lawList.stream().filter(p -> StringUtils.isNotBlank(p.getArticleCode())).collect(Collectors.toList());
        Integer lawTotalNum = lawNumList.size();
        //法律文件数量
        List<CoLegal> lawDocList = lawList.stream().filter(p -> p.getArticleCode().contains("NL")).collect(Collectors.toList());
        Integer lawDocNum = lawDocList.size();
        //法规文件数量
        List<CoLegal> ruleDocList = lawList.stream().filter(p -> p.getArticleCode().contains("GL") || p.getArticleCode().contains("DR") || p.getArticleCode().contains("LL") || p.getArticleCode().contains("IR") || p.getArticleCode().contains("LR")).collect(Collectors.toList());
        Integer ruleDocNum = ruleDocList.size();
        //标准文件数量
        List<CoLegal> standardDocList = lawList.stream().filter(p -> p.getArticleCode().contains("GS") || p.getArticleCode().contains("NS") || p.getArticleCode().contains("IS") || p.getArticleCode().contains("LS") || p.getArticleCode().contains("TS")).collect(Collectors.toList());
        Integer standardDocNum = standardDocList.size();

        //法律法规名称
        String lawName = "";
        if (LegalModelEnum.ZHMB.getInfo().equals(retrieveLegal.getLawName())) {
            lawName = LegalModelEnum.ZHMB.getInfo();
        } else {
            lawName = CollUtil.isEmpty(coLegalList) ? retrieveLegal.getLawName() : coLegalList.get(0).getLawName();
        }
        //总对标项数量(合计对标条文数量)
        double itemTotalNum = coLegalList.size();

        //完全符合项数量
        List<CoLegal> countAList = coLegalList.stream().filter(p -> OptEnum.A.getInfo().equals(p.getResult())).collect(Collectors.toList());
        double countANum = countAList.size();
        //完全符合项占比
        BigDecimal countAProportion = itemTotalNum > 0 ? BigDecimal.valueOf(Arith.round(countANum / itemTotalNum * 100, 2)) : BigDecimal.ZERO;
        //部分符合项数量
        List<CoLegal> countBList = coLegalList.stream().filter(p -> OptEnum.B.getInfo().equals(p.getResult())).collect(Collectors.toList());
        double countBNum = countBList.size();
        //部分符合项占比
        BigDecimal countBProportion = itemTotalNum > 0 ? BigDecimal.valueOf(Arith.round(countBNum / itemTotalNum * 100, 2)) : BigDecimal.ZERO;
        //合格率（“完全符合项”* 1+”部分符合项“*0.5）/（”总对标项“）*100%
        BigDecimal qualifiedProportion = itemTotalNum > 0 ? BigDecimal.valueOf(Arith.round((countANum * 1 + countBNum * 0.5) / itemTotalNum * 100, 2)) : BigDecimal.ZERO;
        //不符合项数量
        List<CoLegal> countCList = coLegalList.stream().filter(p -> OptEnum.C.getInfo().equals(p.getResult())).collect(Collectors.toList());
        double countCNum = countCList.size();
        //不符合项占比
        BigDecimal countCProportion = itemTotalNum > 0 ? BigDecimal.valueOf(Arith.round(countCNum / itemTotalNum * 100, 2)) : BigDecimal.ZERO;
        //不涉及项数量
        List<CoLegal> countDList = coLegalList.stream().filter(p -> OptEnum.D.getInfo().equals(p.getResult())).collect(Collectors.toList());
        double countDNum = countDList.size();
        //不涉及项占比
        BigDecimal countDProportion = itemTotalNum > 0 ? BigDecimal.valueOf(Arith.round(countDNum / itemTotalNum * 100, 2)) : BigDecimal.ZERO;

        QueryViewLegalResultVo vo = new QueryViewLegalResultVo();
        vo.setLawTotalNum(lawTotalNum);
        vo.setLawDocNum(lawDocNum);
        vo.setRuleDocNum(ruleDocNum);
        vo.setStandardDocNum(standardDocNum);
        vo.setLawName(lawName);
        vo.setItemTotalNum((int) itemTotalNum);
        vo.setCountANum((int) countANum);
        vo.setCountAProportion(countAProportion);
        vo.setCountBNum((int) countBNum);
        vo.setCountBProportion(countBProportion);
        vo.setQualifiedProportion(qualifiedProportion);
        vo.setCountCNum((int) countCNum);
        vo.setCountCProportion(countCProportion);
        vo.setCountDNum((int) countDNum);
        vo.setCountDProportion(countDProportion);
        return vo;
    }

    /**
     * 业务系统风险一览
     *
     * @param dto request
     * @return * @return QueryViewLifecycleSystemResultVo
     * @Date 2022/9/23 16:16
     */
    @Override
    public QueryViewLifecycleSystemResultVo queryLifecycleResult(RequestModel<OperationIdDto> dto) {

        QueryWrapper<CoLifecycle> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper.eq("label_id", LabelEnum.SMZQ.getCode());
        List<CoLifecycle> coLifecycleList = coLifecycleMapper.selectList(queryWrapper);
        List<String> systemList = coLifecycleList.stream().map(CoLifecycle::getBusSystem).distinct().collect(Collectors.toList());

        List<QueryViewLifecycleSystemResultVo> voList = new ArrayList<>();
        systemList.forEach(p -> {
            QueryViewLifecycleSystemResultVo result = this.getLifecycleSingleResult(dto.getPrivator().getOperationId(), p);
            voList.add(result);
        });
        int highRiskAssetNum = voList.stream().mapToInt(QueryViewLifecycleSystemResultVo::getHighRiskAssetNum).sum();
        int mediumRiskAssetNum = voList.stream().mapToInt(QueryViewLifecycleSystemResultVo::getMediumRiskAssetNum).sum();
        int lowRiskAssetNum = voList.stream().mapToInt(QueryViewLifecycleSystemResultVo::getLowRiskAssetNum).sum();
        int moreThanMediumNum = voList.stream().mapToInt(QueryViewLifecycleSystemResultVo::getMoreThanMediumNum).sum();
        double riskFactorAvg = voList.stream().mapToDouble(p -> p.getSingleSystemFactor().doubleValue()).sum() / systemList.size();

        QueryViewLifecycleSystemResultVo vo = new QueryViewLifecycleSystemResultVo();
        vo.setRiskFactorAvg(BigDecimal.valueOf(Arith.round(riskFactorAvg, 2)));
        vo.setHighRiskAssetNum(highRiskAssetNum);
        vo.setMediumRiskAssetNum(mediumRiskAssetNum);
        vo.setMoreThanMediumNum(moreThanMediumNum);
        vo.setLowRiskAssetNum(lowRiskAssetNum);
        return vo;
    }

    /**
     * 业务系统风险排序
     *
     * @param dto request
     * @return * @return List<QueryViewLifecycleSystemResultVo>
     * @Date 2022/9/23 19:00
     */
    @Override
    public List<QueryViewLifecycleSystemResultVo> queryLifecycleResultList(RequestModel<OperationIdDto> dto) {

        QueryWrapper<CoLifecycle> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper.eq("label_id", LabelEnum.SMZQ.getCode());
        List<CoLifecycle> coLifecycleList = coLifecycleMapper.selectList(queryWrapper);
        List<String> systemList = coLifecycleList.stream().map(p -> p.getBusSystem()).distinct().collect(Collectors.toList());

        List<QueryViewLifecycleSystemResultVo> voList = new ArrayList<>();
        systemList.stream().forEach(p -> {
            QueryViewLifecycleSystemResultVo result = this.getLifecycleSingleResult(dto.getPrivator().getOperationId(), p);
            voList.add(result);
        });
        return voList;
    }

    /**
     * 获取单业务系统风险结果
     *
     * @param operationId request
     * @return * @return QueryViewLifecycleSystemResultVo
     * @Date 2022/9/23 18:58
     */
    @Override
    public QueryViewLifecycleSystemResultVo getLifecycleSingleResult(String operationId, String system) {
        /**
         * 单资产风险值：单个作业的单个资产的从数据采集到通用阶段的最大值（风险系数）
         * 单资产平均风险系数：单个作业的单个资产的从数据采集到通用阶段的风险系数平均值
         * 单业务系统风险系数：所有单资产平均风险系数的平均值
         * 单业务系统风险系数：同个业务系统所有单资产平均风险系数的平均值
         * 综合风险值：统计所有“单个业务系统风险系数”的平均值
         */
        //入参校验
        QueryWrapper<CoLifecycle> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        queryWrapper.eq("label_id", LabelEnum.SMZQ.getCode());
        queryWrapper.eq("bus_system", system);
        List<CoLifecycle> coLifecycleList = coLifecycleMapper.selectList(queryWrapper);
        //查询客户的最高敏感等级
        QueryWrapper<CoConstant> query = new QueryWrapper<>();
        query.eq("customer_id", coOperationMapper.selectCustomIdByOperationId(operationId));
        CoConstant coConstant = coConstantMapper.selectOne(query);
        List<Integer> riskGradeList = new ArrayList<>();
        List<BigDecimal> factorList = new ArrayList<>();
        List<Double> riskFactorList = new ArrayList<>();
        List<String> riskLevelList = new ArrayList<>();
        coLifecycleList.forEach(p -> {
            riskGradeList.add(p.getDataCollectionRiskGrade());
            riskGradeList.add(p.getDataTransmissionRiskGrade());
            riskGradeList.add(p.getDataStorageRiskGrade());
            riskGradeList.add(p.getDataProcessingRiskGrade());
            riskGradeList.add(p.getDataExchangeRiskGrade());
            riskGradeList.add(p.getDataDestructionRiskGrade());
            riskGradeList.add(p.getCommonRiskGrade());
            riskGradeList.sort(Comparator.reverseOrder());

            factorList.add(p.getDataCollectionRiskFactor());
            factorList.add(p.getDataTransmissionRiskFactor());
            factorList.add(p.getDataStorageRiskFactor());
            factorList.add(p.getDataProcessingRiskFactor());
            factorList.add(p.getDataExchangeRiskFactor());
            factorList.add(p.getDataDestructionRiskFactor());
            factorList.add(p.getCommonRiskFactor());
            factorList.sort(Comparator.reverseOrder());

            //风险等级汇总统计
            IntSummaryStatistics gradeStatistics = riskGradeList.stream().mapToInt(Number::intValue).summaryStatistics();
            //单资产风险值：单个作业的单个资产的从数据采集到通用阶段的最大值（风险等级）
            int singleRiskValue = gradeStatistics.getMax();
            //风险系数汇总统计
            DoubleSummaryStatistics factorStatistics = factorList.stream().mapToDouble(Number::doubleValue).summaryStatistics();
            //单资产平均风险系数：单个作业的单个资产的从数据采集到通用阶段的风险系数平均值
            Double singleRiskFactor = factorStatistics.getAverage();
            riskFactorList.add(singleRiskFactor);
            //高资产风险数量：先统计单资产风险值在矩阵中的风险等级
            String riskLevel = DcasUtil.getMatrixRisk(singleRiskValue, coConstant.getHighestSensitiveLevel());
            riskLevelList.add(riskLevel);
        });
        int riskTotal = riskLevelList.size();
        //高风险资产数量
        List<String> highRiskList = riskLevelList.stream().filter(p -> RiskLevelEnum.HIGH.getInfo().equals(p)).collect(Collectors.toList());
        int highRiskNum = highRiskList.size();
        List<String> mediumRiskList = riskLevelList.stream().filter(p -> RiskLevelEnum.MEDIUM.getInfo().equals(p)).collect(Collectors.toList());
        int mediumRiskNum = mediumRiskList.size();
        List<String> lowRiskList = riskLevelList.stream().filter(p -> RiskLevelEnum.LOW.getInfo().equals(p)).collect(Collectors.toList());
        int lowRiskNum = lowRiskList.size();
        //中风险以上资产数量
        int moreThanMediumNum = highRiskNum + mediumRiskNum;

        //汇总统计
        DoubleSummaryStatistics statistic = riskFactorList.stream().mapToDouble(Number::doubleValue).summaryStatistics();
        //单业务系统风险系数：同个业务系统所有单资产平均风险系数的平均值
        Double singleSystemFactor = statistic.getAverage();

        //返回值
        QueryViewLifecycleSystemResultVo vo = new QueryViewLifecycleSystemResultVo();
        vo.setBusSystem(system);
        vo.setRiskTotal(riskTotal);
        vo.setHighRiskAssetNum(highRiskNum);
        vo.setMediumRiskAssetNum(mediumRiskNum);
        vo.setLowRiskAssetNum(lowRiskNum);
        vo.setMoreThanMediumNum(moreThanMediumNum);
        vo.setSingleSystemFactor(BigDecimal.valueOf(Arith.round(singleSystemFactor, 2)));
        return vo;
    }

    /**
     * 生命周期风险分析
     *
     * @param dto request
     * @return * @return QueryViewLifecycleAnalysisVo
     * @Date 2022/9/25 21:40
     */
    @Override
    public QueryViewLifecycleAnalysisVo queryLifecycleAnalysis(RequestModel<QueryViewLifecycleAnalysisDto> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());

        QueryWrapper<CoLifecycle> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper.eq("label_id", LabelEnum.SMZQ.getCode());
        if ("汇总分析".equals(dto.getPrivator().getBusSystem())) {
            queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
            queryWrapper.eq("label_id", LabelEnum.SMZQ.getCode());
        } else {
            queryWrapper.eq("bus_system", dto.getPrivator().getBusSystem());
        }
        List<CoLifecycle> coLifecycleList = coLifecycleMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(coLifecycleList)) {
            return new QueryViewLifecycleAnalysisVo();
        }

        OptionalDouble collectionRiskFactor = coLifecycleList.stream().mapToDouble(p -> p.getDataCollectionRiskFactor().doubleValue()).average();
        OptionalDouble transmissionRiskFactor = coLifecycleList.stream().mapToDouble(p -> p.getDataTransmissionRiskFactor().doubleValue()).average();
        OptionalDouble storageRiskFactor = coLifecycleList.stream().mapToDouble(p -> p.getDataStorageRiskFactor().doubleValue()).average();
        OptionalDouble processingRiskFactor = coLifecycleList.stream().mapToDouble(p -> p.getDataProcessingRiskFactor().doubleValue()).average();
        OptionalDouble exchangeRiskFactor = coLifecycleList.stream().mapToDouble(p -> p.getDataExchangeRiskFactor().doubleValue()).average();
        OptionalDouble destructionRiskFactor = coLifecycleList.stream().mapToDouble(p -> p.getDataDestructionRiskFactor().doubleValue()).average();
        OptionalDouble commonRiskFactor = coLifecycleList.stream().mapToDouble(p -> p.getCommonRiskFactor().doubleValue()).average();

        OptionalDouble collectionCapacityFactor = coLifecycleList.stream().mapToDouble(p -> p.getCollectionCapacityFactor().doubleValue()).average();
        OptionalDouble transmissionCapacityFactor = coLifecycleList.stream().mapToDouble(p -> p.getTransmissionCapacityFactor().doubleValue()).average();
        OptionalDouble storageCapacityFactor = coLifecycleList.stream().mapToDouble(p -> p.getStorageCapacityFactor().doubleValue()).average();
        OptionalDouble processingCapacityFactor = coLifecycleList.stream().mapToDouble(p -> p.getProcessingCapacityFactor().doubleValue()).average();
        OptionalDouble exchangeCapacityFactor = coLifecycleList.stream().mapToDouble(p -> p.getExchangeCapacityFactor().doubleValue()).average();
        OptionalDouble destructionCapacityFactor = coLifecycleList.stream().mapToDouble(p -> p.getDestructionCapacityFactor().doubleValue()).average();
        OptionalDouble commonCapacityFactor = coLifecycleList.stream().mapToDouble(p -> p.getCommonCapacityFactor().doubleValue()).average();

        QueryViewLifecycleAnalysisVo vo = new QueryViewLifecycleAnalysisVo();
        vo.setCollectionRiskFactor(BigDecimal.valueOf(Arith.round(collectionRiskFactor.getAsDouble(), 2)));
        vo.setTransmissionRiskFactor(BigDecimal.valueOf(Arith.round(transmissionRiskFactor.getAsDouble(), 2)));
        vo.setStorageRiskFactor(BigDecimal.valueOf(Arith.round(storageRiskFactor.getAsDouble(), 2)));
        vo.setProcessingRiskFactor(BigDecimal.valueOf(Arith.round(processingRiskFactor.getAsDouble(), 2)));
        vo.setExchangeRiskFactor(BigDecimal.valueOf(Arith.round(exchangeRiskFactor.getAsDouble(), 2)));
        vo.setDestructionRiskFactor(BigDecimal.valueOf(Arith.round(destructionRiskFactor.getAsDouble(), 2)));
        vo.setCommonRiskFactor(BigDecimal.valueOf(Arith.round(commonRiskFactor.getAsDouble(), 2)));
        vo.setCollectionCapacityFactor(BigDecimal.valueOf(Arith.round(collectionCapacityFactor.getAsDouble(), 2)));
        vo.setTransmissionCapacityFactor(BigDecimal.valueOf(Arith.round(transmissionCapacityFactor.getAsDouble(), 2)));
        vo.setStorageCapacityFactor(BigDecimal.valueOf(Arith.round(storageCapacityFactor.getAsDouble(), 2)));
        vo.setProcessingCapacityFactor(BigDecimal.valueOf(Arith.round(processingCapacityFactor.getAsDouble(), 2)));
        vo.setExchangeCapacityFactor(BigDecimal.valueOf(Arith.round(exchangeCapacityFactor.getAsDouble(), 2)));
        vo.setDestructionCapacityFactor(BigDecimal.valueOf(Arith.round(destructionCapacityFactor.getAsDouble(), 2)));
        vo.setCommonCapacityFactor(BigDecimal.valueOf(Arith.round(commonCapacityFactor.getAsDouble(), 2)));
        return vo;
    }

    /**
     * 报告生成下载
     *
     * @param response request
     * @Date 2022/7/14 17:10
     */
    @Override
    public void exportWord(HttpServletResponse response, ExportWordDto dto) throws Exception {
        CoOperation coOperation = coOperationMapper.selectById(dto.getOperationId());
        if (coOperation.getIsSpec() != null && coOperation.getIsSpec()){
            reportMainEntry.entry(response, dto, coOperation.getSpecId(), ReportGroupEnum.SPEC_WORD);
        } else {
            reportMainEntry.entry(response, dto, null, ReportGroupEnum.ASSESSMENT_WORD);
        }
    }

    @Override
    @DataScope(deptAlias = "t", userAlias = "t", extra = "job")
    public CustomerDetailVO customerDetail(QueryCustomerDetailsViewDto dto) {
        CustomerDetailVO vo = new CustomerDetailVO();
        QueryWrapper<CoCustomer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_id", dto.getCustomerId());
        CoCustomer coCustomer = coCustomerMapper.selectOne(queryWrapper);
        if (Objects.isNull(coCustomer)) {
            return vo;
        }
        vo.setCustomerName(coCustomer.getCustomerName());
        vo.setCustomerDirector(coCustomer.getCustomerDirector());
        SSOAccountDTO user = SecurityUtils.getLoginUser();
        List<ProjectWorkVO> projectWorks = coProjectMapper.projectWorksWithCostomerId(dto);
        // 评估作业
        List<ProjectWorkVO> accessWorks = projectWorks.stream().filter(p -> p.getType() == 1 && Objects.nonNull(p.getStatus())).collect(Collectors.toList());
        // 检查作业
        List<ProjectWorkVO> reviewWorks = projectWorks.stream().filter(p -> p.getType() == 2 && Objects.nonNull(p.getStatus())).collect(Collectors.toList());
        vo.setAccess(coWorkbenchViewService.buildAccessWork(accessWorks, user));
        vo.setReview(coWorkbenchViewService.buildReviewWork(reviewWorks, user));
        vo.setProject(buildProjectView(projectWorks, user));

        List<ProjectWorkVO> working = projectWorks.stream().filter(p ->
                coProjectService.hasPermission(user, p, WorkPermissionType.EXECUTE)).collect(Collectors.toList());
        vo.setOperationList(coWorkbenchViewService.buildOperationView(working, false));
        vo.setIndustry(working.stream().flatMap(w -> StrUtil.split(w.getIndustry(), StrUtil.COMMA).stream()).collect(Collectors.toSet()));
        vo.setRegion(working.stream().flatMap(w -> StrUtil.split(w.getRegion(), ";").stream()).collect(Collectors.toSet()));
        return vo;
    }

    private QueryOperationStatusVo.WorkOverview buildProjectView(List<ProjectWorkVO> projectWork, SSOAccountDTO user) {
        QueryOperationStatusVo.WorkOverview vo = new QueryOperationStatusVo.WorkOverview();
        vo.setName("项目详情");
        Map<String, List<ProjectWorkVO>> projectWorkGroup = projectWork.stream().collect(Collectors.groupingBy(ProjectWorkVO::getProjectId));
        AtomicInteger onWaiting = new AtomicInteger(0);
        AtomicInteger onGoing = new AtomicInteger(0);
        AtomicInteger completed = new AtomicInteger(0);
        projectWorkGroup.forEach((projectId, works) -> {
            List<ProjectWorkVO> accessWorks = works.stream().filter(p -> p.getType() == 1 && Objects.nonNull(p.getStatus())).collect(Collectors.toList());
            List<ProjectWorkVO> reviewWorks = works.stream().filter(p -> p.getType() == 2 && Objects.nonNull(p.getStatus())).collect(Collectors.toList());
            // 项目没有关联作业，项目待启动
            if (CollUtil.isEmpty(accessWorks) && CollUtil.isEmpty(reviewWorks)) {
                onWaiting.incrementAndGet();
            }
            // 存在正在执行中的评估作业或者检查作业，项目为执行中
            else if ((accessWorks.stream().anyMatch(w -> w.getStatus() == 1 && coProjectService.hasPermission(user, w, WorkPermissionType.EXECUTE))
                    || reviewWorks.stream().anyMatch(w -> w.getStatus() == 2 && coProjectService.hasPermission(user, w, WorkPermissionType.EXECUTE)))) {
                onGoing.incrementAndGet();
            }
            // 项目下的所有作业都为已结项，项目为已结项
            else if (accessWorks.stream().allMatch(w -> w.getStatus() == 3)
                    && reviewWorks.stream().allMatch(w -> w.getStatus() == 3)) {
                completed.incrementAndGet();
            }
        });
        vo.setOnWaiting(onWaiting.get());
        vo.setOnGoing(onGoing.get());
        vo.setCompleted(completed.get());
        return vo;
    }

    @Override
    public RiskAnalysisReportVO queryRiskAnalysisReport(RequestModel<QueryViewLifecycleAnalysisDto> dto) {
        String operationId = dto.getPrivator().getOperationId();
        return iRiskAnalysisService.queryRiskAnalysisReport(operationId, dto.getPrivator().isForceUpdate());
    }

    @Override
    public void exportReportAttachment(HttpServletResponse response, ExportWordDto dto) throws Exception {
        reportMainEntry.entry(response, dto, null, ReportGroupEnum.ASSET_LIST);
    }

    /**
     * 能力差距分析--查询图表名称
     *
     * @param dto
     */
    private List<String> queryChartKey(OperationIdDto dto) {
        //调用图表模板查询
        //        List<QueryChartModelVo> voList = coGapAnalysisService.queryChartModel(dto);
        GapAnalysisQuery gapAnalysisQuery = new GapAnalysisQuery();
        gapAnalysisQuery.setOperationId(dto.getOperationId());
        gapAnalysisQuery.setLabelId(LabelEnum.CJFX.getCode());
        List<QueryGapAnalysisVo> voList = coGapAnalysisMapper.queryByChartId(gapAnalysisQuery);
        return voList.stream().map(QueryGapAnalysisVo::getKey).collect(Collectors.toList());
    }

    @Override
    public QueryViewGapAnalysisConformVo queryGapChartResult(String operationId) {
        OperationIdDto dto = new OperationIdDto();
        dto.setOperationId(operationId);
        List<String> keyList = queryChartKey(dto);
        QueryViewGapAnalysisConformVo gapAnalysisConformVo = queryTotalGapConformResult(operationId, keyList);
        gapAnalysisConformVo.setCoGapAnalysisMap(queryGapAnalysisChartByOperationId(operationId,keyList));
        return gapAnalysisConformVo;
    }

    private Map<String, List<CoGapAnalysis>> queryGapAnalysisChartByOperationId(String operationId,
        List<String> chartNameList) {
        Map<String, List<CoGapAnalysis>> map = new HashMap<>(16);
        chartNameList.forEach(s -> {
            List<String> strList = StrUtil.split(s, StrUtil.DASHED);
            RequestModel<QueryGapAnalysisChartViewDto> requestModel = new RequestModel<>();
            QueryGapAnalysisChartViewDto queryGapAnalysisChartViewDto = new QueryGapAnalysisChartViewDto();
            queryGapAnalysisChartViewDto.setOperationId(operationId);
            queryGapAnalysisChartViewDto.setChartName(strList.get(1));
            requestModel.setPrivator(queryGapAnalysisChartViewDto);
            List<CoGapAnalysis> list = queryGapAnalysisChart(requestModel);
            if (CollUtil.isEmpty(list)){
                return;
            }
            map.put(queryGapAnalysisChartViewDto.getChartName(), list);
        });
        return map;
    }

    private QueryViewGapAnalysisConformVo queryTotalGapConformResult(String operationId, List<String> chartNameList) {
        /**
         * ---总对标项数量 = totalNum--->sum
         * ---符合项比例 = countANum / totalNum---->sum/sum
         * ---不符合项比例 = countCNum / totalNum
         * ---部分符合项数量 = countBNum
         * ---部分符合项比例 countBFactor=countBNum / totalNum
         * -------------------------------有效对标率-------------------------
         * ---有效对标比例：effectiveFactor : totalNum-countDNum/totalNum
         * ---不涉及项（不适用项）数量：countDNum
         * ---不涉及项比例：countDNum / totalNum
         * ------------------------------管理能力-----------------------
         * ---是否要跟chartName关联还是直接计算？
         * ---管理能力总数量：totalNum
         * ---符合项比例： totalANum/totalNum
         * ---不符合项比例：totalCNum/totalNum
         */
        //总对标项
        //总对标项数量
        int totalNum = 0;
        //符合项数量
        int countANum = 0;
        //不符合项数量
        int countCNum = 0;
        //部分符合项数量
        int countBNum = 0;
        //有效对标率
        //不涉及项/不适用项数量
        int countDNum = 0;
        //管理能力总数量
        int managementTotalNum = 0;
        //管理能力符合项数量
        int managementCountANum = 0;
        //管理能力不符合项数量
        int managementCountCNum = 0;
        //技术能力总数量
        int technologyTotalNum = 0;
        //技术能力符合项数量
        int technologyCountANum = 0;
         //技术能力不符合项数量
        int technologyCountCNum = 0;

        List<ResultCountDTO> resultCountList = coVerificationMapper.queryCountByOperationId(operationId);

        // 符合情况
        for (ResultCountDTO resultCountDTO : resultCountList){
            if (OptEnum.A.getInfo().equals(resultCountDTO.getResult())){
                countANum += resultCountDTO.getCount();
            } else if (OptEnum.B.getInfo().equals(resultCountDTO.getResult()) || OptEnum.E.getInfo().equals(resultCountDTO.getResult())) {
                countBNum += resultCountDTO.getCount();
            } else if (OptEnum.D.getInfo().equals(resultCountDTO.getResult())) {
                countDNum += resultCountDTO.getCount();
            } else if (OptEnum.C.getInfo().equals(resultCountDTO.getResult())) {
                countCNum += resultCountDTO.getCount();
            }
            totalNum += resultCountDTO.getCount();
        }

        QueryWrapper<CoGapAnalysis> wrapper = new QueryWrapper<>();
        wrapper.eq("operation_id", operationId);
        wrapper.eq("type", 2);
        wrapper.in("indicator", Lists.newArrayList("管理能力", "技术能力"));
        wrapper.orderByAsc("sort");
        List<CoGapAnalysis> gapAnalysisList = coGapAnalysisMapper.selectList(wrapper);
        for (CoGapAnalysis coGapAnalysis : gapAnalysisList) {
            if ("管理能力".equals(coGapAnalysis.getIndicator())) {
                managementCountANum += coGapAnalysis.getCountA();
                managementCountCNum += coGapAnalysis.getCountC();
                managementTotalNum += coGapAnalysis.getTotal();
            }
            if ("技术能力".equals(coGapAnalysis.getIndicator())) {
                technologyCountANum += coGapAnalysis.getCountA();
                technologyCountCNum += coGapAnalysis.getCountC();
                technologyTotalNum += coGapAnalysis.getTotal();
            }
        }

        //符合项比例
        BigDecimal countAFactor =
            totalNum > 0 ? BigDecimal.valueOf(countANum).divide(BigDecimal.valueOf(totalNum), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)) : BigDecimal.ZERO;
        //不符合项比例
        BigDecimal countCFactor =
            totalNum > 0 ? BigDecimal.valueOf(countCNum).divide(BigDecimal.valueOf(totalNum), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)) : BigDecimal.ZERO;
        //部分符合项比例
        BigDecimal countBFactor =
            totalNum > 0 ? BigDecimal.valueOf(countBNum).divide(BigDecimal.valueOf(totalNum), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)) : BigDecimal.ZERO;
        //不涉及项比例
        BigDecimal countDFactor =
            totalNum > 0 ? BigDecimal.valueOf(countDNum).divide(BigDecimal.valueOf(totalNum), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)) : BigDecimal.ZERO;
        //有效对标率=effectiveFactor : totalNum-countDNum/totalNum
        BigDecimal effectiveFactor = BigDecimal.valueOf(100).subtract(countDFactor);
        //管理能力符合项比例
        BigDecimal managementCountAFactor = managementTotalNum > 0 ? BigDecimal.valueOf(managementCountANum)
            .divide(BigDecimal.valueOf(managementTotalNum), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
            : BigDecimal.ZERO;
        //管理能力不符合项比例
        BigDecimal managementCountCFactor = managementTotalNum > 0 ? BigDecimal.valueOf(managementCountCNum)
            .divide(BigDecimal.valueOf(managementTotalNum), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
            : BigDecimal.ZERO;
        //技术能力不符合项比例
        BigDecimal technologyCountCFactor = technologyTotalNum > 0 ? BigDecimal.valueOf(technologyCountCNum)
            .divide(BigDecimal.valueOf(technologyTotalNum), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
            : BigDecimal.ZERO;
        //技术能力符合项比例
        BigDecimal technologyCountAFactor = technologyTotalNum > 0 ? BigDecimal.valueOf(technologyCountANum)
            .divide(BigDecimal.valueOf(technologyTotalNum), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
            : BigDecimal.ZERO;

        //返回值
        QueryViewGapAnalysisConformVo vo = new QueryViewGapAnalysisConformVo();
        vo.setTotalNum(Double.valueOf(totalNum).intValue());
        vo.setCountAFactor(countAFactor);
        vo.setCountCFactor(countCFactor);
        vo.setCountBNum(Double.valueOf(countBNum).intValue());
        vo.setCountBFactor(countBFactor);
        vo.setEffectiveFactor(effectiveFactor);
        vo.setCountDNum(Double.valueOf(countDNum).intValue());
        vo.setCountDFactor(countDFactor);
        vo.setManagementTotalNum(Double.valueOf(managementTotalNum).intValue());
        vo.setManagementCountAFactor(managementCountAFactor);
        vo.setManagementCountCFactor(managementCountCFactor);
        vo.setTechnologyTotalNum(Double.valueOf(technologyTotalNum).intValue());
        vo.setTechnologyCountAFactor(technologyCountAFactor);
        vo.setTechnologyCountCFactor(technologyCountCFactor);
        return vo;
    }

    @Override
    @SchemaSwitch(String.class)
    public SpecOverviewVO getSpecOverview(String operationId) {
        List<SpecCalcResult> specCalcResultList =
            specCalcResultService.list(new QueryWrapper<SpecCalcResult>().eq("operation_id", operationId));

        List<SpecialEvaluationConditionConfig> conditionConfigs =
            specialEvaluationConditionConfigMapper.selectResultScopeList(operationId, 2);
        List<String> lifeCycleSort =
            Lists.newArrayList("数据采集安全", "数据传输安全", "数据存储安全", "数据处理安全", "数据交换安全",
                "数据销毁安全", "通用安全");
        SpecOverviewVO specOverviewVO = new SpecOverviewVO();
        List<SpecOverviewVO.SpecLifeCycle> allSpecLifeCycleList = new ArrayList<>();
        //生命周期分数
        List<Double> lifeCycleScores = new ArrayList<>();
        NumberFormat df = DecimalFormat.getNumberInstance();
        df.setMaximumFractionDigits(3);

        if (CollUtil.isNotEmpty(specCalcResultList)) {
            // 先按照阶段分组，然后按过程域分组，再按照等级分组，计算各个等级平均分，最后算出过程域分数，最终得到生命周期分数
            // 等级得分=BP得分平均值
            // 过程域得分=等级得分之和
            // 生命周期等分=过程域得分平均值
            specCalcResultList.stream().collect(Collectors.groupingBy(SpecCalcResult::getStage)).forEach((k, v) -> {
                List<SpecOverviewVO.SpecLifeCycle> specLifeCycleList = new ArrayList<>();
                v.stream().collect(Collectors.groupingBy(SpecCalcResult::getProcess)).forEach((process, list) -> {
                        SpecOverviewVO.SpecLifeCycle specLifeCycle = new SpecOverviewVO.SpecLifeCycle();
                        specLifeCycle.setName(k);
                        specLifeCycle.setProcess(process);
                        specLifeCycle.setProcessScore(Double.valueOf(df.format(calcProcessScore(list))));
                        specLifeCycleList.add(specLifeCycle);
                    });
                double lifeCycleValue =
                    specLifeCycleList.stream().mapToDouble(SpecOverviewVO.SpecLifeCycle::getProcessScore).average()
                        .orElse(0.0);
                lifeCycleScores.add(lifeCycleValue);
                specLifeCycleList.forEach(
                    specLifeCycle -> specLifeCycle.setLifeCycleScore(Double.valueOf(df.format(lifeCycleValue))));
                allSpecLifeCycleList.addAll(specLifeCycleList);
            });
        }
        // 按照生命周期、过程域排序
        List<SpecOverviewVO.SpecLifeCycle> resultList =
            allSpecLifeCycleList.stream().sorted(Comparator.comparing(c -> lifeCycleSort.indexOf(c.getName())))
                .sorted(Comparator.comparing(SpecOverviewVO.SpecLifeCycle::getProcess)).collect(Collectors.toList());
        specOverviewVO.setSpecLifeCycleList(resultList);

        AtomicInteger total = new AtomicInteger();
        List<ResultCountDTO> resultCountList = coVerificationMapper.queryCountByOperationId(operationId);

        // 符合情况
        SpecOverviewVO.CheckResult checkResult = new SpecOverviewVO.CheckResult();
        resultCountList.forEach(resultCountDTO -> {
            if (OptEnum.A.getInfo().equals(resultCountDTO.getResult())){
                checkResult.setCountA(resultCountDTO.getCount());
            } else if (OptEnum.B.getInfo().equals(resultCountDTO.getResult())) {
                checkResult.setCountC(resultCountDTO.getCount());
            } else if (OptEnum.E.getInfo().equals(resultCountDTO.getResult())) {
                checkResult.setCountB(resultCountDTO.getCount());
            } else if (OptEnum.C.getInfo().equals(resultCountDTO.getResult())) {
                checkResult.setCountD(resultCountDTO.getCount());
            }
            total.getAndAdd(resultCountDTO.getCount());
        });

        checkResult.setCountBRate(
            BigDecimal.valueOf(checkResult.getCountB()).divide(BigDecimal.valueOf(total.get()), 4, RoundingMode.HALF_UP));
        checkResult.setCountARate(
            BigDecimal.valueOf(checkResult.getCountA()).divide(BigDecimal.valueOf(total.get()), 4, RoundingMode.HALF_UP));
        checkResult.setCountCRate(
            BigDecimal.valueOf(checkResult.getCountC()).divide(BigDecimal.valueOf(total.get()), 4, RoundingMode.HALF_UP));
        checkResult.setCountDRate(
            BigDecimal.valueOf(checkResult.getCountD()).divide(BigDecimal.valueOf(total.get()), 4, RoundingMode.HALF_UP));
        checkResult.setTotal(total.get());
        specOverviewVO.setCheckResult(checkResult);

        // 计算总得分
        double totalScore = lifeCycleScores.stream().mapToDouble(Double::doubleValue).average().orElse(0);
        specOverviewVO.setTotalScore(Double.valueOf(df.format(totalScore)));
        if (CollUtil.isNotEmpty(conditionConfigs)) {
            List<BetweenBaseRule.InnerBaseRule> innerBaseRules = new ArrayList<>();
            conditionConfigs.forEach(specialEvaluationConditionConfig -> {
                BetweenBaseRule.InnerBaseRule innerBaseRule =
                    new BetweenBaseRule.InnerBaseRule(specialEvaluationConditionConfig);
                innerBaseRules.add(innerBaseRule);
            });
            BetweenBaseRule betweenBaseRule = new BetweenBaseRule(innerBaseRules);
            String result = betweenBaseRule.accept(BigDecimal.valueOf(totalScore));
            specOverviewVO.setResult(result);
            specOverviewVO.setDesc(String.format("当前能力评估结果为%s", result));
        }
        return specOverviewVO;
    }

    private double calcProcessScore(List<SpecCalcResult> list) {
        List<Double> levelAverageList = new ArrayList<>();
        list.stream().sorted(Comparator.comparing(SpecCalcResult::getLevel))
            .collect(Collectors.groupingBy(SpecCalcResult::getLevel)).forEach((level, v) -> {
                double levelValue =
                    v.stream().map(SpecCalcResult::getScore).collect(Collectors.averagingDouble(BigDecimal::doubleValue));
                levelAverageList.add(levelValue);
            });
       return levelAverageList.stream().mapToDouble(Double::doubleValue).sum();
    }

    @Override
    public QueryViewGapAnalysisConformVo querySpecGapChartResult(String operationId) {
        OperationIdDto dto = new OperationIdDto();
        dto.setOperationId(operationId);
        List<String> keyList = new ArrayList<>();
        List<CoGapAnalysis> list = coGapAnalysisService.list(new QueryWrapper<CoGapAnalysis>().eq("operation_id", operationId));
        boolean exist1 = false;
        boolean exist2 = false;
        boolean exist3 = false;
        boolean exist4 = false;
        if (CollUtil.isNotEmpty(list)){
            for (CoGapAnalysis coGapAnalysis : list) {
                if (!"101".equals(coGapAnalysis.getAnalysisDimension())) {
                    continue;
                }
                String key = StrUtil.join(StrUtil.DASHED, coGapAnalysis.getStandardId(), coGapAnalysis.getChartName());
                if (keyList.contains(key)) {
                    continue;
                }
                if (1000 != coGapAnalysis.getStandardId()){
                    continue;
                }
                // 专项评估报告页面 差距分析图表固定以下四种
                if ("stage".equals(coGapAnalysis.getAnalysisGranularity()) && "极坐标图".equals(
                    coGapAnalysis.getAnalysisChart())) {
                    keyList.add(key);
                    exist1 = true;
                } else if ("stage".equals(coGapAnalysis.getAnalysisGranularity()) && "表格".equals(
                    coGapAnalysis.getAnalysisChart())) {
                    keyList.add(key);
                    exist2 = true;
                } else if ("process".equals(coGapAnalysis.getAnalysisGranularity()) && "雷达图".equals(
                    coGapAnalysis.getAnalysisChart())) {
                    keyList.add(key);
                    exist3 = true;
                } else if ("dimension".equals(coGapAnalysis.getAnalysisGranularity()) && "雷达图".equals(
                    coGapAnalysis.getAnalysisChart())) {
                    keyList.add(key);
                    exist4 = true;
                }
            }
        }
        String level = coVerificationMapper.queryMaxLevel(operationId);
        AnalysisCharReq req = new AnalysisCharReq();
        req.setStandardId(1000);
        req.setLabelId(113L);
        req.setOperationId(operationId);
        req.setLevel(LevelEnum.getLevelByCode(level));
        req.setTemplateId(101);
        if (!exist1){
            req.setGranularity("stage");
            req.setGranularityName("生命周期");
            req.setChart("极坐标图");
            QueryGapAnalysisVo queryGapAnalysisVo = coGapAnalysisService.addChart(req);
            keyList.add(StrUtil.join(StrUtil.DASHED, queryGapAnalysisVo.getStandardId(), queryGapAnalysisVo.getChartName()));
        }
        if (!exist2){
            req.setGranularity("stage");
            req.setGranularityName("生命周期");
            req.setChart("表格");
            QueryGapAnalysisVo queryGapAnalysisVo = coGapAnalysisService.addChart(req);
            keyList.add(StrUtil.join(StrUtil.DASHED, queryGapAnalysisVo.getStandardId(), queryGapAnalysisVo.getChartName()));
        }
        if (!exist3){
            req.setGranularity("process");
            req.setGranularityName("过程域");
            req.setChart("雷达图");
            QueryGapAnalysisVo queryGapAnalysisVo = coGapAnalysisService.addChart(req);
            keyList.add(StrUtil.join(StrUtil.DASHED, queryGapAnalysisVo.getStandardId(), queryGapAnalysisVo.getChartName()));
        }
        if (!exist4){
            req.setGranularity("dimension");
            req.setGranularityName("能力维度");
            req.setChart("雷达图");
            QueryGapAnalysisVo queryGapAnalysisVo = coGapAnalysisService.addChart(req);
            keyList.add(StrUtil.join(StrUtil.DASHED, queryGapAnalysisVo.getStandardId(), queryGapAnalysisVo.getChartName()));
        }
        QueryViewGapAnalysisConformVo gapAnalysisConformVo = queryTotalGapConformResult(operationId, keyList);
        gapAnalysisConformVo.setCoGapAnalysisMap(queryGapAnalysisChartByOperationId(operationId,keyList));
        return gapAnalysisConformVo;
    }

    @SchemaSwitch
    @Override
    public CustomerOverviewVO getDataOverview(String customerId) {
        CustomerOverviewVO vo = new CustomerOverviewVO();
        // 查询客户所有的项目ID
        List<String> projectIds = coProjectMapper.queryProjectIdsByCustomerId(customerId);
        if (CollUtil.isEmpty(projectIds)) {
            return vo.init();
        }

        // 查询所有项目下的作业ID
        List<String> operationIds = coOperationMapper.queryOperationIdsByProjectIds(projectIds);
        if (CollUtil.isEmpty(operationIds)) {
            return vo.init();
        }

        // 获取所有作业下的业务系统 根据名字去重
        List<String> systemNameList =
            dynamicProcessTreeMapper.queryBusSystemNameByOperationIds(operationIds, LabelEnum.XTDY.getCode());
        vo.setBuySystemCount(systemNameList.stream().distinct().count());
        // 获取所有作业下的资产数量 根据schema+table去重
        List<CoInventory> coInventories = coInventoryMapper.queryInventoryAllByOperationIds(operationIds);
        long total = coInventories.stream()
            .map(coInventory -> coInventory.getSchemaName() + StrPool.DOT + coInventory.getDataAsset()).distinct()
            .count();
        long sensitiveCount = coInventories.stream().filter(coInventory -> coInventory.getSensitive() != null && coInventory.getSensitive() == 1)
            .map(coInventory -> coInventory.getSchemaName() + StrPool.DOT + coInventory.getDataAsset()).distinct()
            .count();
        vo.setInventoryCount(total);
        vo.setSensitiveRate(total == 0 ? 0
            : BigDecimal.valueOf(sensitiveCount).divide(BigDecimal.valueOf(total), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue());

        // 获取所有作业下用户权限
        List<CoAssetUser> coAssetUserList = coAssetUserMapper.queryAllByOperationIds(operationIds);
        long userAuthTotal = coAssetUserList.stream()
            .map(coAssetUser -> coAssetUser.getUsername() + StrPool.DOT + coAssetUser.getDbConfig()).distinct().count();
        long allAuthCount = coAssetUserList.stream().filter(CoAssetUser::getCrudPri)
            .map(coAssetUser -> coAssetUser.getUsername() + StrPool.DOT + coAssetUser.getDbConfig()).distinct().count();
        vo.setUserAuthCount(userAuthTotal);
        vo.setAllAuthUserRate(userAuthTotal == 0 ? 0
            : BigDecimal.valueOf(allAuthCount).divide(BigDecimal.valueOf(userAuthTotal), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue());

        // 统计基础检测符合情况
        statisticsDbAccordStat(vo, operationIds);

        // 统计合规风险TOP10
        statisticsLegalResult(vo, operationIds);

        // 统计安全风险TOP10
        statisticsRiskResult(vo, operationIds);

        // 统计能力差距分布TOP10
        statisticsVerificationResult(vo, operationIds);
        return vo;
    }

    @Override
    public List<ApiInterfaceVO> jobInterfaceReport(String operationId) {
        List<Long> planIds = mkAppJobMapper.selectList(new QueryWrapper<MkAppJob>().eq("operation_id", operationId).eq("type", 4).eq("status", 2))
                .stream().map(MkAppJob::getPlanId).collect(Collectors.toList());
        if (CollUtil.isEmpty(planIds))
            return null;
        String url = String.format(AppInterfaceConstants.PCAP_WORD_REPORT, "8090");

        log.info("[网络流量监测]-[WORD报告查询] 准备调用 url : {}", url);
        long start = System.currentTimeMillis();
        ApiJobQueryReq apiJobQueryReq = new ApiJobQueryReq();
        apiJobQueryReq.setPlanIds(planIds);
        String result = HttpUtil.post(url, JSONUtil.toJsonStr(apiJobQueryReq), 10_000);
        log.info("[网络流量监测]-[WORD报告查询] 调用成功，耗时：{}，请求地址：{}，返回信息：{}", System.currentTimeMillis() - start, url, result);

        Response<List<ApiInterfaceVO>> res = JSONUtil.parseObj(result).toBean(new TypeReference<Response<List<ApiInterfaceVO>>>(){
        });
        if (!res.isSuccess()) {
            throw new ServiceException(res.getMsg());
        }
        return res.getData();
    }

    private void statisticsVerificationResult(CustomerOverviewVO vo, List<String> operationIds) {
        List<BusSystemResultDTO> busSystemVerResultList = coVerificationMapper.queryBusSystemVerificationResultByOperationIds(operationIds);
        if (CollUtil.isNotEmpty(busSystemVerResultList)){
            CustomerOverviewVO.CapabilityRiskSorted capabilityRiskSorted = new CustomerOverviewVO.CapabilityRiskSorted();
            List<BusSystemResultDTO> finalResultList = splitBusSystemResultList(busSystemVerResultList);
            CustomerOverviewVO.AxisObject xAxisObject = new CustomerOverviewVO.AxisObject();
            CustomerOverviewVO.AxisObject yAxisObject = new CustomerOverviewVO.AxisObject();
            List<Object> busSystemResultList = new ArrayList<>();
            List<Object> busSystemtList = new ArrayList<>();
            // 未排序列表
            List<ResultCountDTO> unSortList = new ArrayList<>();
            finalResultList.stream().collect(Collectors.groupingBy(BusSystemResultDTO::getBusSystem)).forEach((busSystem, list)->{
                // 取第一个作业检测结果
                list.stream().collect(Collectors.groupingBy(BusSystemResultDTO::getCreateTime))
                    .forEach((date, subList) -> {
                        if (busSystemtList.contains(busSystem)) {
                            return;
                        }
                        int countC = Math.toIntExact(subList.stream().filter(
                                busSystemResultDTO -> OptEnum.C.getInfo().equals(busSystemResultDTO.getSystemResult()))
                            .count());
                        int totalCount = subList.size();
                        ResultCountDTO resultCountDTO = new ResultCountDTO();
                        resultCountDTO.setName(busSystem);
                        resultCountDTO.setRate(totalCount == 0 ? 0.0
                            : BigDecimal.valueOf(countC).divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100)).doubleValue());
                        unSortList.add(resultCountDTO);
                    });
            });
            // 根据占比比较取top10
            unSortList.stream().sorted(Comparator.comparingDouble(ResultCountDTO::getRate).reversed()).limit(10).forEach(res -> {
                busSystemtList.add(res.getName());
                busSystemResultList.add(res.getRate());
            });
            yAxisObject.setData(busSystemtList);
            yAxisObject.setName("业务系统");
            xAxisObject.setData(busSystemResultList);
            xAxisObject.setName("不符合占比");
            capabilityRiskSorted.setXAxis(xAxisObject);
            capabilityRiskSorted.setYAxis(yAxisObject);
            vo.setCapabilityRiskSorted(capabilityRiskSorted);

            // 统计能力分析文件选中次数
            CustomerOverviewVO.StandardFileSelected standardFileSelected = new CustomerOverviewVO.StandardFileSelected();
            List<PieDataDTO> standardFileSelectedList = coVerificationMapper.countStandardFileSelected(operationIds);
            standardFileSelected.setData(standardFileSelectedList);
            vo.setStandardFileSelected(standardFileSelected);
        }

    }

    private void statisticsRiskResult(CustomerOverviewVO vo, List<String> operationIds) {
        CustomerOverviewVO.SafetyRiskSorted safetyRiskSorted = new CustomerOverviewVO.SafetyRiskSorted();
        List<BusSystemResultDTO> list = adviseRiskMapper.queryAdviseRiskByOperationIds(operationIds);
        if (CollUtil.isNotEmpty(list)) {
            List<Map<String, Object>> busSystemList = dynamicProcessTreeMapper.queryBusSystemMapByOperationIds(operationIds);
            Map<String, String> busSystemMap = new HashMap<>(16);
            for (Map<String, Object> map : busSystemList){
                busSystemMap.put(String.join(StrPool.DASHED,(String)map.get("operationid"),String.valueOf(map.get("systemid"))),
                    (String)map.get("systemname"));
            }
            List<BusSystemResultDTO> finalList = new ArrayList<>();
            for (BusSystemResultDTO dto : list){
                String[] systemIdArr = dto.getBusSystem().split(StrPool.COMMA);
                BusSystemResultDTO result = new BusSystemResultDTO();
                BeanUtils.copyProperties(dto, result);
                result.setBusSystem(busSystemMap.get(String.join(StrPool.DASHED, dto.getOperationId(), systemIdArr[0])));
                finalList.add(result);
            }
            CustomerOverviewVO.AxisObject xAxisObject = new CustomerOverviewVO.AxisObject();
            CustomerOverviewVO.AxisObject yAxisObject = new CustomerOverviewVO.AxisObject();
            List<Object> busSystemResultList = new ArrayList<>();
            List<Object> busSystemtList = new ArrayList<>();
            // 未排序列表
            List<ResultCountDTO> unSortList = new ArrayList<>();
            finalList.stream().collect(Collectors.groupingBy(BusSystemResultDTO::getBusSystem)).forEach((busSystem, list1)->{
                // 取第一个作业检测结果
                list1.stream().collect(Collectors.groupingBy(BusSystemResultDTO::getCreateTime))
                    .forEach((date, subList) -> {
                        if (busSystemtList.contains(busSystem)) {
                            return;
                        }
                        int countHighLevel = Math.toIntExact(subList.stream().filter(
                                busSystemResultDTO -> busSystemResultDTO.getLevel() != null && busSystemResultDTO.getLevel() >= 4)
                            .count());
                        int totalCount = subList.size();
                        ResultCountDTO resultCountDTO = new ResultCountDTO();
                        resultCountDTO.setName(busSystem);
                        resultCountDTO.setRate(totalCount == 0 ? 0 : BigDecimal.valueOf(countHighLevel)
                            .divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100)).doubleValue());
                        unSortList.add(resultCountDTO);
                    });
            });
            // 根据占比比较取top10
            unSortList.stream().sorted(Comparator.comparingDouble(ResultCountDTO::getRate).reversed()).limit(10).forEach(res -> {
                busSystemtList.add(res.getName());
                busSystemResultList.add(res.getRate());
            });
            yAxisObject.setData(busSystemtList);
            yAxisObject.setName("业务系统");
            xAxisObject.setData(busSystemResultList);
            xAxisObject.setName("高风险占比");
            safetyRiskSorted.setXAxis(xAxisObject);
            safetyRiskSorted.setYAxis(yAxisObject);
            vo.setSafetyRiskSorted(safetyRiskSorted);

            // 统计风险模型文件应用次数
            CustomerOverviewVO.ModelFileSelected modelFileSelected = new CustomerOverviewVO.ModelFileSelected();
            List<PieDataDTO> pieDataList = coOperationMapper.countModelFileSelected(operationIds);
            modelFileSelected.setData(pieDataList);
            vo.setModelFileSelected(modelFileSelected);
        }
    }

    private void statisticsDbAccordStat(CustomerOverviewVO vo, List<String> operationIds) {
        CustomerOverviewVO.DbAccordStat dbAccordStat = new CustomerOverviewVO.DbAccordStat();
        // 按创建作业时间倒叙排序
        List<DbSourceAccordDTO> dbSourceAccords = detectionResultMapper.queryDbAccordByOperationIds(operationIds);
        if (CollUtil.isNotEmpty(dbSourceAccords)) {
            List<Integer> accordCountList = new ArrayList<>();
            List<Integer> unAccordCountList = new ArrayList<>();
            List<String> nameList = new ArrayList<>();
            dbSourceAccords.stream().collect(Collectors.groupingBy(DbSourceAccordDTO::getDbSourceName))
                .forEach((dbSourceName, list) -> {
                    // 取第一个作业检测结果
                    list.stream().collect(Collectors.groupingBy(DbSourceAccordDTO::getCreateTime))
                        .forEach((date, subList) -> {
                            if (nameList.contains(dbSourceName)) {
                                return;
                            }
                            int accordCount =
                                Math.toIntExact(subList.stream().filter(DbSourceAccordDTO::getAccord).count());
                            accordCountList.add(accordCount);
                            unAccordCountList.add(subList.size() - accordCount);
                            nameList.add(dbSourceName);
                        });

                });
            dbAccordStat.setAccordCountList(accordCountList);
            dbAccordStat.setDbList(nameList);
            dbAccordStat.setUnAccordCountList(unAccordCountList);
            vo.setDbAccordStat(dbAccordStat);
        }
    }

    private void statisticsLegalResult(CustomerOverviewVO vo, List<String> operationIds) {
        List<BusSystemResultDTO> legalResultList = coLegalMapper.queryBusSystemLegalResultByOperationIds(operationIds);
        if (CollUtil.isNotEmpty(legalResultList)){
            List<BusSystemResultDTO> finalLegalResultList = splitBusSystemResultList(legalResultList);
            CustomerOverviewVO.LegalRiskSorted legalRiskSorted = new CustomerOverviewVO.LegalRiskSorted();
            CustomerOverviewVO.AxisObject xAxisObject = new CustomerOverviewVO.AxisObject();
            CustomerOverviewVO.AxisObject yAxisObject = new CustomerOverviewVO.AxisObject();
            List<Object> busSystemResultList = new ArrayList<>();
            List<Object> busSystemtList = new ArrayList<>();
            // 未排序列表
            List<ResultCountDTO> unSortList = new ArrayList<>();
            finalLegalResultList.stream().collect(Collectors.groupingBy(BusSystemResultDTO::getBusSystem)).forEach((busSystem, list)->{
                // 取第一个作业检测结果
                list.stream().collect(Collectors.groupingBy(BusSystemResultDTO::getCreateTime))
                    .forEach((date, subList) -> {
                        if (busSystemtList.contains(busSystem)) {
                            return;
                        }
                        int countC = Math.toIntExact(subList.stream().filter(
                                busSystemResultDTO -> OptEnum.C.getInfo().equals(busSystemResultDTO.getSystemResult()))
                            .count());
                        int totalCount = subList.size();
                        ResultCountDTO resultCountDTO = new ResultCountDTO();
                        resultCountDTO.setName(busSystem);
                        resultCountDTO.setRate(totalCount == 0 ? 0
                            : BigDecimal.valueOf(countC).divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100)).doubleValue());
                        unSortList.add(resultCountDTO);
                    });
            });

            // 根据占比比较取top10
            unSortList.stream().sorted(Comparator.comparingDouble(ResultCountDTO::getRate).reversed()).limit(10).forEach(res -> {
                busSystemtList.add(res.getName());
                busSystemResultList.add(res.getRate());
            });
            yAxisObject.setData(busSystemtList);
            yAxisObject.setName("业务系统");
            xAxisObject.setData(busSystemResultList);
            xAxisObject.setName("不符合占比");
            legalRiskSorted.setXAxis(xAxisObject);
            legalRiskSorted.setYAxis(yAxisObject);
            vo.setLegalRiskSorted(legalRiskSorted);

            CustomerOverviewVO.LegalFileSelected legalFileSelected = new CustomerOverviewVO.LegalFileSelected();
            List<PieDataDTO> lawFileSelectedList = coLegalMapper.countLawFileSelected(operationIds);
            legalFileSelected.setData(lawFileSelectedList);
            vo.setLegalFileSelected(legalFileSelected);
        }
    }

    private List<BusSystemResultDTO> splitBusSystemResultList(List<BusSystemResultDTO> legalResultList) {
        List<BusSystemResultDTO> finalLegalResultList = new ArrayList<>();
        // 将"OB系统：不适用\nOA系统：不符合"拆分
        for (BusSystemResultDTO busSystemResultDTO : legalResultList) {
            String[] systemResultArr = busSystemResultDTO.getSystemResult().split(StrPool.LF);
            for (String systemResult : systemResultArr) {
                BusSystemResultDTO dto = new BusSystemResultDTO();
                BeanUtils.copyProperties(busSystemResultDTO, dto);
                String[] strArr = systemResult.split(Constants.SYSTEM_SUFFIX);
                dto.setBusSystem(strArr[0]);
                dto.setSystemResult(strArr[1]);
                finalLegalResultList.add(dto);
            }
        }
        return finalLegalResultList;
    }

    @Override
    public ApiTecReportVO tecReportQuery(String operationId) {
        ApiTecReportVO vo = new ApiTecReportVO();
        // 获取数源总数
        List<PreSourceConfig> preSourceConfigList = preSourceConfigMapper.selectList(new QueryWrapper<PreSourceConfig>().eq("operation_id", operationId));

        ApiTecReportVO.HistogramChart histogramChart = new ApiTecReportVO.HistogramChart();
        Map<Integer, String> dbSourceMap = new HashMap<>(16);
        preSourceConfigList.forEach(preSourceConfig -> {
            String appIds = preSourceConfig.getAppIds();
            String[] appIdArr = appIds.split(",");
            for (String appId : appIdArr) {
                dbSourceMap.put(Integer.parseInt(appId), preSourceConfig.getConfigName());
            }
        });

        // 查询技术能力作业结果
        int epReachNum = 0;
        int dmReachNum = 0;
        AtomicInteger aclReachNum = new AtomicInteger();
        List<MkAppJob> appJobs = mkAppJobMapper.selectList(new QueryWrapper<MkAppJob>().eq("operation_id", operationId).eq("status", 2));
        int totalJobNum = 0;
        Map<String, ApiTecReportVO.XAxis> map = new HashMap<>(16);
        for (MkAppJob mkAppJob : appJobs) {
            // 过滤脱敏加密作业
            if (!AbilityType.ENCRYPT.getCode().equals(mkAppJob.getType()) && !AbilityType.DESENSITIZATION.getCode().equals(mkAppJob.getType())){
                continue;
            }
            ApiJobQueryVO result = JSONUtil.toBean(JSONUtil.toJsonStr(mkAppJob.getParamOut()), ApiJobQueryVO.class);
            if (AbilityType.DESENSITIZATION.getCode().equals(mkAppJob.getType()) && ("1".equals(result.getResult()) || "0.5".equals(result.getResult()))) {
                dmReachNum++;
            } else if (AbilityType.ENCRYPT.getCode().equals(mkAppJob.getType()) && ("1".equals(result.getResult()) || "0.5".equals(result.getResult()))){
                epReachNum++;
            }
            String dbSourceName = dbSourceMap.get(mkAppJob.getId());
            map.put(dbSourceName, new ApiTecReportVO.XAxis(dbSourceName, dmReachNum, epReachNum));
            totalJobNum++;
        }
        // 查询访问控制结果
        List<CoPermission> permissions = coPermissionMapper.listByOperationId(operationId);
        Map<String, List<CoPermission>> dbPermissionMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(permissions)){
            dbPermissionMap = permissions.stream().filter(coPermission -> StrUtil.isNotEmpty(coPermission.getResult())).collect(Collectors.groupingBy(CoPermission::getName));
        }
        List<ApiTecReportVO.XAxis> list = new ArrayList<>();
        Map<String, List<CoPermission>> finalDbPermissionMap = dbPermissionMap;
        map.forEach((k,v)-> {
            if (finalDbPermissionMap.containsKey(k)){
              aclReachNum.set(1);
            }
            v.setAclReachNum(aclReachNum.get());
            list.add(v);
        });
        int tecReachSourceNum = dmReachNum+epReachNum;
        vo.setJobTotalNum(totalJobNum);
        histogramChart.setXAxisList(list);
        vo.setHistogramChart(histogramChart);
        vo.setEpReachNum(epReachNum);
        vo.setDmReachNum(dmReachNum);
        vo.setAclReachNum(aclReachNum.get());
        vo.setTecReachJobNum(tecReachSourceNum);
        vo.setReachRate(totalJobNum == 0 ? 0 : BigDecimal.valueOf(tecReachSourceNum).divide(BigDecimal.valueOf(totalJobNum), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue());
        return vo;
    }
}

