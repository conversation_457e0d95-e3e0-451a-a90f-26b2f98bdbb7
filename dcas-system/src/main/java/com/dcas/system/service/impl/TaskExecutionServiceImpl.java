package com.dcas.system.service.impl;

import com.dcas.common.domain.entity.TaskExecution;
import com.dcas.common.mapper.TaskExecutionMapper;
import com.dcas.common.utils.DateUtils;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.StringUtils;
import com.dcas.system.service.ITaskExecutionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 任务执行记录Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskExecutionServiceImpl implements ITaskExecutionService {

    private final TaskExecutionMapper taskExecutionMapper;

    /**
     * 查询任务执行记录
     * 
     * @param executionId 任务执行记录主键
     * @return 任务执行记录
     */
    @Override
    public TaskExecution selectTaskExecutionByExecutionId(Long executionId) {
        return taskExecutionMapper.selectTaskExecutionByExecutionId(executionId);
    }

    /**
     * 查询任务执行记录列表
     * 
     * @param taskExecution 任务执行记录
     * @return 任务执行记录
     */
    @Override
    public List<TaskExecution> selectTaskExecutionList(TaskExecution taskExecution) {
        return taskExecutionMapper.selectTaskExecutionList(taskExecution);
    }

    /**
     * 根据任务ID查询执行记录列表
     * 
     * @param taskId 任务ID
     * @return 执行记录列表
     */
    @Override
    public List<TaskExecution> selectTaskExecutionByTaskId(Long taskId) {
        return taskExecutionMapper.selectTaskExecutionByTaskId(taskId);
    }

    /**
     * 新增任务执行记录
     * 
     * @param taskExecution 任务执行记录
     * @return 结果
     */
    @Override
    public int insertTaskExecution(TaskExecution taskExecution) {
        return taskExecutionMapper.insertTaskExecution(taskExecution);
    }

    /**
     * 修改任务执行记录
     * 
     * @param taskExecution 任务执行记录
     * @return 结果
     */
    @Override
    public int updateTaskExecution(TaskExecution taskExecution) {
        return taskExecutionMapper.updateTaskExecution(taskExecution);
    }

    /**
     * 批量删除任务执行记录
     * 
     * @param executionIds 需要删除的任务执行记录主键
     * @return 结果
     */
    @Override
    public int deleteTaskExecutionByExecutionIds(Long[] executionIds) {
        return taskExecutionMapper.deleteTaskExecutionByExecutionIds(executionIds);
    }

    /**
     * 删除任务执行记录信息
     * 
     * @param executionId 任务执行记录主键
     * @return 结果
     */
    @Override
    public int deleteTaskExecutionByExecutionId(Long executionId) {
        return taskExecutionMapper.deleteTaskExecutionByExecutionId(executionId);
    }

    /**
     * 根据任务ID删除所有执行记录
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int deleteTaskExecutionByTaskId(Long taskId) {
        return taskExecutionMapper.deleteTaskExecutionByTaskId(taskId);
    }

    /**
     * 创建新的执行记录
     * 
     * @param taskId 任务ID
     * @param executorId 执行者ID
     * @param executorName 执行者名称
     * @return 执行记录
     */
    @Override
    public TaskExecution createTaskExecution(Long taskId, Long executorId, String executorName) {
        TaskExecution execution = new TaskExecution();
        execution.setTaskId(taskId);
        execution.setExecutionBatch(generateExecutionBatch(taskId));
        execution.setStatus("STARTED");
        execution.setExecutorId(executorId);
        execution.setExecutorName(executorName);
        execution.setCurrentStep(0);
        execution.setProgressPercentage(0);
        execution.setSuccessSteps(0);
        execution.setFailedSteps(0);
        execution.setSkippedSteps(0);
        execution.setRetryCount(0);
        execution.setManuallyTerminated("0");
        execution.setExecutionEnvironment(getExecutionEnvironment());
        
        insertTaskExecution(execution);
        return execution;
    }

    /**
     * 开始执行记录
     * 
     * @param executionId 执行记录ID
     * @param totalSteps 总步骤数
     * @return 结果
     */
    @Override
    public boolean startExecution(Long executionId, Integer totalSteps) {
        Date startTime = DateUtils.getNowDate();
        TaskExecution execution = new TaskExecution();
        execution.setExecutionId(executionId);
        execution.setStatus("RUNNING");
        execution.setStartTime(startTime);
        execution.setTotalSteps(totalSteps);

        appendExecutionLog(executionId, "任务开始执行，总步骤数: " + totalSteps);
        
        return updateTaskExecution(execution) > 0;
    }

    /**
     * 完成执行记录
     * 
     * @param executionId 执行记录ID
     * @param executionResult 执行结果
     * @return 结果
     */
    @Override
    public boolean completeExecution(Long executionId, String executionResult) {
        Date endTime = DateUtils.getNowDate();
        TaskExecution execution = selectTaskExecutionByExecutionId(executionId);
        if (execution == null) {
            return false;
        }
        
        long duration = 0;
        if (execution.getStartTime() != null) {
            duration = endTime.getTime() - execution.getStartTime().getTime();
        }
        
        execution.setStatus("COMPLETED");
        execution.setEndTime(endTime);
        execution.setExecutionDuration(duration);
        execution.setExecutionResult(executionResult);
        execution.setProgressPercentage(100);

        appendExecutionLog(executionId, "任务执行完成，执行时长: " + duration + "ms");
        
        return updateTaskExecution(execution) > 0;
    }

    /**
     * 标记执行失败
     * 
     * @param executionId 执行记录ID
     * @param errorMessage 错误信息
     * @return 结果
     */
    @Override
    public boolean failExecution(Long executionId, String errorMessage) {
        Date endTime = DateUtils.getNowDate();
        TaskExecution execution = selectTaskExecutionByExecutionId(executionId);
        if (execution == null) {
            return false;
        }
        
        long duration = 0;
        if (execution.getStartTime() != null) {
            duration = endTime.getTime() - execution.getStartTime().getTime();
        }
        
        execution.setStatus("FAILED");
        execution.setEndTime(endTime);
        execution.setExecutionDuration(duration);
        execution.setErrorMessage(errorMessage);

        appendExecutionLog(executionId, "任务执行失败: " + errorMessage);
        
        return updateTaskExecution(execution) > 0;
    }

    /**
     * 终止执行记录
     * 
     * @param executionId 执行记录ID
     * @param terminationReason 终止原因
     * @return 结果
     */
    @Override
    public boolean terminateExecution(Long executionId, String terminationReason) {
        Date endTime = DateUtils.getNowDate();
        TaskExecution execution = selectTaskExecutionByExecutionId(executionId);
        if (execution == null) {
            return false;
        }
        
        long duration = 0;
        if (execution.getStartTime() != null) {
            duration = endTime.getTime() - execution.getStartTime().getTime();
        }
        
        execution.setStatus("TERMINATED");
        execution.setEndTime(endTime);
        execution.setExecutionDuration(duration);
        execution.setManuallyTerminated("1");
        execution.setTerminationReason(terminationReason);

        appendExecutionLog(executionId, "任务被终止: " + terminationReason);
        
        return updateTaskExecution(execution) > 0;
    }

    /**
     * 更新执行进度
     * 
     * @param executionId 执行记录ID
     * @param currentStep 当前步骤
     * @param progressPercentage 进度百分比
     * @param successSteps 成功步骤数
     * @param failedSteps 失败步骤数
     * @param skippedSteps 跳过步骤数
     * @return 结果
     */
    @Override
    public boolean updateExecutionProgress(Long executionId, Integer currentStep, Integer progressPercentage,
                                          Integer successSteps, Integer failedSteps, Integer skippedSteps) {
        return taskExecutionMapper.updateTaskExecutionProgress(executionId, currentStep, progressPercentage,
                successSteps, failedSteps, skippedSteps, getUsername()) > 0;
    }

    /**
     * 添加执行日志
     * 
     * @param executionId 执行记录ID
     * @param logMessage 日志信息
     * @return 结果
     */
    @Override
    public boolean appendExecutionLog(Long executionId, String logMessage) {
        TaskExecution execution = selectTaskExecutionByExecutionId(executionId);
        if (execution == null) {
            return false;
        }
        
        String timestamp = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getNowDate());
        String newLogEntry = "[" + timestamp + "] " + logMessage + "\n";
        
        String existingLog = execution.getExecutionLog();
        String updatedLog = StringUtils.isEmpty(existingLog) ? newLogEntry : existingLog + newLogEntry;
        
        return taskExecutionMapper.updateTaskExecutionLog(executionId, updatedLog, getUsername()) > 0;
    }

    /**
     * 获取当前用户名
     */
    private String getUsername() {
        try {
            return SecurityUtils.getAccount();
        } catch (Exception e) {
            return "system";
        }
    }

    /**
     * 根据执行批次号查询执行记录
     *
     * @param executionBatch 执行批次号
     * @return 执行记录
     */
    @Override
    public TaskExecution getTaskExecutionByBatch(String executionBatch) {
        return taskExecutionMapper.selectTaskExecutionByBatch(executionBatch);
    }

    /**
     * 查询任务的最新执行记录
     *
     * @param taskId 任务ID
     * @return 最新执行记录
     */
    @Override
    public TaskExecution getLatestTaskExecution(Long taskId) {
        return taskExecutionMapper.selectLatestTaskExecution(taskId);
    }

    /**
     * 查询运行中的执行记录
     *
     * @return 运行中的执行记录列表
     */
    @Override
    public List<TaskExecution> getRunningExecutions() {
        return taskExecutionMapper.selectRunningExecutions();
    }

    /**
     * 根据状态查询执行记录
     *
     * @param status 状态
     * @return 执行记录列表
     */
    @Override
    public List<TaskExecution> getExecutionsByStatus(String status) {
        return taskExecutionMapper.selectTaskExecutionByStatus(status);
    }

    /**
     * 生成执行批次号
     *
     * @param taskId 任务ID
     * @return 执行批次号
     */
    @Override
    public String generateExecutionBatch(Long taskId) {
        String timestamp = DateUtils.parseDateToStr("yyyyMMddHHmmss", DateUtils.getNowDate());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return "TASK_" + taskId + "_" + timestamp + "_" + uuid;
    }

    /**
     * 增加重试次数
     *
     * @param executionId 执行记录ID
     * @return 结果
     */
    @Override
    public boolean incrementRetryCount(Long executionId) {
        return taskExecutionMapper.incrementExecutionRetryCount(executionId, getUsername()) > 0;
    }

    /**
     * 检查执行记录是否可以终止
     *
     * @param executionId 执行记录ID
     * @return 是否可以终止
     */
    @Override
    public boolean canTerminateExecution(Long executionId) {
        TaskExecution execution = selectTaskExecutionByExecutionId(executionId);
        if (execution == null) {
            return false;
        }

        String status = execution.getStatus();
        return "STARTED".equals(status) || "RUNNING".equals(status);
    }

    /**
     * 检查执行记录是否正在运行
     *
     * @param executionId 执行记录ID
     * @return 是否正在运行
     */
    @Override
    public boolean isExecutionRunning(Long executionId) {
        TaskExecution execution = selectTaskExecutionByExecutionId(executionId);
        if (execution == null) {
            return false;
        }

        String status = execution.getStatus();
        return "STARTED".equals(status) || "RUNNING".equals(status);
    }

    /**
     * 计算执行时长
     *
     * @param executionId 执行记录ID
     * @return 执行时长（毫秒）
     */
    @Override
    public Long calculateExecutionDuration(Long executionId) {
        TaskExecution execution = selectTaskExecutionByExecutionId(executionId);
        if (execution == null || execution.getStartTime() == null) {
            return 0L;
        }

        Date endTime = execution.getEndTime();
        if (endTime == null) {
            endTime = DateUtils.getNowDate(); // 如果还在执行中，使用当前时间
        }

        return endTime.getTime() - execution.getStartTime().getTime();
    }

    /**
     * 获取执行环境信息
     */
    private String getExecutionEnvironment() {
        return "Java " + System.getProperty("java.version") +
               " on " + System.getProperty("os.name") +
               " " + System.getProperty("os.version");
    }
}
