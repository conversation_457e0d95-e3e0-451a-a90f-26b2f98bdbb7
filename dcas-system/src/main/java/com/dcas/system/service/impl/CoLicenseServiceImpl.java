package com.dcas.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.algorithms.SnowFlake;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.AttributeEnum;
import com.dcas.common.enums.CommonResultCode;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.exception.params.FailParamsException;
import com.dcas.common.mapper.CoOperationMapper;
import com.dcas.common.mapper.SecurityOperationMapper;
import com.dcas.common.utils.HttpPost;
import com.dcas.common.utils.ServletUtils;
import com.dcas.common.utils.StringUtils;
import com.dcas.common.utils.file.FileUploadUtils;
import com.dcas.common.utils.file.FileUtils;
import com.dcas.common.domain.entity.CoFile;
import com.dcas.common.domain.entity.CoLicense;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.common.model.other.Activecode;
import com.dcas.common.model.vo.LoadLicenseVo;
import com.dcas.common.model.vo.QueryLicenseVo;
import com.dcas.system.filter.LicenseFileFilter;
import com.dcas.common.mapper.CoFileMapper;
import com.dcas.common.mapper.CoLicenseMapper;
import com.dcas.system.service.CoLicenseService;
import com.dcas.system.service.ISysConfigService;
import com.dcas.system.service.TerminalService;
import com.mchz.dcas.client.DcasCocClient;
import com.mchz.dcas.client.model.request.TerminalHeartRequest;
import com.mchz.dcas.client.model.response.TerminalHeartResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * license实现类
 *
 * <AUTHOR>
 * @Date 2022/11/18 18:06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoLicenseServiceImpl extends ServiceImpl<CoLicenseMapper, CoLicense> implements CoLicenseService {

    private final DcasCocClient dcasCocClient;
    private final CoFileMapper coFileMapper;
    private final CoLicenseMapper coLicenseMapper;
    private final TerminalService terminalService;
    private final ISysConfigService sysConfigService;
    private final SecurityOperationMapper securityOperationMapper;
    private final CoOperationMapper coOperationMapper;
    private static final String LICENSE_QUERY_API_HOST = "https://127.0.0.1:11443/api/mcbp/license/1.0.0/activestate/query";
    private static final String LICENSE_LOAD_API_HOST = "https://127.0.0.1:11443/api/mcbp/license/1.0.0/licensefile/load";
    /**
     * 知识库授权key
     */
    private static final String LIBRARY_GRANT_KEY = "libraryGrant";
    /**
     * 系统升级授权key
     */
    private static final String UPGRADE_GRANT_KEY = "upgradeGrant";
    /**
     * 客户端授权key
     */
    private static final String CUSTOMNAME_KEY = "customName";
    /**
     * 系统版本类型授权key
     */
    private static final String EDITION_KEY = "edition";
    /**
     * 作业数量限制key
     */
    private static final String JOB_NUM_KEY = "jobNum";

    /**
     * 查询激活状态(成功或失败都会返回机器码)
     *
     * @param lncSerialId request
     * @return * @return String
     * @Date 2022/11/18 18:05
     */
    @Override
    public QueryLicenseVo queryLicense(String lncSerialId) {
        String backString = null;
        try {
            backString = this.getLicenseResult(lncSerialId);
        } catch (IOException e) {
            throw new FailParamsException("请求license服务异常");
        }
        JSONObject responseJson = JSON.parseObject(backString);
        JSONObject data = responseJson.getJSONObject("data");
        String deviceId = data.getString("deviceId");

        QueryLicenseVo vo = new QueryLicenseVo();
        vo.setActive(data.getBoolean("active"));
        vo.setActiveCode(data.getInteger("activecode"));
        vo.setActiveMsg(data.getString("activemsg"));
        vo.setActiveMethod(data.getString("activeMethod"));
        vo.setDeviceId(deviceId);
        vo.setAuthorTimeExcess(data.getInteger("authorTimeExcess"));
        vo.setAuthorTimeStart(data.getInteger("authorTimeStart"));
        vo.setAuthorTimeEnd(data.getInteger("authorTimeEnd"));
        vo.setLncType(data.getString("lncType"));
        vo.setLncDesc(data.getString("lncDesc"));
        vo.setLncSerialId(data.getString("lncSerialId"));
        return vo;
    }

    @Override
    public String queryMachineCode() {
        SysConfig config = sysConfigService.selectConfigById(SysConfigEnum.MACHINE_CODE.getCode());
        if (Objects.isNull(config) || StrUtil.isEmpty(config.getConfigValue())) {
            throw new ServiceException("统一License服务异常");
        }
        return config.getConfigValue();
    }

    /**
     * 查询许可证表
     *
     * @param dto 激活码
     * @return * @return CoLicense
     * @Date 2022/11/18 18:05
     */
    @Override
    public LoadLicenseVo queryLicenseResult(RequestModel<CoLicense> dto) {
        //查询许可证资料表
        QueryWrapper<CoLicense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_id", dto.getPrivator().getDeviceId());
        queryWrapper.isNotNull("attribute");
        List<CoLicense> coLicenseList = coLicenseMapper.selectList(queryWrapper);
        if (Objects.isNull(coLicenseList)){
            throw new ServiceException("您的证书无效，请检查是否取得授权！");
        }
        LoadLicenseVo vo = new LoadLicenseVo();
        for (CoLicense coLicense : coLicenseList) {
            if (AttributeEnum.CLIENT_GRANT.getAttribute().equals(coLicense.getAttribute())) {
                if (DateUtil.currentSeconds() > coLicense.getAuthorTimeEnd()) {
                    throw new ServiceException("您的终端证书已过期，请重新申请！");
                }
                vo.setActive(coLicense.getActive());
                vo.setActiveCode(coLicense.getActiveCode());
                vo.setDeviceId(coLicense.getDeviceId());
                vo.setLncSerialId(coLicense.getLncSerialId());
                vo.setAuthorTimeExcess(coLicense.getAuthorTimeExcess());
                vo.setMaintainTimeExcess(coLicense.getMaintainTimeExcess());
                vo.setAuthorTimeStart(coLicense.getAuthorTimeStart());
                vo.setAuthorTimeEnd(coLicense.getAuthorTimeEnd());
                vo.setLocked(coLicense.getLocked());
                vo.setExpired(coLicense.getExpired());
            } else if (AttributeEnum.UPGRADE_GRANT.getAttribute().equals(coLicense.getAttribute())){
                vo.setUpgradeGrantTimeEnd(coLicense.getAuthorTimeEnd());
            } else if ( AttributeEnum.LIBRARY_GRANT.getAttribute().equals(coLicense.getAttribute())){
                vo.setLibraryGrantTimeEnd(coLicense.getAuthorTimeEnd());
            }
            vo.setCustomName(coLicense.getCustomName());
        }

        // 获取剩余作业数量
        String jobNumStr = sysConfigService.selectConfigByKey(SysConfigEnum.JOB_NUM.getKey());
        if (CharSequenceUtil.isNotEmpty(jobNumStr)) {
            Integer securityJobCount = securityOperationMapper.selectCount(null);
            Integer operationJobCount = coOperationMapper.selectCount(null);
            vo.setOperationJobNum(Integer.parseInt(jobNumStr) - operationJobCount);
            vo.setSecurityJobNum(Integer.parseInt(jobNumStr) - securityJobCount);
        }
        return vo;
    }

    /**
     * 上传license文件
     *
     * @param file request
     * @return * @return Map<Object>
     * @Date 2022/6/6 10:16
     */
    @Override
    public Map<String, Object> uploadFile(MultipartFile file) throws IOException {
        // 上传文件路径
        String uploadPath = "./data/storage/license";
        // 上传并返回新文件路径
        String filePath = FileUploadUtils.uploadLicense(uploadPath, file);
        String url = getUrl() + filePath;

        Map<String, Object> result = new HashMap<>(1, 2);
        String id = SnowFlake.getId();
        result.put("fileId", id);
        result.put("url", url);
        result.put("filePath", filePath);
        result.put("originalFilename", file.getOriginalFilename());
        result.put("newFileName", FileUtils.getName(filePath));

        //存储到文件表
        CoFile coFile = new CoFile();
        coFile.setFileId(id);
        coFile.setUrl(url);
        coFile.setFilePath(filePath);
        coFile.setOriginalFileName(file.getOriginalFilename());
        coFile.setNewFileName(FileUtils.getName(filePath));
        coFileMapper.add(coFile);
        return result;
    }

    /**
     * 加载license文件
     *
     * @param type 文件类型
     * @param path 文件路径
     */
    @Override
    public LoadLicenseVo loadLicense(Integer type, String path) {
        path = "/opt/mc/apps/dcas_se/current"+path;
        LoadLicenseVo vo = new LoadLicenseVo();
        if ("zip".equals(FileUtil.getSuffix(path))){
            File file = ZipUtil.unzip(path, StandardCharsets.UTF_8);
            List<File> files = FileUtil.loopFiles(file, new LicenseFileFilter());
            if (files == null || files.size() == 0){
                throw new ServiceException("找不到正确license文件，请确认包中存在lsc后缀的文件!");
            }
            String deviceId = null;
            Map<String, CoLicense> fileMap = new HashMap<>(16);
            for (File f : files){
                JSONObject licenseData = doLoad(type, f.getAbsolutePath());
                String productExtern = licenseData.getString("productExtern");

                if (StrUtil.isEmpty(productExtern)) {
                    throw new ServiceException("特性字段为空，请检查该产品license特性配置!");
                }
                // productExtern为json类型字符串，获取其中属性为customName的值
                JSONObject productExternJson = JSON.parseObject(productExtern);
                if (productExternJson.containsKey(CUSTOMNAME_KEY)){
                    String customName = productExternJson.getString("customName");
                    String lncType = productExternJson.getString("lncType");
                    CoLicense coLicense = buildLicenseData(customName, licenseData, lncType);
                    coLicense.setAttribute(AttributeEnum.CLIENT_GRANT.getAttribute());
                    fileMap.put(CUSTOMNAME_KEY, coLicense);

                    // 更新系统配置
                    String editVersion = productExternJson.getString(EDITION_KEY);
                    Integer jobNum = productExternJson.getInteger(JOB_NUM_KEY);
                    saveOrUpdateSysConfig(editVersion, jobNum);

                    vo.setActive(coLicense.getActive());
                    vo.setActiveCode(coLicense.getActiveCode());
                    vo.setActiveMsg(licenseData.getString("activemsg"));
                    vo.setActiveMethod(licenseData.getString("activeMethod"));
                    vo.setDeviceId(coLicense.getDeviceId());
                    vo.setLncSerialId(coLicense.getLncSerialId());
                    vo.setAuthorTimeExcess(coLicense.getAuthorTimeExcess());
                    vo.setMaintainTimeExcess(coLicense.getMaintainTimeExcess());
                    vo.setAuthorTimeStart(coLicense.getAuthorTimeStart());
                    vo.setAuthorTimeEnd(coLicense.getAuthorTimeEnd());
                } else if (productExternJson.containsKey(LIBRARY_GRANT_KEY)){
                    //知识库授权
                    vo.setLibraryGrantTimeEnd(licenseData.getLong("authorTimeEnd"));
                    CoLicense coLicense = buildLicenseData(null, licenseData, null);
                    coLicense.setAttribute(AttributeEnum.LIBRARY_GRANT.getAttribute());
                    fileMap.put(LIBRARY_GRANT_KEY, coLicense);

                } else if (productExternJson.containsKey(UPGRADE_GRANT_KEY)){
                    //系统升级授权
                    CoLicense coLicense = buildLicenseData(null, licenseData, null);
                    coLicense.setAttribute(AttributeEnum.UPGRADE_GRANT.getAttribute());
                    fileMap.put(UPGRADE_GRANT_KEY, coLicense);
                    vo.setUpgradeGrantTimeEnd(licenseData.getLong("authorTimeEnd"));
                }
            }

            // 按终端授权-升级维保授权-知识库授权排序
            List<CoLicense> sortList = new ArrayList<>();
            sortList.add(fileMap.get(CUSTOMNAME_KEY));
            sortList.add(fileMap.get(UPGRADE_GRANT_KEY));
            sortList.add(fileMap.get(LIBRARY_GRANT_KEY));

            for (CoLicense coLicense : sortList){
                if (AttributeEnum.CLIENT_GRANT.getAttribute().equals(coLicense.getAttribute())){
                    doSaveOrUpdate(coLicense);
                    // 根据维保时间同时生成知识库授权和维保授权
                    doUpdateLibraryGrantAndUpgradeGrant(coLicense);
                } else if (AttributeEnum.UPGRADE_GRANT.getAttribute().equals(coLicense.getAttribute())){
                    checkDependencyGrant(coLicense.getDeviceId(), AttributeEnum.CLIENT_GRANT);
                    doSaveOrUpdate(coLicense);
                } else if (AttributeEnum.LIBRARY_GRANT.getAttribute().equals(coLicense.getAttribute())){
                    checkDependencyGrant(coLicense.getDeviceId(), AttributeEnum.UPGRADE_GRANT);
                    doSaveOrUpdate(coLicense);
                }
                deviceId = coLicense.getDeviceId();
            }
            doSendTerminalHearth(deviceId);
        } else {
            JSONObject licenseData = doLoad(type, path);
            String productExtern = licenseData.getString("productExtern");
            if (StrUtil.isEmpty(productExtern)) {
                throw new ServiceException("特性字段为空，请检查该产品license特性配置!");
            }
            // productExtern为json类型字符串，获取其中属性为customName的值
            JSONObject productExternJson = JSON.parseObject(productExtern);
            if (productExternJson.containsKey(CUSTOMNAME_KEY)){
                String customName = productExternJson.getString("customName");
                String lncType = productExternJson.getString("lncType");
                CoLicense coLicense = buildLicenseData(customName, licenseData, lncType);
                coLicense.setAttribute(AttributeEnum.CLIENT_GRANT.getAttribute());
                vo = queryForBuildLoadLicenseVO(coLicense, AttributeEnum.CLIENT_GRANT);
                doSaveOrUpdate(coLicense);

                // 更新系统配置
                String editVersion = productExternJson.getString(EDITION_KEY);
                Integer jobNum = productExternJson.getInteger(JOB_NUM_KEY);
                saveOrUpdateSysConfig(editVersion, jobNum);

                // 根据维保时间同时生成知识库授权和维保授权
                doUpdateLibraryGrantAndUpgradeGrant(coLicense);

                // 发送终端心跳
                doSendTerminalHearth(coLicense.getDeviceId());
            } else if (productExternJson.containsKey(LIBRARY_GRANT_KEY)){
                checkDependencyGrant(licenseData.getString("deviceId"), AttributeEnum.UPGRADE_GRANT);
                //知识库授权
                CoLicense coLicense = buildLicenseData(null, licenseData, null);
                coLicense.setAttribute(AttributeEnum.LIBRARY_GRANT.getAttribute());
                doSaveOrUpdate(coLicense);
                vo = queryForBuildLoadLicenseVO(coLicense, AttributeEnum.LIBRARY_GRANT);
                vo.setLibraryGrantTimeEnd(licenseData.getLong("authorTimeEnd"));
                // 发送终端心跳
                doSendTerminalHearth(coLicense.getDeviceId());
            } else if (productExternJson.containsKey(UPGRADE_GRANT_KEY)){
                checkDependencyGrant(licenseData.getString("deviceId"), AttributeEnum.CLIENT_GRANT);
                //系统升级授权
                CoLicense coLicense = buildLicenseData(null, licenseData, null);
                coLicense.setAttribute(AttributeEnum.UPGRADE_GRANT.getAttribute());
                doSaveOrUpdate(coLicense);
                vo = queryForBuildLoadLicenseVO(coLicense, AttributeEnum.UPGRADE_GRANT);
                vo.setUpgradeGrantTimeEnd(licenseData.getLong("authorTimeEnd"));
                // 发送终端心跳
                doSendTerminalHearth(coLicense.getDeviceId());
            }
        }
        return vo;
    }

    private void saveOrUpdateSysConfig(String editionVersion, Integer jobNum) {
        // 系统配置表增加基础版标识
        if (CharSequenceUtil.isNotEmpty(editionVersion)) {
            sysConfigService.saveOrUpdateSysEdition(editionVersion, null);
        }
        // 系统配置表增加license类型标识
        if (Objects.nonNull(jobNum)){
            sysConfigService.saveOrUpdateJobNum(jobNum, null);
        }
    }

    private void doUpdateLibraryGrantAndUpgradeGrant(CoLicense coLicense) {
        // 根据维保时长新增知识库授权和系统升级授权
        CoLicense libraryCoLicense = new CoLicense();
        BeanUtils.copyProperties(coLicense, libraryCoLicense, "licenseId");
        libraryCoLicense.setAuthorTimeExcess(coLicense.getMaintainTimeExcess());
        libraryCoLicense.setAttribute(AttributeEnum.LIBRARY_GRANT.getAttribute());
        // 设置知识库授权截至日期
        setMaintainTimeEnd(libraryCoLicense, coLicense);
        doSaveOrUpdate(libraryCoLicense);

        CoLicense upgradeCoLicense = new CoLicense();
        BeanUtils.copyProperties(coLicense, upgradeCoLicense, "licenseId");
        upgradeCoLicense.setAuthorTimeExcess(coLicense.getMaintainTimeExcess());
        upgradeCoLicense.setAttribute(AttributeEnum.UPGRADE_GRANT.getAttribute());
        // 设置升级维保授权截至日期
        setMaintainTimeEnd(upgradeCoLicense, coLicense);
        doSaveOrUpdate(upgradeCoLicense);
    }

    private void setMaintainTimeEnd(CoLicense copyCoLicense, CoLicense coLicense) {
        // 如果没有维保时间且终端授权少于一年，则知识库授权与维保授权跟终端授权保持一致
        // 如果终端授权超过一年或者永久，则知识库授权与维保授权期限为一年
        // 20230108 改：第一次激活终端授权存在维保时间，则为知识库和升级维保授权时间保持一致
        // 以上三个逻辑全生效
        if (coLicense.getMaintainTimeExcess() != null && coLicense.getMaintainTimeExcess() != 0){
            copyCoLicense.setAuthorTimeEnd(coLicense.getAuthorTimeStart() + TimeUnit.DAYS.toSeconds(coLicense.getMaintainTimeExcess()));
        } else if (coLicense.getAuthorTimeExcess() <= 365){
            copyCoLicense.setAuthorTimeEnd(coLicense.getAuthorTimeEnd());
        } else {
            copyCoLicense.setAuthorTimeEnd(coLicense.getAuthorTimeStart() + TimeUnit.DAYS.toSeconds(365));
        }
    }

    private void checkDependencyGrant(String deviceId, AttributeEnum attributeEnum) {
        QueryWrapper<CoLicense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_id", deviceId);
        queryWrapper.eq("attribute", attributeEnum.getAttribute());
        CoLicense coLicense = coLicenseMapper.selectOne(queryWrapper);

        if (coLicense == null || !coLicense.getActive() || coLicense.getAuthorTimeEnd() <= DateUtil.currentSeconds()){
            if (AttributeEnum.UPGRADE_GRANT == attributeEnum){
                throw new ServiceException("需先进行维保授权！");
            }
            if (AttributeEnum.CLIENT_GRANT == attributeEnum){
                throw new ServiceException("需先客户端授权！");
            }
        }
    }

    @Override
    public void doSendTerminalHearth(String deviceId) {
        QueryWrapper<CoLicense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_id", deviceId);
        queryWrapper.eq("attribute", AttributeEnum.CLIENT_GRANT.getAttribute());
        CoLicense coLicense = coLicenseMapper.selectOne(queryWrapper);
        if (coLicense == null){
            log.warn("查询不到deviceId={}license信息",deviceId);
            return;
        }
        TerminalHeartRequest terminalHeartRequest = terminalService.buildHeartRequest(1);
        try {
            TerminalHeartResponse heart = dcasCocClient.heart(terminalHeartRequest);
            coLicense.setLocked(heart.getLocked());
            coLicense.setExpired(heart.getExpired());
            coLicenseMapper.update(coLicense, queryWrapper);
        } catch (Exception e) {
            log.error("【license激活】发送心跳失败：{}", e.getMessage());
        }
    }

    private LoadLicenseVo queryForBuildLoadLicenseVO(CoLicense license, AttributeEnum attributeEnum) {
        QueryWrapper<CoLicense> queryWrapper = new QueryWrapper<>();
        LoadLicenseVo vo = new LoadLicenseVo();
        if (AttributeEnum.LIBRARY_GRANT == attributeEnum){
            queryWrapper.eq("device_id", license.getDeviceId());
            queryWrapper.eq("attribute", AttributeEnum.CLIENT_GRANT.getAttribute())
                .or().eq("attribute", AttributeEnum.UPGRADE_GRANT.getAttribute());
            List<CoLicense> coLicenseList = coLicenseMapper.selectList(queryWrapper);
            for (CoLicense coLicense : coLicenseList){
                if (AttributeEnum.CLIENT_GRANT.getAttribute().equals(coLicense.getAttribute())){
                    vo.setActive(coLicense.getActive());
                    vo.setActiveCode(coLicense.getActiveCode());
                    vo.setDeviceId(coLicense.getDeviceId());
                    vo.setLncSerialId(coLicense.getLncSerialId());
                    vo.setAuthorTimeExcess(coLicense.getAuthorTimeExcess());
                    vo.setMaintainTimeExcess(coLicense.getMaintainTimeExcess());
                    vo.setAuthorTimeStart(coLicense.getAuthorTimeStart());
                    vo.setAuthorTimeEnd(coLicense.getAuthorTimeEnd());
                } else if (AttributeEnum.UPGRADE_GRANT.getAttribute().equals(coLicense.getAttribute())){
                    vo.setUpgradeGrantTimeEnd(coLicense.getAuthorTimeEnd());
                }
            }
        } else if (AttributeEnum.UPGRADE_GRANT == attributeEnum){
            queryWrapper.eq("device_id", license.getDeviceId());
            queryWrapper.eq("attribute", AttributeEnum.CLIENT_GRANT.getAttribute())
                .or().eq("attribute", AttributeEnum.LIBRARY_GRANT.getAttribute());
            List<CoLicense> coLicenseList = coLicenseMapper.selectList(queryWrapper);
            for (CoLicense coLicense : coLicenseList){
                if (AttributeEnum.CLIENT_GRANT.getAttribute().equals(coLicense.getAttribute())){
                    vo.setActive(coLicense.getActive());
                    vo.setActiveCode(coLicense.getActiveCode());
                    vo.setDeviceId(coLicense.getDeviceId());
                    vo.setLncSerialId(coLicense.getLncSerialId());
                    vo.setAuthorTimeExcess(coLicense.getAuthorTimeExcess());
                    vo.setMaintainTimeExcess(coLicense.getMaintainTimeExcess());
                    vo.setAuthorTimeStart(coLicense.getAuthorTimeStart());
                    vo.setAuthorTimeEnd(coLicense.getAuthorTimeEnd());
                } else if (AttributeEnum.LIBRARY_GRANT.getAttribute().equals(coLicense.getAttribute())){
                    vo.setLibraryGrantTimeEnd(coLicense.getAuthorTimeEnd());
                }
            }
        } else if (AttributeEnum.CLIENT_GRANT == attributeEnum){
            vo.setActive(license.getActive());
            vo.setActiveCode(license.getActiveCode());
            vo.setDeviceId(license.getDeviceId());
            vo.setLncSerialId(license.getLncSerialId());
            vo.setAuthorTimeExcess(license.getAuthorTimeExcess());
            vo.setMaintainTimeExcess(license.getMaintainTimeExcess());
            vo.setAuthorTimeStart(license.getAuthorTimeStart());
            vo.setAuthorTimeEnd(license.getAuthorTimeEnd());

            queryWrapper.eq("device_id", license.getDeviceId());
            queryWrapper.eq("attribute", AttributeEnum.UPGRADE_GRANT.getAttribute())
                .or().eq("attribute", AttributeEnum.LIBRARY_GRANT.getAttribute());
            List<CoLicense> coLicenseList = coLicenseMapper.selectList(queryWrapper);
            for (CoLicense coLicense : coLicenseList){
                if (AttributeEnum.UPGRADE_GRANT.getAttribute().equals(coLicense.getAttribute())){
                    vo.setUpgradeGrantTimeEnd(coLicense.getAuthorTimeEnd());
                } else if (AttributeEnum.LIBRARY_GRANT.getAttribute().equals(coLicense.getAttribute())){
                    vo.setLibraryGrantTimeEnd(coLicense.getAuthorTimeEnd());
                }
            }
        }
        return vo;
    }

    private void doSaveOrUpdate(CoLicense coLicense) {
        // 查询coLicense表，为空值则去新增
        QueryWrapper<CoLicense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_id", coLicense.getDeviceId());
        queryWrapper.eq("attribute", coLicense.getAttribute());
        List<CoLicense> list = coLicenseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            // 机器码不为空，并且识别码不是空（返回识别码说明调用license组件加载文件接口成功）
            if (StringUtils.isNotBlank(coLicense.getDeviceId()) && StringUtils.isNotBlank(coLicense.getLncSerialId())) {
                this.save(coLicense);
            }
        } else {
            coLicenseMapper.update(coLicense, queryWrapper);
        }
    }

    private CoLicense buildLicenseData(String customName, JSONObject licenseData, String lncType) {
        assert licenseData != null;
        Boolean active = licenseData.getBoolean("active");
        Byte activeCode = licenseData.getByte("activecode");
        String deviceId = licenseData.getString("deviceId");
        String lncSerialId = licenseData.getString("lncSerialId");
        int authorTimeExcess = licenseData.getInteger("authorTimeExcess");
        int maintainTimeExcess = licenseData.getInteger("maintainTimeExcess");
        long authorTimeStart = licenseData.getLong("authorTimeStart");
        long authorTimeEnd = licenseData.getLong("authorTimeEnd");

        CoLicense coLicense = new CoLicense();
        coLicense.setDeviceId(deviceId);
        coLicense.setLncSerialId(lncSerialId);
        coLicense.setActive(active);
        coLicense.setActiveCode(activeCode);
        coLicense.setAuthorTimeExcess(authorTimeExcess);
        coLicense.setMaintainTimeExcess(maintainTimeExcess);
        coLicense.setAuthorTimeStart(authorTimeStart);
        coLicense.setAuthorTimeEnd(authorTimeEnd);
        coLicense.setActivationTime(DateUtil.date());
        coLicense.setCustomName(customName);
        coLicense.setLncType(lncType);
        return coLicense;
    }

    private JSONObject doLoad(Integer type, String path) {
        String loadResult = null;
        try {
            loadResult = this.getLoadResult(type, path);
        } catch (Exception e) {
            throw new ServiceException(String.format("加载license文件异常，msg：%s", e.getMessage()));
        }
        JSONObject responseJson = JSON.parseObject(loadResult);
        int code = responseJson.getInteger("code");
        if (code != 0) {
            throw new ServiceException(String.format("加载license文件异常：%s", responseJson.getString("message")));
        }
        JSONObject data = responseJson.getJSONObject("data");
        Boolean active = data.getBoolean("active");
        Byte activeCode = data.getByte("activecode");
        if (active == null || !active){
            String desc = "激活异常";
            if(activeCode != null){
                desc = Activecode.getDescByCode(activeCode);
            }
            throw new ServiceException(desc);
        }
        return data;
    }

    public String getUrl() {
        HttpServletRequest request = ServletUtils.getRequest();
        return getDomain(request);
    }

    public static String getDomain(HttpServletRequest request) {
        StringBuffer url = request.getRequestURL();
        String contextPath = request.getServletContext().getContextPath();
        return url.delete(url.length() - request.getRequestURI().length(), url.length()).append(contextPath).toString();
    }


    /**
     * 获取激活状态
     *
     * @param lncSerialId request
     * @return * @return String
     * @Date 2022/11/22 12:00
     */
    public String getLicenseResult(String lncSerialId) throws IOException {
        return HttpPost.doPost(LICENSE_QUERY_API_HOST, buildParams(lncSerialId));
    }

    /**
     * 根据API文档封装调用方法，将参数组装程接口所需要的json
     *
     * @param lncSerialId request
     * @return * @return String
     * @Date 2022/11/18 19:25
     */
    private String buildParams(String lncSerialId) {
        String a = "{\"lncSerialId\":\"";
        String d = "\"\r\n" + "}";
        String requestJson = a + lncSerialId + d;
        return requestJson;
    }

    /**
     * 加载license
     *
     * @param type
     * @param path request
     * @return * @return String
     * @Date 2022/11/22 12:00
     */
    public String getLoadResult(Integer type, String path) throws IOException {
        log.info("----------------buildParams(type, path):" + buildParams(type, path));
        return HttpPost.doPost(LICENSE_LOAD_API_HOST, buildParams(type, path));
    }

    private String buildParams(Integer type, String path) {
        String a = "{\"type\":";
        String b = ",\"path\":\"";
        String d = "\"\r\n" + "}";
        String requestJson = a + type + b + path + d;
        return requestJson;
    }

    public static void main(String[] args) {
//        String path = "E:\\Users\\arcvi\\download\\dcas__license-dcas123.zip";
//        File file = ZipUtil.unzip(path, StandardCharsets.UTF_8);
//        List<File> files = FileUtil.loopFiles(file, new LicenseFileFilter());
        String productExtern = "{\"edition\":\"BE\",\"lncType\":\"ONTRAIL\",\"customName\":\"1818黄金眼\"}";
        JSONObject productExternJson = JSON.parseObject(productExtern);
        String edition = productExternJson.getString(EDITION_KEY);
        System.out.println(edition);
    }

}
