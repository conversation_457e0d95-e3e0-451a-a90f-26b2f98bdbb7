package com.dcas.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.AssessModuleType;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.enums.DatabaseJobType;
import com.dcas.common.enums.JobStatusEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.*;
import com.dcas.common.utils.Func;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.discovery.service.impl.DiscoveryJobServiceImpl;
import com.dcas.discovery.service.impl.SourceConfigServiceImpl;
import com.dcas.common.model.req.DataBaseWorkAddReq;
import com.dcas.common.model.vo.DbSecurityResultVO;
import com.dcas.system.service.CoDbSecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/12/26 14:26
 * @since 1.0.1
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoDbSecurityServiceImpl extends ServiceImpl<CoDbSecurityMapper, CoDbSecurity> implements CoDbSecurityService {

    private final CoDbSecurityMapper coDbSecurityMapper;
    private final SourceConfigServiceImpl sourceConfigService;
    private final ScanTaskMapper scanTaskMapper;
    private final DiscoveryJobMapper discoveryJobMapper;
    private final DiscoveryJobServiceImpl discoveryJobServiceImpl;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final DiscoverySourceMapper discoverySourceMapper;

    @Override
    public void create(DataBaseWorkAddReq req) {
        SourceConfig sourceConfig = sourceConfigService.getById(req.getConfigId());
        if (Objects.isNull(sourceConfig))
            throw new ServiceException("数据源配置不存在");
        List<CoDbSecurity> list = StrUtil.split(req.getJobType(), StrUtil.COMMA).stream().map(j -> CoDbSecurity.builder()
                .name(req.getJobName())
                .operationId(sourceConfig.getOperationId())
                .configId(req.getConfigId())
                .jobType(DatabaseJobType.valueOf(j).getCode())
                .dbType(sourceConfig.getConfigType())
                .status(JobStatusEnum.UN_START.getCode())
                .createBy(SecurityUtils.getAccount())
                .createAt(DateUtil.date())
                .build()).collect(Collectors.toList());
        this.saveBatch(list);
    }

    @Override
    public List<DbSecurityResultVO> listAll(String operationId, String jobType) {
        DatabaseJobType databaseJobType;
        try {
            databaseJobType = DatabaseJobType.valueOf(jobType);
        } catch (IllegalArgumentException e) {
            throw new ServiceException(String.format("作业类型%s不存在", jobType));
        }
        List<DbSecurityResultVO> res = coDbSecurityMapper.queryAll(operationId, databaseJobType.getCode());
        res.forEach(r -> {
            r.setFileName(DataSourceType.getType(r.getDbType()).getName() + "查询脚本");
        });
        return res;
    }

    @Override
    public void delete(long id, String type, String abilityType) {
        if (Func.isNullStr(type) && Func.isNullStr(abilityType)) {
            ScanTask scanTask = scanTaskMapper.selectById(id);
            if (Objects.nonNull(scanTask)) {
                if (scanTask.getStatus() == 2)
                    throw new ServiceException("运行中的作业无法删除");
                scanTaskMapper.deleteById(id);
            }
        } else if (StrUtil.isNotEmpty(type)) {
            long configId = -1L;
            AssessModuleType moduleType = AssessModuleType.valueOf(type);
            switch (moduleType) {
                case ASSET:
                    DiscoveryJob discoveryJob = discoveryJobMapper.selectById(id);
                    configId = discoverySourceMapper.selectOne(new QueryWrapper<DiscoverySource>().eq("plan_id", discoveryJob.getPlanId())).getSourceId();
                    discoveryJobServiceImpl.stopJob(id);
                    discoveryJobServiceImpl.deleteDiscoveryConfigByJobId(discoveryJob.getPlanId());
                    break;
                case AUTH:
                case BASIC:
                    CoDbSecurity bean = coDbSecurityMapper.selectById(id);
                    if (Objects.nonNull(bean)) {
                        if (Objects.equals(JobStatusEnum.RUNNING.getCode(), bean.getStatus()))
                            throw new ServiceException("运行中的作业无法删除");
                        configId = bean.getConfigId();
                        coDbSecurityMapper.deleteById(id);
                    }
                    break;
            }
            // 如果当前数据源下没有资产作业、权限查询作业、基础评估作业，则该数据源连接可删除
            if (configId != -1) {
                Integer c1 = discoverySourceMapper.selectCount(new QueryWrapper<DiscoverySource>().eq("source_id", configId));
                if (c1 == 0) {
                    Integer c2 = coDbSecurityMapper.selectCount(new QueryWrapper<CoDbSecurity>().eq("config_id", configId));
                    if (c2 == 0) {
                        UpdateWrapper<PreSourceConfig> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.set("current_step", 2);
                        updateWrapper.eq("config_id", configId);
                        preSourceConfigMapper.update(null, updateWrapper);
                    }
                }
            }
        } else if (StrUtil.isNotEmpty(abilityType)) {
            // TODO 能力市场作业删除
            throw new ServiceException("能力市场作业暂不支持删除");
        }
    }

    @Override
    public CoDbSecurity detail(long id) {
        CoDbSecurity bean = coDbSecurityMapper.selectById(id);
        if (Objects.isNull(bean))
            throw new ServiceException("作业不存在");
        return bean;
    }

    @Override
    public void updateStartTime(long id, int status, Date date) {
        CoDbSecurity bean = coDbSecurityMapper.selectById(id);
        if (Objects.isNull(bean))
            throw new ServiceException("作业不存在");
        bean.setStatus(status);
        bean.setStartTime(date);
        bean.setUpdateAt(new Date());
//        bean.setUpdateBy(SecurityUtils.getUsername());
        coDbSecurityMapper.updateById(bean);
    }

    @Override
    public void updateEndTime(long id, int status, Date date) {
        CoDbSecurity bean = coDbSecurityMapper.selectById(id);
        if (Objects.isNull(bean))
            throw new ServiceException("作业不存在");
        bean.setStatus(status);
        bean.setStopTime(date);
        bean.setErrInfo(StrUtil.EMPTY);
        bean.setUpdateAt(new Date());
//        bean.setUpdateBy(SecurityUtils.getUsername());
        coDbSecurityMapper.updateById(bean);
    }

    @Override
    public void updateErrorMsg(long id, int status, String errorMsg) {
        CoDbSecurity bean = coDbSecurityMapper.selectById(id);
        if (Objects.isNull(bean))
            throw new ServiceException("作业不存在");
        bean.setErrInfo(errorMsg);
        bean.setStatus(status);
        bean.setUpdateAt(new Date());
//        bean.setUpdateBy(SecurityUtils.getUsername());
        coDbSecurityMapper.updateById(bean);
    }

    @Override
    public void updateStatus(long id, int status) {
        CoDbSecurity bean = coDbSecurityMapper.selectById(id);
        if (Objects.isNull(bean))
            throw new ServiceException("作业不存在");
        bean.setStatus(status);
        bean.setUpdateAt(new Date());
//        bean.setUpdateBy(SecurityUtils.getUsername());
        coDbSecurityMapper.updateById(bean);
    }
}
