package com.dcas.system.service;

import com.dcas.common.model.dto.CommonDto;
import com.dcas.common.model.dto.FileLocateDTO;
import com.dcas.common.model.dto.ItemAbilityDTO;
import com.dcas.common.model.param.FileRelationParam;
import com.dcas.common.model.req.AbilityItemReq;
import com.dcas.common.model.req.QuestionnaireContentReq;
import com.dcas.common.model.vo.ItemAbilityVO;
import com.dcas.common.model.vo.OnlineQuestionContentVO;
import com.dcas.common.model.vo.QuestionnaireContentVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/14 15:11
 * @since 1.2.0
 */
public interface QuestionnaireService {

    /**
     * 查询调研问卷
     */
    QuestionnaireContentVO selectQuestionnaire(CommonDto dto);

    Map<String, Set<FileLocateDTO>> itemFileRelation(FileRelationParam param);

    /**
     * 问卷保存
     */
    void saveAndUpdate(QuestionnaireContentReq req);

    void importForTemp(QuestionnaireContentReq req);

    /**
     * 离线问卷导出
     *
     * @param operationId 作业id
     * @param response 响应
     */
    void offlineQuestionnaireExport(String operationId, HttpServletResponse response);

    void offlineQuestionnaireImport(String operationId, MultipartFile file);

    String onlineQuestionnaireGenerate(String operationId, Integer jobType);

    OnlineQuestionContentVO selectOnlineQuestionnaire(CommonDto dto);

    void onlineQuestionnaireSave(OnlineQuestionContentVO vo);

    void onlineQuestionnaireImport(String operationId);

    ItemAbilityVO queryAbilityItem(AbilityItemReq req);

    List<ItemAbilityDTO> selectItemAbilityWithLifecycleTag(String operationId);
}
