package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.hash.MurmurHash;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.enums.TemplateAlgorithm;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.Func;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.file.FileUtils;
import com.dcas.common.model.dto.TemplateGenerateDTO;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.excel.TemplateExcel;
import com.dcas.common.model.req.*;
import com.dcas.common.model.vo.TemplateContentVO;
import com.dcas.common.model.vo.TemplateQuestionVO;
import com.dcas.common.mapper.*;
import com.dcas.system.producer.TemplateProduceContext;
import com.dcas.system.service.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/13 14:38
 * @since 1.2.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateServiceImpl extends ServiceImpl<TemplateMapper, Template> implements TemplateService {

    private static final String ITEM_ID = "00000000000000000000000000000000";

    private final TagService tagService;
    private final ItemMapper itemMapper;
    private final TemplateMapper templateMapper;
    private final ItemService itemService;
    private final QuestionService questionService;
    private final QuestionMapper questionMapper;
    private final TemplateContentMapper templateContentMapper;
    private final QuestionSortingMapper questionSortingMapper;
    private final TemplateContentService templateContentService;
    private final QuestionSortingServiceImpl questionSortingService;

    @Override
    public List<Template> listAll() {
        QueryWrapper<Template> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc( "id");
        return templateMapper.selectList(queryWrapper);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void addTemplate(TemplateInsertReq req) {
        Template template = add(req);
        switch (req.getContentType()) {
            case 1 :
                produceByAlgorithm(template, req.getKeyword());
                return;
            case 2 :
                if (StrUtil.isEmpty(req.getUrl()))
                    throw new ServiceException("文件路径不能为空");
                MultipartFile file = FileUtils.createMultipartFile(req.getUrl());
                produceWithFile(template, file);
                return;
            default :
                throw new UnsupportedOperationException("不支持的操作类型");
        }
    }

    private void produceWithFile(Template template, MultipartFile file) throws IOException {
        List<TemplateExcel> templateExcels = Func.fileAttestation(file, TemplateExcel.class, null, true);
        Map<Long, Question> questionMap = questionService.listAll().stream().collect(
                Collectors.toMap(q -> MurmurHash.hash64(q.getTitle()), q -> q));
        Map<Long, Item> itemMap = itemService.listAll().stream().collect(
                Collectors.toMap(i -> MurmurHash.hash64(i.getTitle()), i -> i));
        // 排除不在系统中的问题和选项匹配列
        Iterator<TemplateExcel> iterator = templateExcels.iterator();
        while (iterator.hasNext()) {
            TemplateExcel excel = iterator.next();
            if (StrUtil.isEmpty(excel.getQuestion()) || StrUtil.isEmpty(excel.getItem())) {
                iterator.remove();
                continue;
            }
            long questionKey = MurmurHash.hash64(excel.getQuestion());
            long itemKey = MurmurHash.hash64(excel.getItem());
            if (!questionMap.containsKey(questionKey) || !itemMap.containsKey(itemKey)) {
                iterator.remove();
                continue;
            }
            // 提前设置为字符串哈希后计算的值，防止后续再算一遍
            excel.setQuestion(String.valueOf(questionKey));
            excel.setItem(String.valueOf(itemKey));
        }
        Set<String> tags = templateExcels.stream().flatMap(e -> StrUtil.split(e.getTags(), StrUtil.COMMA).stream()).collect(Collectors.toSet());
        tagService.saveBatch(tags);
        final Map<String, Long> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getName, Tag::getId));
        List<TemplateContent> templateContents = templateExcels.stream().map(excel -> {
            Question question = questionMap.get(Long.valueOf(excel.getQuestion()));
            Item item = itemMap.get(Long.valueOf(excel.getItem()));
            return TemplateContent.builder()
                    .templateId(template.getId())
                    .questionId(question.getId())
                    .itemId(item.getId())
                    .objectIds(question.getObjectIds())
                    .matchTags(questionService.transformTagId(excel.getTags(), tagsMap))
                    .status(Boolean.TRUE)
                    .build();
        }).collect(Collectors.toList());
        templateContentService.saveList(templateContents);
    }

    private void produceByAlgorithm(Template template, String keyword) {
        List<String> keyWords = StrUtil.split(keyword, StrUtil.COMMA);
        final Map<Long, String> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        Set<String> questionIds = questionService.listAll().stream().map(q -> {
            String str = q.getTitle() + q.getDescribe() + questionService.transformTagName(q.getTagIds(), tagsMap);
            if (matchKeywordList(str, keyWords))
                return q.getId();
            return null;
        }).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
        Set<String> itemIds = itemService.listAll().stream().map(i -> {
            String str = i.getTitle() + i.getDescribe() + questionService.transformTagName(i.getTagIds(), tagsMap);
            if (matchKeywordList(str, keyWords))
                return i.getId();
            return null;
        }).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(questionIds) && CollUtil.isNotEmpty(itemIds)) {
            TemplateGenerateDTO dto = new TemplateGenerateDTO();
            dto.setName(template.getName());
            dto.setAlgorithm(TemplateAlgorithm.INTERSECTION.getCode());
            dto.setQuestionIds(new ArrayList<>(questionIds));
            dto.setItemIds(new ArrayList<>(itemIds));
            templateProduce(dto);
        }
        log.debug("未通过关键字匹配到问题或核查项列表");
    }

    public boolean matchKeywordList(String str, List<String> keywordList) {
        for (String keyword : keywordList) {
            if (str.contains(keyword)) {
                return true;
            }
        }
        return false;
    }


    private Template add(TemplateInsertReq req) {
        Template exist = templateMapper.selectByName(req.getName().trim());
        if (Objects.nonNull(exist))
            throw new ServiceException("模板已存在");
        Template template = Template.builder()
                .tag(req.getTag())
                .name(req.getName())
                .algorithm(req.getAlgorithm())
                .industryId(req.getIndustryId())
                .similarity(req.getSimilarity())
                .introduction(req.getIntroduction())
                .understand(Boolean.FALSE)
                .used(Boolean.TRUE)
                .version(req.getVersion())
                .createBy(SecurityUtils.getAccount())
                .createTime(new Date()).build();
        templateMapper.insert(template);
        return template;
    }

    @Override
    public void deleteTemplate(IdsReq req) {
        QueryWrapper<Template> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("understand", true);
        Template template = templateMapper.selectOne(queryWrapper);
        req.getIds().removeIf(id -> Objects.equals(template.getId().intValue(), id));
        QueryWrapper<TemplateContent> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.in("template_id", req.getIds());
        templateContentMapper.delete(deleteWrapper);
        templateMapper.deleteBatchIds(req.getIds());
    }

    @Override
    public void updateTemplate(TemplateInsertReq req, Long templateId) {
        Template template = templateMapper.selectById(templateId);
        if (Objects.isNull(template))
            throw new ServiceException("调研模板不存在");
        template.setName(req.getName());
        template.setIntroduction(req.getIntroduction());
        template.setTag(req.getTag());
        template.setUpdateBy(SecurityUtils.getAccount());
        template.setUpdateTime(new Date());
        templateMapper.updateById(template);
    }

    @Override
    public void setDefault(Long templateId) {
        int count = templateContentMapper.selectCountByTemplateId(templateId);
        if (count == 0)
            throw new ServiceException("当前模板不包含调研项，无法设置为默认模板");
        templateMapper.updateUnderstand();
        Template build = Template.builder().id(templateId).understand(Boolean.TRUE).used(Boolean.TRUE).build();
        templateMapper.updateById(build);
    }

    @Override
    public void stopTemplate(Long templateId) {
        Template template = templateMapper.selectById(templateId);
        if (Objects.nonNull(template) && template.getUnderstand()) {
            throw new ServiceException("默认模板无法停用");
        }
        Template build = Template.builder().id(templateId).used(!template.getUsed()).build();
        templateMapper.updateById(build);
    }

    @Override
    public void createBak(TemplateInsertReq req, Long bakId) {
        Template template = add(req);
        // 同步问题与核查项
        List<TemplateContent> contentList = templateContentMapper.selectContentByTemplateId(bakId);
        contentList.forEach(c -> c.setTemplateId(template.getId()));
        templateContentService.saveList(contentList);
    }

    @Override
    public List<TemplateQuestionVO> queryQuestion(Long id, String objectId) {
        final Map<Long, String> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        List<TemplateQuestionVO> contentList = templateMapper.queryQuestion(id, objectId);
        contentList.forEach(content -> {
            content.setObjectTags(StrUtil.split(content.getObjectTags(), StrUtil.COMMA).stream().map(o -> {
                long objId = Long.parseLong(o);
                return LabelEnum.getNameByCode(objId);
            }).collect(Collectors.joining(StrUtil.COMMA)));
            content.setQuestionTags(questionService.transformTagName(content.getQuestionTags(), tagsMap));
        });
        return contentList;
    }

    @Override
    public void deleteQuestion(TemplateQuestionReq req) {
        QueryWrapper<TemplateContent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", req.getTemplateId());
        queryWrapper.in("question_id", req.getQuestionIds());
        templateContentMapper.delete(queryWrapper);
    }

    @Override
    public List<Question> selectExcludeQuestion(Long templateId) {
        return templateContentMapper.selectExcludeQuestion(templateId);
    }

    @Override
    public void addQuestion(TemplateQuestionReq req) {
        QueryWrapper<Question> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", req.getQuestionIds());
        queryWrapper.eq("status", 0);
        Map<String, String> questionMap = questionMapper.selectList(queryWrapper)
                .stream().collect(Collectors.toMap(Question::getId, Question::getObjectIds));
        List<TemplateContent> content = req.getQuestionIds().stream().map(q ->
                TemplateContent.builder()
                        .templateId(req.getTemplateId())
                        .questionId(q)
                        .itemId(ITEM_ID)
                        .objectIds(questionMap.get(q))
                        .matchTags("0")
                        .status(Boolean.TRUE)
                        .build()).collect(Collectors.toList());
        templateContentService.saveList(content);
    }

    @Override
    public List<TemplateContentVO> queryContent(String questionId, Long templateId) {
        final Map<Long, String> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        List<TemplateContentVO> contentList = templateMapper.queryContent(questionId, templateId);
        transformList(tagsMap, contentList);
        return contentList;
    }

    @Override
    public void questionSorting(QuestionSortIngReq req) {
        QueryWrapper<QuestionSorting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", req.getTemplateId());
        Map<String, Integer> quesMap = questionSortingMapper.selectList(queryWrapper).stream().collect(
                Collectors.toMap(QuestionSorting::getQuestionId, QuestionSorting::getId));
        // 更新
        List<QuestionSorting> updateList = req.getQuestions().stream().filter(q ->
                quesMap.containsKey(q.getQuestionId())).map(que -> QuestionSorting.builder()
                .id(quesMap.get(que.getQuestionId()))
                .sort(que.getSort())
                .build()).collect(Collectors.toList());
        questionSortingService.updateBatchById(updateList);
        // 新增
        List<QuestionSorting> insertList = req.getQuestions().stream().filter(q ->
                !quesMap.containsKey(q.getQuestionId())).map(que -> QuestionSorting.builder()
                .templateId(req.getTemplateId())
                .questionId(que.getQuestionId())
                .sort(que.getSort())
                .build()).collect(Collectors.toList());
        questionSortingService.saveBatch(insertList);
    }

    @Override
    public void updateStatus(ContentStatusReq req) {
        UpdateWrapper<TemplateContent> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("template_id", req.getTemplateId());
        updateWrapper.eq("question_id", req.getQuestionId());
        templateContentMapper.update(TemplateContent.builder().status(req.getStatus()).build(), updateWrapper);
    }

    @Override
    public void insertItemInTemplate(ItemInsertReq req) {
        QueryWrapper<TemplateContent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", req.getTemplateId());
        queryWrapper.eq("question_id", req.getQuestionId());
        Set<String> existItemIds = templateContentMapper.selectList(queryWrapper)
                .stream().map(TemplateContent::getItemId).collect(Collectors.toSet());
        List<TemplateContent> contents = req.getItemIds().stream().filter(i -> !existItemIds.contains(i)).map(itemId ->
                TemplateContent.builder()
                        .templateId(req.getTemplateId())
                        .questionId(req.getQuestionId())
                        .itemId(itemId)
                        .objectIds(req.getObjectTags())
                        .matchTags("0")
                        .status(req.getStatus())
                        .build()
        ).collect(Collectors.toList());
        templateContentService.saveList(contents);
    }

    private void transformList(Map<Long, String> tagsMap, List<TemplateContentVO> contentList) {
        contentList.forEach(content -> {
            content.setMatchTags(questionService.transformTagName(content.getMatchTags(), tagsMap));
            content.setItemTags(questionService.transformTagName(content.getItemTags(), tagsMap));
        });
    }

    @Override
    public void deleteContentById(Long contentId) {
        templateContentMapper.deleteById(contentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void templateProduce(TemplateGenerateDTO dto) {
        QueryWrapper<Question> questionQuery = new QueryWrapper<>();
        questionQuery.eq("status", 0);
        if (!Objects.equals(dto.getQuestionIds().get(0).toUpperCase(), "ALL")) {
            questionQuery.in("id", dto.getQuestionIds());
        }
        List<Question> questions = questionMapper.selectList(questionQuery);
        if (CollUtil.isEmpty(questions))
            throw new ServiceException("调研问题列表为空");

        QueryWrapper<Item> itemQuery = new QueryWrapper<>();
        itemQuery.eq("status", 0);
        if (!Objects.equals(dto.getItemIds().get(0).toUpperCase(), "ALL")) {
            itemQuery.in("id", dto.getItemIds());
        }
        List<Item> items = itemMapper.selectList(itemQuery);
        if (CollUtil.isEmpty(items))
            throw new ServiceException("核查项列表为空");

        Template template;
        String templateName = dto.getName().trim();
        template = templateMapper.selectByName(templateName);
        if (Objects.isNull(template)) {
            template = Template.builder()
                    .name(templateName)
                    .algorithm(dto.getAlgorithm())
                    .similarity(dto.getSimilarity())
                    .industryId(1)
                    .createBy(SecurityUtils.getAccount())
                    .createTime(new Date())
                    .build();
            templateMapper.insert(template);
        }

        TemplateProduceContext context = new TemplateProduceContext(TemplateAlgorithm.of(dto.getAlgorithm()));
        List<TemplateContent> contentList = context.produce(template, questions, items);

        templateContentService.deleteByTemplateId(template.getId());
        templateContentService.saveList(contentList);
    }

    @Override
    public List<Template> searchTemplate(TemplateSearchReq req) {
        return templateMapper.searchTemplate(req);
    }

    @Override
    public List<TemplateQuestionVO> searchTemplateQuestion(TemplateQueSearchReq req) {
        return templateMapper.searchTemplateQuestions(req);
    }
}
