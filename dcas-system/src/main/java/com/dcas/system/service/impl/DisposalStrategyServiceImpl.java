package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.PageResult;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.html.EscapeUtil;
import com.dcas.common.utils.html.Word2Html;
import com.dcas.common.model.dto.DisposalSearchDTO;
import com.dcas.common.domain.entity.AdviseScheme;
import com.dcas.common.domain.entity.DisposalStrategy;
import com.dcas.common.model.param.AdviseSchemaSelectParam;
import com.dcas.common.model.req.DisposalStrategyReq;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.vo.DisposalStrategyVO;
import com.dcas.common.mapper.DisposalStrategyMapper;
import com.dcas.system.service.DisposalStrategyService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/4/18 16:19
 * @since 1.3.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DisposalStrategyServiceImpl extends ServiceImpl<DisposalStrategyMapper, DisposalStrategy> implements DisposalStrategyService {
    private static final String PICTURE = "[图片]";

    private final DisposalStrategyMapper disposalStrategyMapper;

    @Override
    public PageResult<DisposalStrategyVO> list(@RequestParam(value = "currentPage", defaultValue = "1") @ApiParam("当前页码") Integer currentPage,
                                         @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam("每页数量") Integer pageSize) {
        try (Page<Object> page = PageHelper.startPage(currentPage, pageSize)) {
            List<DisposalStrategyVO> disposalStrategies = disposalStrategyMapper.qryStrategies();
            List<DisposalStrategyVO> result = disposalStrategies.stream().peek(d -> {
                if (StrUtil.isNotBlank(d.getScheme())) {
                    String scheme = HtmlUtil.cleanHtmlTag(EscapeUtil.fixIncompleteTags(d.getScheme()));
                    d.setScheme(StrUtil.isEmpty(scheme) ? PICTURE : scheme);
                }
                }).collect(Collectors.toList());
            return PageResult.ofPage(page.getTotal(), result);
        }
    }

    @Override
    public DisposalStrategyVO detail(Integer id) {
        return disposalStrategyMapper.qryById(id);
    }

    @Override
    public void add(DisposalStrategyReq req) {
        DisposalStrategy bean = BeanUtil.copyProperties(req, DisposalStrategy.class);
        if (StrUtil.isEmpty(req.getScheme())) {
            // word上传转html格式
            if (StrUtil.isNotEmpty(req.getUrl())) {
                String html = Word2Html.word2Html(req.getUrl());
                bean.setScheme(html);
            } else {
                throw new ServiceException("处置方案不能为空");
            }
        }
        Date date = new Date();
        String username = SecurityUtils.getAccount();
        bean.setCreateBy(username);
        bean.setUpdateBy(username);
        bean.setCreateTime(date);
        bean.setUpdateTime(date);
        disposalStrategyMapper.insert(bean);
    }

    @Override
    public void batchDelete(IdsReq req) {
        disposalStrategyMapper.deleteBatchIds(req.getIds());
    }

    @Override
    public void update(Integer id, DisposalStrategyReq req) {
        DisposalStrategy bean = BeanUtil.copyProperties(req, DisposalStrategy.class);
        bean.setId(id);
        disposalStrategyMapper.updateById(bean);
    }

    @Override
    public List<DisposalStrategyVO> search(DisposalSearchDTO dto) {
        List<DisposalStrategyVO> disposalStrategyVOS = disposalStrategyMapper.search(dto);
        return disposalStrategyVOS.stream().peek(d -> {
                    if (StrUtil.isNotBlank(d.getScheme())){
                        String scheme = HtmlUtil.cleanHtmlTag(EscapeUtil.fixIncompleteTags(d.getScheme()));
                        d.setScheme(StrUtil.isEmpty(scheme) ? PICTURE : scheme);
                    }
                    }).collect(Collectors.toList());
    }

    @Override
    @SchemaSwitch(AdviseSchemaSelectParam.class)
    public List<AdviseScheme> selectSchemeByItemIdsForLegal(AdviseSchemaSelectParam param) {
        return baseMapper.selectSchemeByItemIdsForLegal(param.getItemIds(), param.getLawIds());
    }

    @Override
    @SchemaSwitch(AdviseSchemaSelectParam.class)
    public List<AdviseScheme> selectSchemeByItemIdsForCapacity(AdviseSchemaSelectParam param) {
        return baseMapper.selectSchemeByItemIdsForCapacity(param.getItemIds(), param.getLawIds());
    }
}
