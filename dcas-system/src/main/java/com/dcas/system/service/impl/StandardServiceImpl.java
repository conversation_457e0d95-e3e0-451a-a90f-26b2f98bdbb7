package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.hash.MurmurHash;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.Func;
import com.dcas.common.utils.PageResult;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.file.FileUtils;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.excel.StandardItemExcel;
import com.dcas.common.model.excel.StandardRelevanceExcel;
import com.dcas.common.model.req.*;
import com.dcas.common.model.vo.ArticleItemVO;
import com.dcas.common.mapper.ClauseItemRelevanceMapper;
import com.dcas.common.mapper.StandardItemMapper;
import com.dcas.common.mapper.StandardMapper;
import com.dcas.common.mapper.StandardTemplateRelevanceMapper;
import com.dcas.system.service.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/6 18:21
 * @since 1.2.0
 */
@Service
@RequiredArgsConstructor
public class StandardServiceImpl implements StandardService {

    private final StandardMapper standardMapper;
    private final StandardItemMapper standardItemMapper;
    private final ClauseItemRelevanceMapper clauseItemRelevanceMapper;
    private final StandardTemplateRelevanceMapper standardTemplateRelevanceMapper;

    private final TagService tagService;
    private final ItemService itemService;
    private final QuestionService questionService;
    private final ClauseItemRelevanceService clauseItemRelevanceService;

    @Override
    public List<Standard> listAll() {
        return standardMapper.selectList(new QueryWrapper<>());
    }

    @Override
    public PageResult<Standard> pageQuery(Integer currentPage, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(currentPage, pageSize);
        List<Standard> standards = standardMapper.selectList(new QueryWrapper<>());
        return PageResult.ofPage(page.getTotal(), standards);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void add(StandardFileReq req) {
        String name = req.getName().trim();
        QueryWrapper<Standard> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        int count = standardMapper.selectCount(queryWrapper);
        if (count != 0)
            throw new ServiceException("标准文件已存在，请勿重复添加");
        Standard standard = BeanUtil.copyProperties(req, Standard.class);
        standard.setEnable(Boolean.TRUE);
        standard.setCreateTime(new Date());
        standard.setCreateBy(SecurityUtils.getAccount());
        standardMapper.insert(standard);
        if (Objects.nonNull(req.getContentType()) && req.getContentType() == 2) {
            if (StrUtil.isEmpty(req.getUrl()))
                throw new ServiceException("文件上传路径不能为空");
            MultipartFile file = FileUtils.createMultipartFile(req.getUrl());
            produceItemsByFile(standard.getId(), file);
        }
    }

    private void produceItemsByFile(Integer id, MultipartFile file) throws IOException {
        List<ClauseItemRelevance> result = Lists.newArrayList();
        List<StandardRelevanceExcel> standardRelevanceExcels = Func.fileAttestation(file, StandardRelevanceExcel.class, null, true);
        Map<Long, Item> itemMap = itemService.listAll().stream().collect(
                Collectors.toMap(i -> MurmurHash.hash64(i.getTitle()), i -> i));
        Iterator<StandardRelevanceExcel> iterator = standardRelevanceExcels.iterator();
        final BeanCopier copier = BeanCopier.create(StandardItemExcel.class, StandardItem.class, false);
        while (iterator.hasNext()) {
            StandardRelevanceExcel excel = iterator.next();
            if (StrUtil.isEmpty(excel.getItem())) {
                continue;
            }
            long itemKey = MurmurHash.hash64(excel.getItem());
            if (StrUtil.isEmpty(excel.getContent()) || !itemMap.containsKey(itemKey)) {
                iterator.remove();
                continue;
            }
            // 提前设置计算好的哈希值
            excel.setItem(String.valueOf(itemKey));
        }
        Set<String> tags = standardRelevanceExcels.stream().flatMap(e -> {
            List<String> split = StrUtil.split(e.getTags(), StrUtil.COMMA);
            split.addAll(StrUtil.split(e.getMatchTags(), StrUtil.COMMA));
            return split.stream();
        }).collect(Collectors.toSet());
        tagService.saveBatch(tags);
        final Map<String, Long> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getName, Tag::getId));
        Map<StandardItemExcel, List<StandardRelevanceExcel>> itemRelevance = standardRelevanceExcels.stream().collect(
                Collectors.groupingBy(item -> BeanUtil.copyProperties(item, StandardItemExcel.class)));
        itemRelevance.forEach((item, relevance) -> {
            StandardItem standardItem = new StandardItem();
            copier.copy(item, standardItem, null);
            standardItem.setStandardId(id);
            standardItem.setTags(questionService.transformTagId(standardItem.getTags(), tagsMap));
            standardItemMapper.insert(standardItem);
            List<ClauseItemRelevance> clauseItemRelevance= relevance.stream().filter(e -> StrUtil.isNotEmpty(e.getItem())).map(r -> {
                return ClauseItemRelevance.builder()
                        .clauseId(standardItem.getId())
                        .itemId(itemMap.get(Long.valueOf(r.getItem())).getId())
                        .matchTags(questionService.transformTagId(r.getMatchTags(), tagsMap))
                        .build();
            }).collect(Collectors.toList());
            result.addAll(clauseItemRelevance);
        });
        clauseItemRelevanceService.saveList(result);
    }

    @Override
    public void update(StandardFileReq req) {
        if (Objects.isNull(req.getId()))
            throw new ServiceException("更新主键不能为空");
        Standard standard = standardMapper.selectById(req.getId());
        if (Objects.isNull(standard))
            throw new ServiceException("更新对象不存在，id:{}", req.getId());
        Standard copy = BeanUtil.copyProperties(req, Standard.class);
        copy.setId(standard.getId());
        copy.setUpdateBy(SecurityUtils.getAccount());
        copy.setUpdateTime(new Date());
        standardMapper.updateById(copy);
    }

    @Override
    public void delete(IdsReq req) {
        List<StandardTemplateRelevance> relevance = standardTemplateRelevanceMapper.selectList(new QueryWrapper<>());
        Set<Integer> standardIds = relevance.stream().map(StandardTemplateRelevance::getStandardId).collect(Collectors.toSet());
        if (!CollUtil.intersectionDistinct(req.getIds(), standardIds).isEmpty())
            throw new ServiceException("待删除列表中存在与模板关联的标准文件，请先取消关联");
        standardMapper.deleteBatchIds(req.getIds());
        QueryWrapper<StandardItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("standard_id", req.getIds());
        List<StandardItem> standardItems = standardItemMapper.selectList(queryWrapper);
        Set<String> itemIds = standardItems.stream().map(StandardItem::getId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(itemIds)) {
            standardItemMapper.deleteBatchIds(itemIds);
            QueryWrapper<ClauseItemRelevance> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.in("clause_id", itemIds);
            clauseItemRelevanceMapper.delete(deleteWrapper);
        }
    }

    @Override
    public void enable(Integer id) {
        Standard standard = standardMapper.selectById(id);
        if (Objects.isNull(standard))
            throw new ServiceException("标准文件不存在");
        Standard build = Standard.builder().id(id).enable(!standard.getEnable()).build();
        standardMapper.updateById(build);
    }

    @Override
    public PageResult<StandardItem> pageQueryItems(Integer id, Integer currentPage, Integer pageSize) {
        final Map<Long, String> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        Page<Object> page = PageHelper.startPage(currentPage, pageSize);
        QueryWrapper<StandardItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("standard_id", id);
        List<StandardItem> standardItems = standardItemMapper.selectList(queryWrapper);
        standardItems.forEach(item -> {
            item.setTags(questionService.transformTagName(item.getTags(), tagsMap));
        });
        return PageResult.ofPage(page.getTotal(), standardItems);
    }

    @Override
    public void addItem(StandardItemReq req) {
        StandardItem standardItem = BeanUtil.copyProperties(req, StandardItem.class);
        standardItem.setTags(questionService.transformTagId(req.getTags()));
        standardItemMapper.insert(standardItem);
    }

    @Override
    public void updateItem(StandardItemUpdateReq req) {
        StandardItem standardItem = BeanUtil.copyProperties(req, StandardItem.class);
        standardItem.setTags(questionService.transformTagId(req.getTags()));
        standardItemMapper.updateById(standardItem);
    }

    @Override
    public void deleteItem(ExcludeItemReq req) {
        standardItemMapper.deleteBatchIds(req.getItemIds());
        QueryWrapper<ClauseItemRelevance> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.in("clause_id", req.getItemIds());
        clauseItemRelevanceMapper.delete(deleteWrapper);
    }

    @Override
    public List<ArticleItemVO> qryClauseItems(String clauseId) {
        final Map<Long, String> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        List<ArticleItemVO> articleItems = clauseItemRelevanceMapper.qryStandardItems(clauseId);
        articleItems.forEach(item -> {
            item.setMatchTags(questionService.transformTagName(item.getMatchTags(), tagsMap));
        });
        return articleItems;
    }

    @Override
    public void addClauseItem(RelevanceInsertReq req) {
        QueryWrapper<ClauseItemRelevance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("clause_id", req.getId());
        Set<String> existItemIds = clauseItemRelevanceMapper.selectList(queryWrapper)
                .stream().map(ClauseItemRelevance::getItemId).collect(Collectors.toSet());
        List<ClauseItemRelevance> clauseItemRelevance = req.getItemIds().stream().filter(i -> !existItemIds.contains(i)).map(item ->
                ClauseItemRelevance.builder()
                        .clauseId(req.getId())
                        .itemId(item)
                        .matchTags("0")
                        .build()).collect(Collectors.toList());
        clauseItemRelevanceService.saveList(clauseItemRelevance);
    }

    @Override
    public void deleteArticleItem(Integer id) {
        clauseItemRelevanceMapper.deleteById(id);
    }

    @Override
    public List<Standard> search(StandardFileReq req) {
        return standardMapper.search(req);
    }

    @Override
    public List<StandardItem> searchItem(StandardItemReq req) {
        List<StandardItem> standardItems = new ArrayList<>();
        final Map<Long, String> tagsMap = tagService.listAll().stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        if (StrUtil.isNotEmpty(req.getTags())) {
            final Map<String, Long> map = tagsMap.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
            if (Objects.isNull(map.get(req.getTags())))
                return standardItems;
            req.setTags(map.get(req.getTags()).toString());
        }
        standardItems = standardItemMapper.search(req);
        standardItems.forEach(item -> {
            item.setTags(questionService.transformTagName(item.getTags(), tagsMap));
        });
        return standardItems;
    }
}
