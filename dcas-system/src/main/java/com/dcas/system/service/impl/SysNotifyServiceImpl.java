package com.dcas.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.enums.NotifyFormEnum;
import com.dcas.common.enums.NotifySearchTypeEnum;
import com.dcas.common.enums.NotifyTypeEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.PageResult;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.model.dto.NotifyDTO;
import com.dcas.common.domain.entity.SysNotify;
import com.dcas.common.model.req.NotifySearchReq;
import com.dcas.common.mapper.SysNotifyMapper;
import com.dcas.system.service.ISysNotifyService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/1/29 16:31
 * @since 1.0.1
 */
@Service
@RequiredArgsConstructor
public class SysNotifyServiceImpl extends ServiceImpl<SysNotifyMapper, SysNotify> implements ISysNotifyService {

    private final SysNotifyMapper sysNotifyMapper;

    @Override
    public void insertList(List<SysNotify> sysNotify) {
        this.saveBatch(sysNotify);
    }

    @Override
    public PageResult<NotifyDTO> listUserMessages(NotifySearchReq req) {
        Page<Object> page = PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        Integer type = null;
        String form = null;
        Date startDate = null;
        Date endDate = null;
        if (req.getType() == NotifySearchTypeEnum.TYPE){
            type = Integer.parseInt(req.getValue());
        }
        if (req.getType() == NotifySearchTypeEnum.FORM){
            form = req.getValue();
        }
        if (req.getType() == NotifySearchTypeEnum.TIME){
            startDate = req.getStartDate();
            endDate = req.getEndDate();
        }
        List<NotifyDTO> notify = sysNotifyMapper.listUserMessages(SecurityUtils.getAccount(), type, form, startDate, endDate);
        notify.forEach(this::translate);
        return PageResult.ofPage(page.getTotal(), notify);
    }

    @Override
    public void deleteById(Long id) {
        sysNotifyMapper.deleteById(id);
    }

    @Override
    public void updateStatus(Long id) {
        SysNotify sysNotify = sysNotifyMapper.selectById(id);
        if (Objects.isNull(sysNotify))
            throw new ServiceException("消息提醒不存在");
        sysNotify.setRead(1);
        sysNotifyMapper.updateById(sysNotify);
    }

    @Override
    public Boolean isUnread() {
        return sysNotifyMapper.unreadMsg(SecurityUtils.getAccount()) > 0;
    }

    private void translate(NotifyDTO dto) {
        dto.setForm(StrUtil.split(dto.getForm(), ";").stream()
                .map(f -> NotifyFormEnum.of(Integer.parseInt(f)).getName()).collect(Collectors.joining(";")));
        dto.setType(NotifyTypeEnum.of(Integer.parseInt(dto.getType())).getName());
    }
}
