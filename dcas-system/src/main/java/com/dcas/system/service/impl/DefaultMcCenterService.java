package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.domain.entity.McAppConfig;
import com.dcas.common.enums.AuthType;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.McAppConfigMapper;
import com.dcas.common.model.dto.SSOAccountDTO;
import com.dcas.common.model.dto.SSOAccountListDTO;
import com.dcas.common.model.vo.AuthNodeVO;
import com.dcas.discovery.domain.enums.Status;
import com.dcas.system.service.IMcCenterService;
import com.google.common.collect.Lists;
import com.mchz.starter.sso.model.SecureUser;
import com.mchz.starter.sso.util.SsoUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/11/7 14:28
 * @since 1.7.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultMcCenterService implements IMcCenterService {

    @Value("${mc.sso.user-center.host:#{null}}")
    private String centerHost;
    @Value("${mc.sso.enabled:true}")
    private boolean ssoEnabled;
    private final McAppConfigMapper mcAppConfigMapper;

    @Override
    public String queryLoginIndex() {
        return centerHost + "/#/not-login/login";
    }

    @Override
    public List<SSOAccountDTO> getSsoUsers() {
        log.debug("[McCenter] host:{}", centerHost);
        HttpRequest request = HttpRequest.of(centerHost + "/account?page=1&size=65535").method(Method.GET);
        String response = this.doRequest(request, null);
        return JSONUtil.parseObj(response).toBean(SSOAccountListDTO.class).getList();
    }

    @Override
    public List<AuthNodeVO> queryAuth() {
        List<AuthNodeVO> list = Lists.newArrayList();
        //填充目录
        List<Auth> auths = fillAuths(getAuths());
        // 获取第一级菜单
        List<Auth> parents = auths.stream().filter(o -> !o.getCode().contains(StrUtil.SLASH)
                && !o.getCode().contains(StrUtil.UNDERLINE)).collect(Collectors.toList());
        buildTree(auths, parents, list, StrUtil.EMPTY);
        return list;
    }

    /**
     * 处理目录
     *
     * @param auths 权限列表
     * @param parents 父级目录
     * @param list {@link AuthNodeVO}
     * @param str 用于拼接id
     */
    private void buildTree(List<Auth> auths, List<Auth> parents, List<AuthNodeVO> list, String str) {
        for (Auth node : parents) {
            String code = node.getCode();
            String name = node.getName();
            AuthNodeVO pNode = new AuthNodeVO();
            pNode.setId(str + AuthType.ofRouteId(code));
            pNode.setName(name);
            //处理当前目录下的操作权限
            buildAuthList(auths, code, pNode);
            //处理当前目录的子目录
            List<Auth> subLists = auths.stream().filter(o -> code.equals(StrUtil.subBefore(o.getCode(), StrUtil.SLASH, true))
                    && !o.getCode().contains(StrUtil.UNDERLINE) && !code.equals(o.getCode())).collect(Collectors.toList());
            buildTree(auths, subLists, pNode.getChild(), pNode.getId());
            list.add(pNode);
        }
    }

    /**
     * 处理权限
     *
     * @param auths 所有权限列表
     * @param code code
     * @param node 构造节点
     */
    private void buildAuthList(List<Auth> auths, String code, AuthNodeVO node) {
        List<Auth> children = auths.stream().filter(o -> o.getCode()
                .contains(code + StrUtil.UNDERLINE)).collect(Collectors.toList());
        if (CollUtil.isEmpty(children)) {
            return;
        }
        List<String> authList = node.getAuthList();
        for (Auth child : children) {
            authList.add(StrUtil.subAfter(child.getCode(), StrUtil.UNDERLINE, true));
        }
    }

    private boolean isStartWith(String str, List<String> list) {
        for (String s : list) {
            if (s.equals(str)) {
                continue;
            }
            if (s.startsWith(str)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<Auth> getAuths() {
        // 未启用统一身份 || 嵌入式（不需要激活）
        if (!ssoEnabled) {
            List<Auth> list = Lists.newArrayList();
            for (AuthType type : AuthType.values()) {
                // 不返回页面，后续根据功能自动填充
                if(!type.getCode().contains(StrUtil.UNDERLINE)) {
                    continue;
                }
                Auth auth = new Auth();
                auth.setCode(type.getCode());
                auth.setName(type.getName());
                list.add(auth);
            }
            return list;
        }

        SecureUser user = SsoUtil.getCurrentUser();
        if (Objects.isNull(user)) {
            throw new ServiceException("用户信息查询失败！");
        }
        String account = user.getAccount();
        // 统一身份新版账号支持中文字符，配合调整
        account = URLEncodeUtil.encode(account);
        Map<String, List<String>> params = getParams();
        HttpRequest request = HttpRequest.get(centerHost + "/account/" + account + "/permission?page=1&size=10000")
                .header(params);
        log.debug("统一身份权限接口调用请求：{}", request.toString());
        String body = request.execute().body();
        log.debug("统一身份权限接口响应内容：{}", body);
        checkResponse(body);
        Response response = JSONUtil.toBean(body, Response.class);
        List<Auth> auths = response.getList();
        Set<String> menuCode = Arrays.stream(AuthType.values()).map(AuthType::getCode).collect(Collectors.toSet());
        return auths.stream().filter(a -> menuCode.contains(a.getCode())).collect(Collectors.toList());
    }

    private static void checkResponse(String response) {
        if (StrUtil.isEmpty(response)) {
            return;
        }
        Map map = JSONUtil.toBean(response, Map.class);
        if (StrUtil.isNotEmpty(MapUtil.getStr(map, "reason"))
                || StrUtil.isNotEmpty(MapUtil.getStr(map, "message"))) {
            throw new ServiceException(MapUtil.getStr(map, "message"));
        }
    }

    private List<Auth> fillAuths(List<Auth> auths) {
        List<Auth> list = Lists.newArrayList();
        List<String> codes = auths.stream().map(Auth::getCode).collect(Collectors.toList());
        for (AuthType type : AuthType.values()) {
            // 根据选择的按钮添加关联菜单
            if (!type.getCode().contains(StrUtil.UNDERLINE)) {
                if (isStartWith(type.getCode(), codes)){
                    Auth a = new Auth();
                    a.setCode(type.getCode());
                    a.setName(type.getName());
                    list.add(a);
                }
            }
        }
        list.addAll(auths);
        return list;
    }

    private String doRequest(HttpRequest httpRequest, Object body) {
        Map<String, String> map = mcAppConfigMapper.selectList(new QueryWrapper<>()).stream().collect(Collectors.toMap(McAppConfig::getName, McAppConfig::getValue));
        String appId = MapUtil.getStr(map, "secret_id");
        String AppSecret = MapUtil.getStr(map, "secret_key");
        httpRequest.header("AppId", appId).header("AppSecret", DigestUtil.sha256Hex(appId + AppSecret)).timeout(30_000);
        String token = getToken();
        if (StrUtil.isNotBlank(token)) {
            httpRequest.header("Authorization", token);
        }

        String payload = "";
        if (body != null) {
            payload = JSONUtil.toJsonStr(body);
            httpRequest.body(payload);
        }

        log.debug("[统一登录平台] >> url:{}, body:{}", httpRequest.getUrl(), payload);
        HttpResponse response = httpRequest.execute();
        log.debug("[统一登录平台] << {}", response != null ? response.body() : "");
        if (response != null) {
            if (response.getStatus() == 404) {
                throw new ServiceException(Status.HTTP_NOT_FOUND, response.body());
            }

            if (!response.isOk()) {
                throw new ServiceException(response.body());
            }
        }

        return response != null ? response.body() : "";
    }

    private Map<String, List<String>> getParams() {
        Map<String, List<String>> map = new HashMap<>(16);
        String token = SsoUtil.getToken();
        Map<String, String> mcAppConfigs = getMcAppConfigs();
        String secretId = getAppId(mcAppConfigs);
        String secretKey = getAppSecret(mcAppConfigs);
        map.put(HEADER_APP_ID, Lists.newArrayList(secretId));
        map.put(HEADER_APP_SECRET, Lists.newArrayList(DigestUtil.sha256Hex(secretId + secretKey)));
        map.put(HEADER_AUTHORIZATION, Lists.newArrayList(token));
        return map;
    }

    public String getAppId(Map<String, String> mcAppConfigs) {
        return mcAppConfigs.get("secret_id");
    }

    public String getAppSecret(Map<String, String> mcAppConfigs) {
        return mcAppConfigs.get("secret_key");
    }

    private Map<String, String> getMcAppConfigs() {
        List<McAppConfig> mcAppConfigs = mcAppConfigMapper.selectList(new QueryWrapper<>());
        return mcAppConfigs.stream().collect(Collectors.toMap(McAppConfig::getName, McAppConfig::getValue));
    }

    private String getToken() {
        return SsoUtil.getToken();
    }

    @Data
    public static class Response {
        private Integer currentPage;
        private Integer pageSize;
        private Integer totalPage;
        private Integer total;
        private List<Auth> list;
    }

    @Data
    public static class Auth {
        private String code;
        private String name;
    }
}
