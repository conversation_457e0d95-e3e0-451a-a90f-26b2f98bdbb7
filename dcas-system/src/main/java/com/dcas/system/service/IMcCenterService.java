package com.dcas.system.service;

import com.dcas.common.model.dto.SSOAccountDTO;
import com.dcas.common.model.vo.AuthNodeVO;
import com.dcas.system.service.impl.DefaultMcCenterService;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/11/7 14:27
 */
public interface IMcCenterService {
    String HEADER_APP_ID = "AppId";
    String HEADER_APP_SECRET = "AppSecret";
    String HEADER_AUTHORIZATION = "Authorization";

    String queryLoginIndex();

    List<SSOAccountDTO> getSsoUsers();

    List<AuthNodeVO> queryAuth();

    List<DefaultMcCenterService.Auth> getAuths();
}
