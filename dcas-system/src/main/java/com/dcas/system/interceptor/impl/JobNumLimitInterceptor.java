package com.dcas.system.interceptor.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.domain.entity.CoLicense;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.common.enums.AttributeEnum;
import com.dcas.common.enums.CommonResultCode;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.*;
import com.dcas.system.service.ISysConfigService;
import com.dcas.system.service.SecurityOperationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @className JobNumLimitInterceptor
 * @description 作业数量限制拦截器
 * @date 2025/06/18 11:34
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JobNumLimitInterceptor implements HandlerInterceptor {

    private final SysConfigMapper sysConfigMapper;
    private final SecurityOperationMapper securityOperationMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoProjectMapper coProjectMapper;
    private final CoLicenseMapper coLicenseMapper;

    private final static String SECURITY_OPERATION_ADD = "/api/security";
    private final static String ASSESSMENT_OPERATION_ADD = "/api/operation/add";
    private final static String PROJECT_ADD = "/api/project/add";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        checkJobNum(request.getMethod(), request.getRequestURI());
        return true;
    }

    private void checkJobNum(String method, String uri) {
        SysConfig sysConfig = sysConfigMapper.selectConfigByKey(SysConfigEnum.JOB_NUM.getKey());
        Integer jobNum = 5;
        if (sysConfig != null && CharSequenceUtil.isNotEmpty(sysConfig.getConfigValue())){
            jobNum = Integer.parseInt(sysConfig.getConfigValue());
        }

        CoLicense coLicense = coLicenseMapper.selectOne(
            new QueryWrapper<CoLicense>().eq("attribute", AttributeEnum.CLIENT_GRANT.getAttribute()));
        // 测试版本
        if (coLicense != null && "ONTRAIL".equals(coLicense.getLncType())) {
            if (SECURITY_OPERATION_ADD.equals(uri) && RequestMethod.POST.name().equals(method)) {
                if (securityOperationMapper.selectCount(null) >= jobNum) {
                    throw new ServiceException(CommonResultCode.MC_BASE_EDITION_WARN);
                }
            } else if (ASSESSMENT_OPERATION_ADD.equals(uri) && (coOperationMapper.selectCount(null) >= jobNum)) {
                throw new ServiceException(CommonResultCode.MC_BASE_EDITION_WARN);
            } else if (PROJECT_ADD.equals(uri) && coProjectMapper.selectCount(null) >= jobNum) {
                throw new ServiceException(CommonResultCode.MC_BASE_EDITION_WARN);
            }
        } else {
            if (SECURITY_OPERATION_ADD.equals(uri) && RequestMethod.POST.name().equals(method)) {
                if (securityOperationMapper.selectCount(null) >= jobNum) {
                    throw new ServiceException(CommonResultCode.JOB_NUM_OVERLIMIT_WARN);
                }
            } else if (ASSESSMENT_OPERATION_ADD.equals(uri) && (coOperationMapper.selectCount(null) >= jobNum)) {
                throw new ServiceException(CommonResultCode.JOB_NUM_OVERLIMIT_WARN);
            }
        }
    }

}
