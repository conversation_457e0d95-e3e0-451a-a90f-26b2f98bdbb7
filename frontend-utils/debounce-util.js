/**
 * 防抖工具类 - 防止用户快速点击导致重复提交
 * 
 * 使用方法：
 * 1. 按钮防抖：debounceClick(buttonElement, handler, delay)
 * 2. 表单提交防抖：debounceSubmit(formElement, handler, delay)
 * 3. 通用防抖：debounce(func, delay)
 */

class DebounceUtil {
    constructor() {
        this.timers = new Map();
        this.submittingForms = new Set();
    }

    /**
     * 通用防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} delay 延迟时间（毫秒）
     * @param {string} key 唯一标识符，用于区分不同的防抖实例
     * @returns {Function} 防抖后的函数
     */
    debounce(func, delay = 300, key = 'default') {
        return (...args) => {
            // 清除之前的定时器
            if (this.timers.has(key)) {
                clearTimeout(this.timers.get(key));
            }

            // 设置新的定时器
            const timer = setTimeout(() => {
                func.apply(this, args);
                this.timers.delete(key);
            }, delay);

            this.timers.set(key, timer);
        };
    }

    /**
     * 按钮点击防抖
     * @param {HTMLElement} button 按钮元素
     * @param {Function} handler 点击处理函数
     * @param {number} delay 防抖延迟时间
     */
    debounceClick(button, handler, delay = 300) {
        if (!button || typeof handler !== 'function') {
            console.error('DebounceUtil: Invalid button or handler');
            return;
        }

        const key = `click_${button.id || Math.random()}`;
        let isProcessing = false;

        button.addEventListener('click', (event) => {
            // 防止重复点击
            if (isProcessing) {
                event.preventDefault();
                return;
            }

            isProcessing = true;
            button.disabled = true;

            // 添加加载状态
            const originalText = button.textContent;
            button.textContent = '处理中...';
            button.classList.add('loading');

            try {
                const result = handler(event);
                
                // 如果返回Promise，等待完成
                if (result && typeof result.then === 'function') {
                    result
                        .then(() => {
                            this.resetButton(button, originalText);
                        })
                        .catch((error) => {
                            console.error('Button handler error:', error);
                            this.resetButton(button, originalText);
                        })
                        .finally(() => {
                            isProcessing = false;
                        });
                } else {
                    // 同步函数，延迟重置
                    setTimeout(() => {
                        this.resetButton(button, originalText);
                        isProcessing = false;
                    }, delay);
                }
            } catch (error) {
                console.error('Button handler error:', error);
                this.resetButton(button, originalText);
                isProcessing = false;
            }
        });
    }

    /**
     * 表单提交防抖
     * @param {HTMLFormElement} form 表单元素
     * @param {Function} handler 提交处理函数
     * @param {number} delay 防抖延迟时间
     */
    debounceSubmit(form, handler, delay = 500) {
        if (!form || typeof handler !== 'function') {
            console.error('DebounceUtil: Invalid form or handler');
            return;
        }

        const formId = form.id || `form_${Math.random()}`;

        form.addEventListener('submit', (event) => {
            event.preventDefault();

            // 检查是否正在提交
            if (this.submittingForms.has(formId)) {
                console.warn('Form is already being submitted');
                return;
            }

            this.submittingForms.add(formId);
            
            // 禁用表单中的所有提交按钮
            const submitButtons = form.querySelectorAll('button[type="submit"], input[type="submit"]');
            submitButtons.forEach(btn => {
                btn.disabled = true;
                if (btn.tagName === 'BUTTON') {
                    btn.dataset.originalText = btn.textContent;
                    btn.textContent = '提交中...';
                }
            });

            try {
                const result = handler(event);
                
                if (result && typeof result.then === 'function') {
                    result
                        .then(() => {
                            this.resetForm(form, submitButtons);
                        })
                        .catch((error) => {
                            console.error('Form handler error:', error);
                            this.resetForm(form, submitButtons);
                        })
                        .finally(() => {
                            this.submittingForms.delete(formId);
                        });
                } else {
                    setTimeout(() => {
                        this.resetForm(form, submitButtons);
                        this.submittingForms.delete(formId);
                    }, delay);
                }
            } catch (error) {
                console.error('Form handler error:', error);
                this.resetForm(form, submitButtons);
                this.submittingForms.delete(formId);
            }
        });
    }

    /**
     * 重置按钮状态
     */
    resetButton(button, originalText) {
        button.disabled = false;
        button.textContent = originalText;
        button.classList.remove('loading');
    }

    /**
     * 重置表单状态
     */
    resetForm(form, submitButtons) {
        submitButtons.forEach(btn => {
            btn.disabled = false;
            if (btn.dataset.originalText) {
                btn.textContent = btn.dataset.originalText;
                delete btn.dataset.originalText;
            }
        });
    }

    /**
     * 清除所有定时器
     */
    clearAll() {
        this.timers.forEach(timer => clearTimeout(timer));
        this.timers.clear();
        this.submittingForms.clear();
    }
}

// 创建全局实例
const debounceUtil = new DebounceUtil();

// 使用示例：
/*
// 1. 按钮防抖
debounceUtil.debounceClick(
    document.getElementById('saveButton'),
    async (event) => {
        // 调用保存API
        await saveQuestionnaire(data);
    },
    500
);

// 2. 表单防抖
debounceUtil.debounceSubmit(
    document.getElementById('questionnaireForm'),
    async (event) => {
        const formData = new FormData(event.target);
        await submitQuestionnaire(formData);
    },
    500
);

// 3. 通用防抖
const debouncedSave = debounceUtil.debounce(saveFunction, 300, 'questionnaire-save');
*/

export default debounceUtil;
