// Alternative implementation using method-level synchronization
// This approach is simpler but only works for single-instance deployments

/**
 * Alternative saveAndUpdate method using synchronized approach
 * Note: This only works for single-instance deployments
 */
@Override
@Transactional(rollbackFor = Exception.class)
public synchronized void saveAndUpdate(QuestionnaireContentReq req) {
    // The synchronized keyword ensures only one thread can execute this method at a time
    // for the same instance of QuestionnaireServiceImpl
    
    CoOperation coOperation = coOperationMapper.selectById(req.getOperationId());
    List<ContentVO> questions = req.getDetails();
    BeanCopier questionnaireCopier = BeanCopier.create(ContentVO.class, Questionnaire.class, false);
    BeanCopier contentCopier = BeanCopier.create(ContentVO.ItemVO.class, QuestionnaireContent.class, false);
    List<Questionnaire> questionnaires = questions.stream().map(q -> {
        Questionnaire o1 = new Questionnaire();
        q.setMatchTags(q.getMatchTags());
        questionnaireCopier.copy(q, o1, null);
        o1.setOperationId(req.getOperationId());
        o1.setObjectId(req.getLabelId());
        return o1;
    }).collect(Collectors.toList());

    List<QuestionnaireItemDegree> degreeList = new ArrayList<>();
    List<QuestionnaireContent> questionnaireContents = new ArrayList<>();
    for (ContentVO vo : questions) {
        List<ContentVO.ItemVO> items = vo.getItems();
        for (ContentVO.ItemVO item : items) {
            QuestionnaireContent questionnaireContent = copyQuestionnaireContent(req, vo, item, contentCopier, coOperation, degreeList);
            questionnaireContents.add(questionnaireContent);
            if (CollUtil.isNotEmpty(item.getChildren())) {
                for (ContentVO.ItemVO child : item.getChildren()) {
                    QuestionnaireContent childQuestionnaireContent = copyQuestionnaireContent(req, vo, child, contentCopier, coOperation, degreeList);
                    questionnaireContents.add(childQuestionnaireContent);
                }
            }
        }
    }
    
    // This check is now thread-safe due to the synchronized method
    List<Questionnaire> questionnaire = getQuestionnaire(req.getLabelId(), req.getOperationId());
    if (CollUtil.isEmpty(questionnaire)) {
        this.saveBatch(questionnaires);
        questionnaireContentService.saveList(questionnaireContents);
        log.info("Successfully saved new questionnaire for operation: {}, object: {}", req.getOperationId(), req.getLabelId());
    } else {
        this.updateBatchById(questionnaires);
        questionnaireContentService.updateList(questionnaireContents);
        log.info("Successfully updated existing questionnaire for operation: {}, object: {}", req.getOperationId(), req.getLabelId());
    }
    
    if (CollUtil.isNotEmpty(degreeList)) {
        Collection<QuestionnaireItemDegree> values = degreeList.stream().collect(Collectors.toMap(
                QuestionnaireItemDegree::getItemId, Function.identity(), (v1, v2) -> v1.getDegree() >= v2.getDegree() ? v1 : v2)).values();
        questionnaireItemDegreeService.remove(new QueryWrapper<QuestionnaireItemDegree>()
                .eq("operation_id", req.getOperationId()).eq("object_id", req.getLabelId()).eq("type", 1));
        questionnaireItemDegreeService.saveBatch(values);
    }
}

/**
 * Alternative using fine-grained locking with ConcurrentHashMap
 * This provides better performance than method-level synchronization
 */
private final ConcurrentHashMap<String, Object> lockMap = new ConcurrentHashMap<>();

@Override
@Transactional(rollbackFor = Exception.class)
public void saveAndUpdateWithFineLock(QuestionnaireContentReq req) {
    String lockKey = req.getOperationId() + ":" + req.getLabelId();
    Object lock = lockMap.computeIfAbsent(lockKey, k -> new Object());
    
    synchronized (lock) {
        try {
            // Same implementation as above
            saveAndUpdateInternal(req);
        } finally {
            // Clean up lock if no other threads are waiting
            lockMap.remove(lockKey, lock);
        }
    }
}
