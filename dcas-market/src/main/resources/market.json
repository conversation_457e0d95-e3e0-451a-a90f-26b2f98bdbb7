{"valid": true, "violations": [], "id": "dcas_market", "icon": "http://*************:1008/icon/logo.png", "lastModified": 1715684167, "name": "dcas_market", "title": "DCAS能力市场", "apps": [{"valid": true, "buildIn": false, "violations": [], "id": "dcas-assistant", "lastModified": 1715684167, "icon": "http://*************:1008/icon/ai.png", "readMe": "## dcas-assistant\n\ndcas-assistant内置智能分析模型", "description": "内置智能分析模型", "name": "dcas-assistant", "tags": ["AI / 大模型"], "title": "内置智能分析模型", "additionalProperties": {"key": "dcas-assistant", "name": "dcas-assistant", "tags": ["AI"], "shortDescZh": "内置智能分析模型", "shortDescEn": "llama3-based AI Assistant", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "github": "https://github.com/zfile-dev/zfile", "document": "https://docs.zfile.vip/"}, "versions": [{"valid": true, "violations": [], "id": "2-0-0", "readMe": null, "name": "2.0.0", "lastModified": 1715684167, "files": [{"name": "dcas-assistant-2.0.0.tar.gz", "size": 7745, "lastModified": "2024-05-14T10:56:07.000+00:00"}], "downloadUrl": "http://*************:1008/dcas-assistant-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-assistant/versions/2-0-0/download-callback?t=1715684167", "additionalProperties": {"formFields": [{"defaultValue": 8000, "edit": false, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "http://*************:1008/dcas-assistant-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-assistant/versions/2-0-0/download-callback?t=1715684167"}, {"valid": true, "buildIn": false, "violations": [], "id": "dcas-dataway", "lastModified": 1715684167, "icon": "http://*************:1008/icon/dataway.png", "readMe": "# Dataway 数据接口配置服务\n\n&emsp;&emsp;依托 DataQL 服务聚合能力，为应用提供一个 UI 界面。并以 jar 包的方式集成到应用中。\n通过 Dataway 可以直接在界面上配置和发布接口。\n\n&emsp;&emsp;这种模式的革新使得开发一个接口不必在编写任何形式的代码，只需要配置一条 DataQL 查询即可完成满足前端对接口的需求。\n从而避免了从数据库到前端之间一系列的开发配置任务，例如：Mapper、DO、DAO、Service、Controller 统统不在需要。\n\n&emsp;&emsp;Dataway特意采用了 jar包集成的方式发布，这使得任意的老项目都可以无侵入的集成 Dataway。\n直接改进老项目的迭代效率，大大减少企业项目研发成本。\n\n![avatar](https://www.hasor.net/web/_images/CC2_A633_6D5C_MK4L.png)\n\n&emsp;&emsp;如上图所示 Dataway 在开发模式上提供了巨大的便捷。\n虽然工作流程中标识了由后端开发来配置 DataQL 接口，但这主要是出于考虑接口责任人。\n但在实际工作中根据实际情况需要，配置接口的人员可以是产品研发生命周期中任意一名角色。\n\n----------\n## 主打场景\n\n&emsp;&emsp;主打场景并不是说 Dataway 适用范围仅限于此，而是经过多次项目实践。我们认为下面这些场景会有非常好的预期效果。\n比如说 ``取数据`` 在一些报表、看板项目中即便是取数据逻辑在复杂。我们依然做到了真正的 零 开发，所有取数逻辑全部通过 DataQL + SQL 的方式满足。\n对比往期项目对于后端技术人员的需求从 3～5 人的苦逼通宵加班，直接缩减为 1人配置化搞定。\n\n&emsp;&emsp;再比如，某个内部类 ERP 项目，20多个表单页面，后端部分仅有 1000 行左右的核心代码。其它数据存取逻辑全部配置化完成。\n\n\n01. 取数据\n    - 如果你只想从数据库或者服务中获取某类数据，不需要： VO、BO、Convert、DO、Mapper 这类东西。\n02. 存数据\n    - 如果是从页面表单递交数据到数据库或者服务，免去 BO、FormBean、DO、Mapper 这类东西。\n03. 数据聚合\n    - 基于服务调用结果经过结构转换并响应给前端。\n    - 将数据库和服务等多个结果进行汇聚然后返回给前端。\n\n----------\n## 技术架构\n\n![avatar](https://www.hasor.net/web/_images/CC2_B633_6D5C_MK4L.png)\n\n&emsp;&emsp;刚一接触 DataQL 可能会有一种错觉认为 DataQL 是一个高级别的 ORM 工具。\n这一点需要澄清。DataQL 的竞品应是 GraphQL，而非 ORM 框架。\n\n&emsp;&emsp;ORM 类框架有一个最大的特点是具有 Mapping 过程，然后通过框架在进行 CURD 操作。\n例如：Mybatis、Hibernate。其中有一些甚至做到了更高级的界面化例如： apijson，但其本质依然是 ORM。\n\n&emsp;&emsp;而 DataQL 有很大不同。虽然 DataQL 提供了非常出色的基于 SQL 数据存取能力。但从技术架构上来审视，可以看出它并不是 ORM 框架。\n它没有 ORM 中最关键的 Mapping 过程。DataQL 专注的是：结果转换、数据和服务的聚合查询。\n\n&emsp;&emsp;造成 ORM 错觉的是由于 DataQL 充分利用 Udf 和 Fragment 奇妙的组合，提供了更便捷的数据库存储逻辑配置化而已。\n\n----------\n## Spring 中使用 Dataway\n\n&emsp;&emsp;Dataway 是 Hasor 生态中的一员，使用 Dataway 第一步需要通过 [hasor-spring](https://www.hasor.net/web/spring/index.html) 打通两个生态。\n\n```xml\n<!-- 引入依赖 -->\n<dependency>\n    <groupId>net.hasor</groupId>\n    <artifactId>hasor-spring</artifactId>\n    <version>4.1.3</version>\n</dependency>\n<dependency>\n    <groupId>net.hasor</groupId>\n    <artifactId>hasor-dataway</artifactId>\n    <version>4.1.3</version>\n</dependency>\n```\n\n```java\n@EnableHasor()      // 在Spring 中启用 Hasor\n@EnableHasorWeb()   // 将 hasor-web 配置到 Spring 环境中，Dataway 的 UI 是通过 hasor-web 提供服务。\n```\n\n&emsp;&emsp;然后第二步，在应用的 `application.properties` 配置文件中启用 Dataway\n\n```properties\n# 启用 Dataway 功能（默认不启用）\nHASOR_DATAQL_DATAWAY=true\n# 开启 ui 管理功能（注意生产环境必须要设置为 false，否则会造成严重的生产安全事故）\nHASOR_DATAQL_DATAWAY_ADMIN=true\n\n# （可选）API工作路径\nHASOR_DATAQL_DATAWAY_API_URL=/api/\n# （可选）ui 的工作路径，只有开启 ui 管理功能后才有效\nHASOR_DATAQL_DATAWAY_UI_URL=/interface-ui/\n```\n\n&emsp;&emsp;第三步，初始化 dataway 的必要数据库表。\n\n```sql\nCREATE TABLE `interface_info` (\n    `api_id`          int(11)      NOT NULL AUTO_INCREMENT   COMMENT 'ID',\n    `api_method`      varchar(12)  NOT NULL                  COMMENT 'HttpMethod：GET、PUT、POST',\n    `api_path`        varchar(512) NOT NULL                  COMMENT '拦截路径',\n    `api_status`      int(2)       NOT NULL                  COMMENT '状态：0草稿，1发布，2有变更，3禁用',\n    `api_comment`     varchar(255)     NULL                  COMMENT '注释',\n    `api_type`        varchar(24)  NOT NULL                  COMMENT '脚本类型：SQL、DataQL',\n    `api_script`      mediumtext   NOT NULL                  COMMENT '查询脚本：xxxxxxx',\n    `api_schema`      mediumtext       NULL                  COMMENT '接口的请求/响应数据结构',\n    `api_sample`      mediumtext       NULL                  COMMENT '请求/响应/请求头样本数据',\n    `api_create_time` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n    `api_gmt_time`    datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',\n    PRIMARY KEY (`api_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='Dataway 中的API';\n\nCREATE TABLE `interface_release` (\n    `pub_id`          int(11)      NOT NULL AUTO_INCREMENT   COMMENT 'Publish ID',\n    `pub_api_id`      int(11)      NOT NULL                  COMMENT '所属API ID',\n    `pub_method`      varchar(12)  NOT NULL                  COMMENT 'HttpMethod：GET、PUT、POST',\n    `pub_path`        varchar(512) NOT NULL                  COMMENT '拦截路径',\n    `pub_status`      int(2)       NOT NULL                  COMMENT '状态：0有效，1无效（可能被下线）',\n    `pub_type`        varchar(24)  NOT NULL                  COMMENT '脚本类型：SQL、DataQL',\n    `pub_script`      mediumtext   NOT NULL                  COMMENT '查询脚本：xxxxxxx',\n    `pub_script_ori`  mediumtext   NOT NULL                  COMMENT '原始查询脚本，仅当类型为SQL时不同',\n    `pub_schema`      mediumtext       NULL                  COMMENT '接口的请求/响应数据结构',\n    `pub_sample`      mediumtext       NULL                  COMMENT '请求/响应/请求头样本数据',\n    `pub_release_time`datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间（下线不更新）',\n    PRIMARY KEY (`pub_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='Dataway API 发布历史。';\n\ncreate index idx_interface_release on interface_release (pub_api_id);\n```\n\n&emsp;&emsp;最后一步，将 Spring 使用的数据源导入到 Hasor 环境共 Dataway 使用。\n\n```java\n@DimModule\n@Component\npublic class ExampleModule implements SpringModule {\n    @Autowired\n    private DataSource dataSource = null;\n\n    @Override\n    public void loadModule(ApiBinder apiBinder) throws Throwable {\n        // .DataSource form Spring boot into Hasor\n        apiBinder.installModule(new JdbcModule(Level.Full, this.dataSource));\n        // .custom DataQL\n        //apiBinder.tryCast(QueryApiBinder.class).loadUdfSource(apiBinder.findClass(DimUdfSource.class));\n        //apiBinder.tryCast(QueryApiBinder.class).bindFragment(\"sql\", SqlFragment.class);\n    }\n}\n```\n\n----------\n## 使用UI配置接口\n\n&emsp;&emsp;启动工程，访问 http://127.0.0.1:8080/interface-ui/ 就可以看到下面页面了。\n\n![avatar](https://www.hasor.net/web/_images/CC2_C633_6D5C_MK4L.png)\n\n![avatar](https://www.hasor.net/web/_images/CC2_D633_6D5C_MK4L.png)\n\n发布接口需要先进行冒烟测试，冒烟通过之后就可以点亮发布按钮。接口只有在发布之后才能在 Api List 页面中调用，前端才可以正常访问。\n\n\n## FAQ：\n\n&emsp;&emsp;拿到源码直接倒入工程后发现有一些类缺失是什么问题？ 答：请先执行一下 \"mvn compile\"。原因是：dataway的 dao 层采用的是 DataQL 方案。", "description": "基于DataQL服务聚合能力，通过DataQL语言实现接口的聚合和定义，从而消除了传统开发中的繁琐代码编写过程", "name": "dataway", "tags": ["本地"], "title": "基于DataQL服务聚合能力实现的接口服务", "additionalProperties": {"key": "dcas-dataway", "name": "dcas-dataway", "tags": ["Local"], "shortDescZh": "基于DataQL服务聚合能力实现的接口服务", "shortDescEn": "DataQL-based Interface service", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "website": "https://${ip}:4433/dcas-dataway/dcas/interface-ui/", "github": "https://gitee.com/zycgit/dataql/tree/dev/dataql-dataway", "document": "https://www.dataql.net/docs/dataway/overview"}, "versions": [{"valid": true, "violations": [], "id": "1-0-0", "readMe": null, "name": "1.0.0", "lastModified": 1715684167, "files": [{"name": "dcas-dataway-1.0.0.tar.gz", "size": 7745, "lastModified": "2024-05-14T10:56:07.000+00:00"}], "downloadUrl": "http://*************:1008/dcas-dataway-1.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dataway/versions/4-2-5/download-callback?t=1715684167", "additionalProperties": {"formFields": [{"defaultValue": 9091, "edit": true, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "http://*************:1008/dcas-dataway-1.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dataway/versions/1-0-0/download-callback?t=1715684167"}, {"valid": true, "buildIn": false, "violations": [], "id": "dcas-compare", "lastModified": 1715684167, "icon": "http://*************:1008/icon/compare.png", "readMe": "# DCAS-COMPARE 脱敏加密检测服务\n\n&emsp;&emsp;", "description": "脱敏加密检测服务", "name": "dcas-compare", "tags": ["本地"], "title": "脱敏加密检测服务", "additionalProperties": {"key": "dcas-compare", "name": "dcas-compare", "tags": ["Local"], "shortDescZh": "脱敏加密检测服务", "shortDescEn": "Desensitization encryption detection", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "website": "https://${ip}:4433/dcas-compare/", "github": "https://gitee.com/zycgit/dataql/tree/dev/dataql-dataway", "document": "https://www.dataql.net/docs/dataway/overview"}, "versions": [{"valid": true, "violations": [], "id": "2-0-0", "readMe": null, "name": "2.0.0", "lastModified": 1715684167, "files": [{"name": "dcas-compare-2.0.0.tar.gz", "size": 7745, "lastModified": "2024-05-14T10:56:07.000+00:00"}], "downloadUrl": "http://*************:1008/dcas-compare-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-compare/versions/2-0-0/download-callback?t=1715684167", "additionalProperties": {"formFields": [{"defaultValue": 8083, "edit": false, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "http://*************:1008/dcas-compare-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-compare/versions/2-0-0/download-callback?t=1715684167"}, {"valid": true, "buildIn": false, "violations": [], "id": "dcas-pcap", "lastModified": 1724135568, "icon": "http://*************:1008/icon/pcap.png", "readMe": "# DCAS-PCAP 网络流量发现组件\n\n&emsp;&emsp;", "description": "网络流量发现组件", "name": "dcas-pcap", "tags": ["本地"], "title": "网络流量发现组件", "additionalProperties": {"key": "dcas-pcap", "name": "dcas-pcap", "tags": ["Local"], "shortDescZh": "网络流量发现组件", "shortDescEn": "Network traffic discovery component", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "website": "https://${ip}:4433/pcap/", "github": "", "document": ""}, "versions": [{"valid": true, "violations": [], "id": "2-0-0", "readMe": null, "name": "2.0.0", "lastModified": 1724135568, "files": [{"name": "dcas-pcap-2.0.0.tar.gz", "size": 8820, "lastModified": "2024-08-20T14:36:00.000+00:00"}], "downloadUrl": "http://*************:1008/dcas-pcap-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-pcap/versions/2-0-0/download-callback?t=1724135568", "additionalProperties": {"formFields": [{"defaultValue": 8090, "edit": false, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "http://*************:1008/dcas-pcap-2.0.0.tar.gz", "downloadCallbackUrl": "http://*************:1008/dcas-pcap/versions/2-0-0/download-callback?t=1715684167"}, {"valid": true, "buildIn": true, "violations": [], "id": "dcas-authority", "lastModified": 1715684167, "icon": "http://*************:1008/icon/auth.png", "readMe": "# DCAS-AUTHORITY 访问权限检测服务\n\n&emsp;&emsp;", "description": "访问权限检测服务", "name": "dcas-authority", "tags": ["本地"], "title": "访问权限检测服务", "additionalProperties": {"key": "dcas-authority", "name": "dcas-authority", "tags": ["Local"], "shortDescZh": "访问权限检测服务", "shortDescEn": "Access permission detection service", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "website": "https://${ip}:4433/#/accessPermissions", "github": "", "document": ""}, "versions": [{"valid": true, "violations": [], "id": "1-0-0", "readMe": null, "name": "1.0.0", "lastModified": 1715684167, "files": [{"name": "dcas-authority-1.0.0.tar.gz", "size": 7745, "lastModified": "2024-05-14T10:56:07.000+00:00"}], "downloadUrl": "", "downloadCallbackUrl": "", "additionalProperties": {"formFields": [{"defaultValue": 8088, "edit": false, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "", "downloadCallbackUrl": ""}, {"valid": true, "buildIn": true, "violations": [], "id": "dcas-capability", "lastModified": 1715684167, "icon": "http://*************:1008/icon/auth.png", "readMe": "# DCAS-CAPABILITY 数据安全能力验证\n\n&emsp;&emsp;", "description": "数据安全能力验证", "name": "dcas-capability", "tags": ["本地"], "title": "数据安全能力验证", "additionalProperties": {"key": "dcas-capability", "name": "dcas-capability", "tags": ["Local"], "shortDescZh": "数据安全能力验证", "shortDescEn": "Access permission detection service", "type": "tool", "crossVersionUpdate": false, "limit": 1, "recommend": 0, "website": "https://${ip}:4433/#/capabilityVerification", "github": "", "document": ""}, "versions": [{"valid": true, "violations": [], "id": "1-0-0", "readMe": null, "name": "1.0.0", "lastModified": 1715684167, "files": [{"name": "dcas-capability-1.0.0.tar.gz", "size": 7745, "lastModified": "2024-05-14T10:56:07.000+00:00"}], "downloadUrl": "", "downloadCallbackUrl": "", "additionalProperties": {"formFields": [{"defaultValue": 8089, "edit": false, "envKey": "DCAS_APP_PORT_HTTP", "labelEn": "Port", "labelZh": "端口", "required": true, "rule": "paramPort", "type": "number"}]}}], "downloads": 0, "downloadUrl": "", "downloadCallbackUrl": ""}], "additionalProperties": {"tags": [{"key": "WebSite", "name": "建站", "sort": 1}, {"key": "Database", "name": "数据库", "sort": 2}, {"key": "Server", "name": "Web 服务器", "sort": 3}, {"key": "Runtime", "name": "运行环境", "sort": 4}, {"key": "Tool", "name": "实用工具", "sort": 5}, {"key": "Storage", "name": "云存储", "sort": 6}, {"key": "AI", "name": "AI / 大模型", "sort": 7}, {"key": "BI", "name": "BI", "sort": 8}, {"key": "Security", "name": "安全", "sort": 9}, {"key": "DevTool", "name": "开发工具", "sort": 10}, {"key": "DevOps", "name": "DevOps", "sort": 11}, {"key": "Middleware", "name": "中间件", "sort": 12}, {"key": "Media", "name": "多媒体", "sort": 13}, {"key": "Email", "name": "邮件服务", "sort": 14}, {"key": "Game", "name": "休闲游戏", "sort": 15}, {"key": "Local", "name": "本地", "sort": 99}], "version": "v1.0.0"}}