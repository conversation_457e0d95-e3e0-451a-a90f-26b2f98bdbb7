package com.dcas.market.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.AbilityType;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.TreeLabelDTO;
import com.dcas.common.model.req.ApiJobQueryReq;
import com.dcas.common.model.vo.*;
import com.dcas.market.app.constant.*;
import com.dcas.market.app.model.*;
import com.dcas.market.app.model.vo.AppDetailVO;
import com.dcas.market.app.model.vo.AppInstalledVO;
import com.dcas.market.app.model.vo.AppVO;
import com.dcas.market.app.service.*;
import com.dcas.market.config.MarketConfig;
import com.dcas.market.config.SshConfig;
import com.dcas.market.factory.MkAppJobContext;
import com.dcas.market.task.AsyncUpTask;
import com.dcas.market.utils.AppUtil;
import com.dcas.market.utils.ComposeUtil;
import com.dcas.market.utils.IpUtil;
import com.dcas.market.utils.ShellUtil;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.command.*;
import com.github.dockerjava.api.model.Container;
import com.github.dockerjava.api.model.Network;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.mchz.dcas.client.common.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.yaml.snakeyaml.Yaml;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.dcas.market.app.constant.AppConstant.*;
import static com.dcas.market.app.constant.ContainerConstant.DCAS_NETWORK;

/**
 * <AUTHOR>
 * @className AppServiceImpl
 * @description 应用App实现类
 * @date 2024/05/27 16:13
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class AppServiceImpl implements IAppService {

    private final IMkAppService iMkAppService;
    private final IMkAppInstallService iMkAppInstallService;
    private final MkAppInstallMapper mkAppInstallMapper;
    private final IMkAppDetailService iMkAppDetailService;
    private final IMkAppTagService iMkAppTagService;
    private final IMkTagService iMkTagService;
    private final DockerClient dockerClient;
    private final MarketConfig marketConfig;
    private final AsyncUpTask asyncUpTask;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;

    /**
     * 匹配${}正则
     */
    private final static String  REGEX = "\\$\\{(.+?)\\}";
    private final MkAppJobMapper mkAppJobMapper;

    @Override
    public PageInfo<AppVO> getAppList(Integer currentPage, Integer pageSize, String name) throws IOException {
        PageInfo<MkApp> pageInfo = iMkAppService.pageApp(currentPage, pageSize, name);
        if (CollUtil.isNotEmpty(pageInfo.getList())) {
            List<AppVO> list = new ArrayList<>();
            List<MkAppTag> appTags = iMkAppTagService.list();
            Map<Integer, MkTag> tagMap =
                iMkTagService.list().stream().collect(Collectors.toMap(MkTag::getId, Function.identity()));
            Map<Integer, List<MkAppTag>> appTagMap = new HashMap<>(16);
            if (CollUtil.isNotEmpty(appTags)) {
                appTagMap = appTags.stream().collect(Collectors.groupingBy(MkAppTag::getAppId));
            }
            List<MkAppInstall> mkAppInstalls = iMkAppInstallService.list();
            Map<Integer, List<MkAppInstall>> appInstallMap = new HashMap<>(16);
            if (CollUtil.isNotEmpty(mkAppInstalls)) {
                appInstallMap = mkAppInstalls.stream().collect(Collectors.groupingBy(MkAppInstall::getAppId));
            }
            List<MkAppDetail> appDetails = iMkAppDetailService.list();
            Map<Integer, List<MkAppDetail>> appDetailsMap = new HashMap<>(16);
            if (CollUtil.isNotEmpty(appDetails)) {
                appDetailsMap = appDetails.stream().collect(Collectors.groupingBy(MkAppDetail::getAppId));
            }
            for (MkApp mkApp : pageInfo.getList()) {
                AppVO app = new AppVO();
                BeanUtils.copyProperties(mkApp, app);
                if (appTagMap.containsKey(mkApp.getId())) {
                    app.setTags(appTagMap.get(mkApp.getId()).stream().map(appTag -> tagMap.get(appTag.getTagId()))
                        .collect(Collectors.toList()));
                }
                app.setLimit(mkApp.getLimitCount());
                if (appInstallMap.containsKey(mkApp.getId())) {
                    List<MkAppInstall> mkAppInstallList = appInstallMap.get(mkApp.getId());
                    app.setInstalled(mkAppInstallList.size());
                    app.setStatus(mkAppInstallList.stream().findFirst().get().getStatus());
                } else {
                    app.setStatus(APP_NORMAL);
                    app.setInstalled(0);
                }
                if (appDetailsMap.containsKey(mkApp.getId())) {
                    app.setVersions(appDetailsMap.get(mkApp.getId()).stream().map(MkAppDetail::getVersion)
                        .collect(Collectors.toList()));
                }
                list.add(app);
            }
            PageInfo<AppVO> page = new PageInfo<>();
            BeanUtils.copyProperties(pageInfo, page);
            page.setList(list);
            return page;
        }
        return new PageInfo<>();
    }

    @Override
    public PageInfo<AppInstalledVO> getInstalledAppList(Integer currentPage, Integer pageSize, String name) {
        try (Page<Object> ignored = PageMethod.startPage(currentPage, pageSize)) {
            List<MkAppInstall> appInstallList = mkAppInstallMapper.selectList(
                new QueryWrapper<MkAppInstall>().like(CharSequenceUtil.isNotEmpty(name), "name", name));
            List<AppInstalledVO> list = new ArrayList<>();
            List<MkApp> apps = iMkAppService.list();
            Map<Integer, MkApp> appMap = new HashMap<>(16);
            if (CollUtil.isNotEmpty(apps)) {
                appMap = apps.stream().collect(Collectors.toMap(MkApp::getId, Function.identity()));
            }
            for (MkAppInstall mkAppInstall : appInstallList) {
                AppInstalledVO app = new AppInstalledVO();
                BeanUtils.copyProperties(mkAppInstall, app);
                if (appMap.containsKey(mkAppInstall.getAppId())) {
                    MkApp mkApp = appMap.get(mkAppInstall.getAppId());
                    AppVO appVO = new AppVO();
                    BeanUtils.copyProperties(mkApp, appVO);
                    app.setApp(appVO);
                }
                list.add(app);
            }
            return new PageInfo<>(list);
        }
    }

    @Override
    public Boolean installApp(InstallParam installParam) {
        MkAppInstall mkAppInstall = MkAppInstall.builder().build();
        checkSshConnection();
        // 创建默认docker network
        if (!createDefaultDockerNetwork()) {
            log.error("不能创建默认的docker network");
            throw new ServiceException("can't create default docker network");
        }
        List<MkAppInstall> appInstallList =
            iMkAppInstallService.list(new QueryWrapper<MkAppInstall>().eq("name", installParam.getName()));
        if (CollUtil.isNotEmpty(appInstallList)) {
            log.warn("app应用【{}】已安装！", installParam.getName());
            throw new ServiceException("app应用已安装！");
        }
        // 根据appId和版本号查询app详情
        MkAppDetail appDetail = iMkAppDetailService.getOne(
            new QueryWrapper<MkAppDetail>().eq("app_id", installParam.getAppId())
                .eq("version", installParam.getVersion()));
        MkApp app = iMkAppService.getById(appDetail.getAppId());
        String ip = CharSequenceUtil.subBetween(appDetail.getDownloadUrl(), HTTP_IP_PREFIX, HTTP_IP_SUFFIX);
//        if (!NetUtil.ping(ip, 1000)){
//            log.warn("ping {} failed!", ip);
//            throw new ServiceException("设备网络问题，无法安装!");
//        }
        int httpPort = 0;
        int httpsPort = 0;
        for (Map.Entry<String, Object> entry : installParam.getParams().entrySet()) {
            if (!entry.getKey().startsWith(DCAS_APP_PORT_PREFIX)) {
                continue;
            }
            if (DCAS_APP_PORT_HTTP.equals(entry.getKey())) {
                httpPort = (Integer)entry.getValue();
                if (checkPort(entry.getKey(), httpPort)) {
                    log.warn("{} 端口已被占用！", httpPort);
                    throw new ServiceException(httpPort + "端口已被占用！");
                }
            } else if (AppConstant.DCAS_APP_PORT_HTTPS.equals(entry.getKey())) {
                httpsPort = (Integer)entry.getValue();
                if (checkPort(entry.getKey(), httpsPort)) {
                    log.warn("{} 端口已被占用！", httpsPort);
                    throw new ServiceException(httpsPort + "端口已被占用！");
                }
            }
        }
        // 检查app安装是否超过限制
        if (checkRequiredAndLimit(app, appInstallList.size())) {
            log.warn("超出安装限制数！{}", app.getLimitCount());
            throw new ServiceException("超出安装限制数！无法安装" + app.getLimitCount());
        }

        String containerName = checkContainerName(installParam, app.getKey());
        installParam.setContainerName(containerName);

        Map<String, Object> envMap = new HashMap<>(16);
        envMap.put(AppConstant.CONTAINER_NAME, containerName);
        envMap.put(AppConstant.DCAS_APP_PORT_HTTP, httpPort);
        envMap.put(AppConstant.DCAS_APP_PORT_HTTPS, httpsPort);
        if (!addDockerComposeCommonParam(appDetail.getDockerCompose(), installParam, envMap)) {
            return false;
        }
        try {
            // 保存记录到数据库
            mkAppInstall =
                MkAppInstall.builder().appId(installParam.getAppId()).appDetailId(installParam.getAppDetailId())
                    .name(installParam.getName()).containerName(installParam.getContainerName())
                    .serviceName(installParam.getName()).dockerCompose(installParam.getDockerCompose())
                    .httpPort(httpPort).httpsPort(httpsPort).createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now()).version(installParam.getVersion()).status(INSTALLING)
                    .key(app.getKey()).env(JSONUtil.toJsonStr(envMap)).build();
            iMkAppInstallService.save(mkAppInstall);
            // 安装应用、更新状态
            asyncUpTask.installAddAndUpdateStatus(installParam, app, mkAppInstall, envMap);
            log.info("docker compose up finished!");
            return true;
        } catch (Exception e){
            log.error("installed failed! prepare to rollback...", e);
            OpParam opParam = new OpParam();
            opParam.setOperate(OperateTypeEnum.UNINSTALL.getType());
            opParam.setInstallId(mkAppInstall.getId());
            try {
                op(opParam);
            } catch (Exception ex) {
                log.error("failed to rollback", ex);
            }
            return false;
        }
    }

    private void checkSshConnection() {
        ShellUtil shellUtil = new ShellUtil();
        if (!shellUtil.testConnect()){
            log.error("ssh连接失败:{}", shellUtil.toString());
            throw new IllegalStateException("app安装失败");
        }
    }

    private String checkContainerName(InstallParam installParam, String key) {
        String containerName = CONTAINE_RPREFIX + key + "-" + RandomUtil.randomString(4);
        if (installParam.isAdvanced() && CharSequenceUtil.isNotEmpty(installParam.getContainerName())) {
            containerName = installParam.getContainerName();
            // 数据库列表校验
            if (iMkAppInstallService.count(new QueryWrapper<MkAppInstall>().eq(ContainerConstant.CONTAINER_NAME, containerName)) > 0) {
                log.warn("存在相同容器名！{}", containerName);
                throw new ServiceException("存在相同容器名！");
            }
            // 容器列表校验
            if (checkContainerNameIsExist(containerName)) {
                log.warn("存在相同容器名！{}", containerName);
                throw new ServiceException("存在相同容器名！");
            }
        }
        return containerName;
    }


    private boolean checkPort(String key, int port) {
        switch (key) {
            case DCAS_APP_PORT_HTTP:
                if (iMkAppInstallService.count(new QueryWrapper<MkAppInstall>().eq("http_port", port)) > 0) {
                    return true;
                }
                break;
            case DCAS_APP_PORT_HTTPS:
                if (iMkAppInstallService.count(new QueryWrapper<MkAppInstall>().eq("https_port", port)) > 0) {
                    return true;
                }
                break;
            default:
                return false;
        }
        return false;
    }


    private boolean checkContainerNameIsExist(String containerName) {
        try (ListContainersCmd listContainersCmd = dockerClient.listContainersCmd()
            .withNameFilter(Collections.singletonList(containerName))) {
            List<Container> containers = listContainersCmd.exec();
            if (!containers.isEmpty()) {
                return true;
            }
        }
        return false;
    }

    private boolean addDockerComposeCommonParam(String dockerCompose, InstallParam installParam,
        Map<String, Object> envMap) {
        Yaml yaml = new Yaml();
        Map<String, Object> dockerComposeMap;
        if (installParam.isEditCompose()) {
            dockerComposeMap = yaml.load(installParam.getDockerCompose());
        } else {
            dockerComposeMap = yaml.load(dockerCompose);
        }
        if (CollUtil.isEmpty(dockerComposeMap)) {
            log.error("docker-compose.yml格式错误！{}", dockerComposeMap);
            throw new ServiceException("docker-compose.yml格式错误！");
        }

        if (!dockerComposeMap.containsKey("services")) {
            log.error("docker-compose.yml格式错误！");
            return false;
        }
        Map<String,Object> servicesMap = (Map<String, Object>)dockerComposeMap.get("services");
        Map<String, Object> serviceMap = new HashMap<>(16);
        if (servicesMap.containsKey(installParam.getName())) {
            serviceMap = (Map<String, Object>)servicesMap.get(installParam.getName());
            if (serviceMap.containsKey("ports")) {
                List<String> portsList = (List<String>)serviceMap.get("ports");
                // 替换compose文件中的http端口
                String port = String.valueOf(envMap.get(DCAS_APP_PORT_HTTP));
                log.info("replace port: {}",  port);
                portsList =
                    portsList.stream().map(s -> s.replace(AppConstant.$PORT, port)).collect(Collectors.toList());
                serviceMap.put("ports",portsList);
                installParam.setEditCompose(true);
            }
            String hostIp = "127.0.0.1";
            if (installParam.isAllowPort()) {
                hostIp = "";
            }
            // 替换环境变量IP
            ShellUtil shellUtil = new ShellUtil();
            try{
                IpUtil ipUtil = new IpUtil(shellUtil);
                if (serviceMap.containsKey("environment")) {
                    List<String> envList = (List<String>)serviceMap.get("environment");
                    hostIp = ipUtil.getLocalIp();
                    log.info("replace environment ip: {}", hostIp);
                    String finalHostIp = hostIp;
                    envList = envList.stream().map(s -> s.replace($IP, finalHostIp)).collect(Collectors.toList());
                    serviceMap.put("environment", envList);
                }
                envMap.put(HOST_IP, hostIp);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        if (CharSequenceUtil.isNotEmpty(installParam.getContainerName())) {
            Pattern pattern = Pattern.compile(REGEX);
            // 判断compose文件中不包含容器名或者包含容器名且容器名包含${}，则替换自动生成的容器名
            if (!serviceMap.containsKey(ContainerConstant.CONTAINER_NAME) || serviceMap.containsKey(
                ContainerConstant.CONTAINER_NAME) && pattern.matcher(
                (String)serviceMap.get(ContainerConstant.CONTAINER_NAME)).find()) {
                log.info("replace container name with {}", installParam.getContainerName());
                serviceMap.put(ContainerConstant.CONTAINER_NAME, installParam.getContainerName());
                installParam.setEditCompose(true);
            } else {
                installParam.setContainerName((String)serviceMap.get(ContainerConstant.CONTAINER_NAME));
                log.info("update container name with {}", serviceMap.get(ContainerConstant.CONTAINER_NAME));
            }

        }
        if (installParam.isAdvanced()) {
            if (installParam.getCpuQuota() > 0) {
                envMap.put(CPUS, installParam.getCpuQuota());
            }
            if (installParam.getMemoryLimit() > 0) {
                envMap.put(MEMORY_LIMIT, installParam.getMemoryLimit() + installParam.getMemoryUnit());
            }
            if (serviceMap.containsKey("deploy")){
                Map<String, Object> deployMap = (Map<String, Object>)serviceMap.get("deploy");
                Map<String, Object> resourcesMap = new HashMap<>(16);
                Map<String, Object> limitsMap = new HashMap<>(16);
                if (installParam.getCpuQuota() > 0) {
                    limitsMap.put("cpus", installParam.getCpuQuota());
                }
                if (installParam.getMemoryLimit() > 0) {
                    limitsMap.put("memory", CharSequenceUtil.concat(true, installParam.getMemoryLimit().toString(),
                        installParam.getMemoryUnit()));
                }
                if (!limitsMap.isEmpty()) {
                    resourcesMap.put("limits", limitsMap);
                }
                if (!resourcesMap.isEmpty()) {
                    deployMap.put("resources", resourcesMap);
                }
                if (CollUtil.isNotEmpty(deployMap)) {
                    serviceMap.put("deploy", deployMap);
                }
            } else {
                Map<String, Object> deployMap = new HashMap<>(16);
                Map<String, Object> resourcesMap = new HashMap<>(16);
                Map<String, Object> limitsMap = new HashMap<>(16);
                if (installParam.getCpuQuota() > 0) {
                    limitsMap.put("cpus", installParam.getCpuQuota());
                }
                if (installParam.getMemoryLimit() > 0) {
                    limitsMap.put("memory", CharSequenceUtil.concat(true, installParam.getMemoryLimit().toString(),
                        installParam.getMemoryUnit()));
                }
                if (!limitsMap.isEmpty()) {
                    resourcesMap.put("limits", limitsMap);
                }
                if (!resourcesMap.isEmpty()) {
                    deployMap.put("resources", resourcesMap);
                }
                if (CollUtil.isNotEmpty(deployMap)) {
                    serviceMap.put("deploy", deployMap);
                }
            }
        }
        // 更新compose文件
        installParam.setDockerCompose(yaml.dumpAsMap(dockerComposeMap));
        return true;
    }

    private boolean checkRequiredAndLimit(MkApp app, int installedSize) {
        return app.getLimitCount() > 0 && installedSize >= app.getLimitCount();
    }

    private boolean createDefaultDockerNetwork() {
        ListNetworksCmd listNetworksCmd = dockerClient.listNetworksCmd().withNameFilter(DCAS_NETWORK);
        List<Network> networks = listNetworksCmd.exec();
        if (CollUtil.isEmpty(networks)) {
            try (CreateNetworkCmd createNetworkCmd = dockerClient.createNetworkCmd().withName(DCAS_NETWORK)) {
                createNetworkCmd.exec();
            }
        }
        return true;
    }

    @Override
    public void op(OpParam opParam) {
        OperateTypeEnum operateTypeEnum = OperateTypeEnum.typeOf(opParam.getOperate());
        MkAppInstall appInstall = iMkAppInstallService.getById(opParam.getInstallId());
        String composePath = AppUtil.getComposePath(appInstall.getKey(), appInstall.getName(), marketConfig.getInstallDir());
        String result = "";
        ShellUtil shellUtil = new ShellUtil();
        try{
            ComposeUtil composeUtil = new ComposeUtil(shellUtil);
            log.info("prepare to {} {}", opParam.getOperate(), appInstall.getName());
            switch (Objects.requireNonNull(operateTypeEnum)) {
                case START:
                    result = composeUtil.start(composePath);
                    appInstall.setStatus(RUNNING);
                    iMkAppInstallService.updateById(appInstall);
                    break;
                case STOP:
                    result = composeUtil.stop(composePath);
                    appInstall.setStatus(STOPPED);
                    iMkAppInstallService.updateById(appInstall);
                    break;
                case RESTART:
                    result = composeUtil.restart(composePath);
                    appInstall.setStatus(RUNNING);
                    iMkAppInstallService.updateById(appInstall);
                    break;
                case REBUILD:
                    appInstall.setStatus(REBUILDING);
                    iMkAppInstallService.updateById(appInstall);
                    result = composeUtil.rebuild(composePath);
                    appInstall.setStatus(RUNNING);
                    iMkAppInstallService.updateById(appInstall);
                    break;
                case UNINSTALL:
                case DELETE:
                    result = composeUtil.uninstall(composePath);
                    iMkAppInstallService.removeById(appInstall.getId());
                    // 删除数据库
                    unInstall(appInstall);
                    // 删除安装文件
                    FileUtil.del(AppUtil.getPath(appInstall.getKey(), appInstall.getName(), marketConfig.getInstallDir()));
                    break;
                default:
                    log.warn("{} not implemented!", operateTypeEnum);
                    break;
            }
        } catch (Exception e){
            log.error("执行操作{}失败!",opParam.getOperate(), e);
        }
        log.info(result);
    }

    private void unInstall(MkAppInstall appInstall) {
        log.info("Uninstalling db and delete nginx config file ...");
        ShellUtil shellUtil = new ShellUtil();
        try {
            AppUtil appUtil = new AppUtil(shellUtil);
            appUtil.runScript(OperateTypeEnum.UNINSTALL, AppUtil.getPath(appInstall.getKey(), appInstall.getName(), marketConfig.getInstallDir()));
        } catch (Exception e){
            log.error("卸载失败", e);
        }
    }

    @Override
    public AppDetailVO getAppDetail(Integer appId, String version) {
        MkAppDetail mkAppDetail =
            iMkAppDetailService.getOne(new QueryWrapper<MkAppDetail>().eq("app_id", appId).eq("version", version));
        AppDetailVO appDetailVO = new AppDetailVO();
        BeanUtils.copyProperties(mkAppDetail, appDetailVO);
        List<FormField> formFields = JSONUtil.toList(mkAppDetail.getParams(), FormField.class);
        AppParams appParams = new AppParams(formFields);
        appDetailVO.setParams(appParams);
        MkApp mkApp = iMkAppService.getById(appId);
        AppVO appVO = new AppVO();
        BeanUtils.copyProperties(mkApp, appVO);
        appDetailVO.setApp(appVO);
        return appDetailVO;
    }

    @Override
    public List<App> getAppList(String jsonDir) throws IOException {
        String json = "";
        if (CharSequenceUtil.isEmpty(jsonDir)) {
            ClassPathResource classPathResource = new ClassPathResource(AppConstant.APP_CONFIG_FILE_NAME);
            InputStream inputStream = classPathResource.getInputStream();
            json = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        } else {
            try {
                json = FileUtil.readUtf8String(jsonDir);
            } catch (IORuntimeException e){
                log.error(e.getMessage());
            }
        }
        Market market = JSONUtil.toBean(json, Market.class);
        return market.getApps();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdate(List<App> list) throws Exception {
        if (CollUtil.isNotEmpty(list)) {
            List<MkApp> mkApps = iMkAppService.list();
            List<MkAppDetail> mkAppDetails = iMkAppDetailService.list();
            Map<String, MkApp> appMap = new HashMap<>(16);
            Map<String, MkAppDetail> appDetailMap = new HashMap<>(16);
            if (CollUtil.isNotEmpty(mkApps)) {
                appMap = mkApps.stream().collect(Collectors.toMap(MkApp::getName, Function.identity()));
            }
            if (CollUtil.isNotEmpty(mkAppDetails)) {
                appDetailMap = mkAppDetails.stream()
                    .collect(Collectors.toMap(MkAppDetail::getAppIdWithVersion, Function.identity()));
            }

            // 获取app标签关联表数据
            List<MkAppTag> existAppTags = iMkAppTagService.list();
            Map<String, MkAppTag> appTagMap = new HashMap<>(16);
            if (CollUtil.isNotEmpty(existAppTags)) {
                appTagMap = existAppTags.stream().collect(Collectors.toMap(MkAppTag::getUnique, Function.identity()));
            }
            List<MkAppDetail> appDetails = new ArrayList<>();
            List<MkAppTag> appTags = new ArrayList<>();
            for (App app : list) {
                MkApp mkApp;

                if (appMap.containsKey(app.getId())) {
                    mkApp = appMap.get(app.getId());
                    mergeApp(mkApp, app);
                    iMkAppService.updateById(mkApp);
                    // 版本号不存在 则添加
                    for (Version version : app.getVersions()) {
                        String key = CharSequenceUtil.join(StrPool.DASHED, mkApp.getId(), version.getName());
                        if (!appDetailMap.containsKey(key)) {
                            appDetails.addAll(convertMkAppDetail(app, mkApp.getId(),mkApp.getResource()));
                        }
                    }
                } else {
                    mkApp = convertMkApp(app);
                    iMkAppService.save(mkApp);
                    installBuildInApp(mkApp);
                    List<MkAppDetail> details = convertMkAppDetail(app, mkApp.getId(),mkApp.getResource());
                    appDetails.addAll(details);
                }

                // 遍历更新app标签关联表
                for (String name : app.getTags()) {
                    MkTag tag = iMkTagService.getOne(new QueryWrapper<MkTag>().eq("name", name));
                    String key = mkApp.getId() + "_" + tag.getId();
                    if (appTagMap.containsKey(key)) {
                        continue;
                    }
                    MkAppTag mkAppTag = new MkAppTag();
                    mkAppTag.setTagId(tag.getId());
                    mkAppTag.setAppId(mkApp.getId());
                    mkAppTag.setCreatedAt(LocalDateTime.now());
                    mkAppTag.setUpdatedAt(LocalDateTime.now());
                    appTags.add(mkAppTag);
                }
            }

            // 批量更新或保存app详情表
            iMkAppDetailService.saveOrUpdateBatch(appDetails);
            // 批量更新标签关联表
            iMkAppTagService.saveOrUpdateBatch(appTags);
        }
    }

    private void installBuildInApp(MkApp mkApp) {
        if (!mkApp.getBuildIn())
            return;
        MkAppDetail appDetail = MkAppDetail.builder().appId(mkApp.getId()).version("1.0.0").status("Normal").build();
        iMkAppDetailService.save(appDetail);
        MkAppInstall appInstall = MkAppInstall.builder()
                .name(mkApp.getName())
                .key(mkApp.getKey())
                .appId(mkApp.getId())
                .appDetailId(appDetail.getId())
                .version("1.0.0").status("Running")
                .containerName(mkApp.getName())
                .serviceName(mkApp.getName())
                .httpPort(0)
                .httpsPort(0)
                .createdAt(LocalDateTime.now())
                .build();
        mkAppInstallMapper.insert(appInstall);
    }

    public List<MkAppDetail> convertMkAppDetail(App app, Integer appId, String resource) {
        List<MkAppDetail> list = new ArrayList<>();
        if (Boolean.TRUE.equals(app.getBuildIn())) {
            return list;
        }
        app.getVersions().forEach(version -> {
            String dockerCompose = "";
            if (ComposeConstant.REMOTE.equals(resource)) {
                // 下载docker-compose.yml等配置文件
                try {
                    ComposeUtil.downloadDockerComposeFileToLocal(version.getDownloadUrl(), marketConfig.getRemoteDir());
                } catch (IOException e) {
                    throw new ServiceException(e);
                }

                dockerCompose = ComposeUtil.getDockerComposeContent(app.getId(), version.getName(), marketConfig.getRemoteDir());
            } else {
                dockerCompose = ComposeUtil.getDockerComposeContent(app.getId(), version.getName(), marketConfig.getLocalDir());
            }

            AdditionalProperties<String> additionalProperties = version.getAdditionalProperties();
            MkAppDetail mkAppDetail = MkAppDetail.builder().status(AppConstant.APP_NORMAL)
                .downloadCallBackUrl(version.getDownloadCallbackUrl()).downloadUrl(version.getDownloadUrl())
                .ignoreUpgrade(false).needUpdate(false).createdAt(LocalDateTime.now()).updatedAt(LocalDateTime.now())
                .lastModified(version.getLastModified()).lastVersion(version.getName()).version(version.getName())
                .appId(appId).params(
                    additionalProperties != null && additionalProperties.getFormFields() != null ? JSONUtil.toJsonStr(
                        additionalProperties.getFormFields()) : "")
                .dockerCompose(dockerCompose)
                .build();
            list.add(mkAppDetail);
        });
        return list;
    }

    private MkApp convertMkApp(App app) {
        AdditionalProperties<String> additionalProperties = app.getAdditionalProperties();
        String localFile = String.join(File.separator, marketConfig.getLocalDir(),
            additionalProperties.getKey());
        log.info("local file path: " + localFile);
        MkApp mkApp = MkApp.builder().buildIn(app.getBuildIn()).name(app.getId()).readMe(app.getReadMe())
            .key(additionalProperties.getKey()).limitCount(additionalProperties.getLimit())
            .recommend(additionalProperties.getRecommend()).shortDescEn(additionalProperties.getShortDescEn())
            .shortDescZh(additionalProperties.getShortDescZh()).document(additionalProperties.getDocument())
            .type(additionalProperties.getType()).required("")
            .crossVersionUpdate(additionalProperties.getCrossVersionUpdate()).status(AppConstant.APP_NORMAL)
            .lastModified(app.getLastModified())
            .github(additionalProperties.getGithub()).createdAt(LocalDateTime.now()).updatedAt(LocalDateTime.now())
            .resource(FileUtil.exist(localFile) ? ComposeConstant.LOCAL : ComposeConstant.REMOTE).build();
        ShellUtil shellUtil = new ShellUtil();
        try {
            IpUtil ipUtil = new IpUtil(shellUtil);
            if (additionalProperties.getWebsite() != null) {
                mkApp.setWebsite(additionalProperties.getWebsite().replace($IP, ipUtil.getLocalIp()));
            }
            mkApp.setIcon(getIcon(app.getIcon()));
            return mkApp;
        } catch (Exception e){
            log.error("", e);
            return mkApp;
        }
    }

    private void mergeApp(MkApp mkApp, App app) {
        AdditionalProperties<String> additionalProperties = app.getAdditionalProperties();
        mkApp.setBuildIn(app.getBuildIn());
        mkApp.setDocument(additionalProperties.getDocument());
        mkApp.setLastModified(app.getLastModified());
        mkApp.setReadMe(app.getReadMe());
        mkApp.setKey(additionalProperties.getKey());
        mkApp.setGithub(additionalProperties.getGithub());
        mkApp.setLimitCount(additionalProperties.getLimit());
        mkApp.setGithub(additionalProperties.getGithub());
        mkApp.setDocument(additionalProperties.getDocument());
        mkApp.setRecommend(additionalProperties.getRecommend());
        mkApp.setShortDescEn(additionalProperties.getShortDescEn());
        mkApp.setShortDescZh(additionalProperties.getShortDescZh());
        mkApp.setType(additionalProperties.getType());
        mkApp.setCrossVersionUpdate(additionalProperties.getCrossVersionUpdate());
        String localFile = String.join(File.separator, marketConfig.getLocalDir(),
            additionalProperties.getKey());
        log.info("local file path: " + localFile);
        mkApp.setResource(FileUtil.exist(localFile) ? ComposeConstant.LOCAL : ComposeConstant.REMOTE);
        mkApp.setStatus(AppConstant.APP_NORMAL);
        mkApp.setUpdatedAt(LocalDateTime.now());
        ShellUtil shellUtil = new ShellUtil();
        try{
            IpUtil ipUtil = new IpUtil(shellUtil);
            mkApp.setWebsite(additionalProperties.getWebsite().replace($IP, ipUtil.getLocalIp()));
            mkApp.setIcon(getIcon(app.getIcon()));
        } catch (Exception e){
            log.error("获取本地ip失败!", e);
        }
    }

    private String getIcon(String icon) throws IOException {
        URL url = new URL(icon);
        return Base64.getEncoder().encodeToString(IOUtils.toByteArray(url));
    }

    @Override
    public boolean checkCount(int size) {
        return iMkAppService.count() == size;
    }


    @Override
    public List<MkApp> listAll() {
        return iMkAppService.list();
    }

    @Override
    public List<AbilityModelVO> getAbilityModel() {
        List<MkAppInstall> installs = iMkAppInstallService.list(new QueryWrapper<MkAppInstall>().in("name",
                Arrays.stream(AbilityType.values()).map(AbilityType::getName).collect(Collectors.toSet())));
        Map<String, MkAppInstall> installMap = installs.stream().collect(Collectors.toMap(MkAppInstall::getName, Function.identity()));
        return Arrays.stream(AbilityType.values()).map(a -> {
            MkAppInstall install = installMap.get(a.getName());
            return AbilityModelVO.builder()
                    .name(a.getName())
                    .description(a.getDescription())
                    .code(a.getCode())
                    .available(Objects.nonNull(install) && Objects.equals(install.getStatus(), "Running"))
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public List<MkAppJob> createJob(Integer abilityType, Map<Integer, MkAppJob> jobConfigMap) {
        List<MkAppInstall> mkAppInstalls = mkAppInstallMapper.selectList(new QueryWrapper<>());
        // 查询已安装应用
        Map<String, Integer> appMap = mkAppInstalls.stream().collect(Collectors.toMap(MkAppInstall::getKey, MkAppInstall::getHttpPort));
        return MkAppJobContext.startJob(abilityType, jobConfigMap, appMap);
    }

    @Override
    public void syncPcapExportJob(ApiJobQueryVO req) {
        MkAppJob mkAppJob = mkAppJobMapper.selectOne(new QueryWrapper<MkAppJob>().eq("plan_id", req.getPlanId()));
        if (Objects.isNull(mkAppJob))
            return;
        MkAppJob update = new MkAppJob();
        update.setId(mkAppJob.getId());
        update.setParamOut(req);
        mkAppJobMapper.updateById(update);
    }

    @Override
    public ApiNetworkAssetVO pcapReportQuery(String operationId) {
        List<Long> planIds = mkAppJobMapper.selectList(new QueryWrapper<MkAppJob>().eq("operation_id", operationId).eq("type", 4).eq("status", 2))
                .stream().map(MkAppJob::getPlanId).collect(Collectors.toList());
        if (CollUtil.isEmpty(planIds))
            return null;
        String url = String.format(AppInterfaceConstants.PCAP_PAGE_REPORT, "8090");

        log.info("[网络流量监测]-[页面报告查询] 准备调用 url : {}", url);
        long start = System.currentTimeMillis();
        ApiJobQueryReq apiJobQueryReq = new ApiJobQueryReq();
        apiJobQueryReq.setPlanIds(planIds);
        String result = HttpUtil.post(url, JSONUtil.toJsonStr(apiJobQueryReq), 10_000);
        log.info("[网络流量监测]-[页面报告查询] 调用成功，耗时：{}，请求地址：{}，返回信息：{}", System.currentTimeMillis() - start, url, result);

        Response<ApiNetworkAssetVO> res = JSONUtil.parseObj(result).toBean(new TypeReference<Response<ApiNetworkAssetVO>>(){
        });
        if (!res.isSuccess()) {
            throw new ServiceException(res.getMsg());
        }
        return res.getData();
    }

    @Override
    public ApiNetworkAssetVO pcapReportQuery(MkAppJob appJob) {
        String url = String.format(AppInterfaceConstants.PCAP_PAGE_REPORT, "8090");

        log.info("[网络流量监测]-[页面报告查询] 准备调用 url : {}", url);
        long start = System.currentTimeMillis();
        ApiJobQueryReq apiJobQueryReq = new ApiJobQueryReq();
        apiJobQueryReq.setPlanIds(Collections.singletonList(appJob.getPlanId()));
        String result = HttpUtil.post(url, JSONUtil.toJsonStr(apiJobQueryReq), 10_000);
        log.info("[网络流量监测]-[页面报告查询] 调用成功，耗时：{}，请求地址：{}，返回信息：{}", System.currentTimeMillis() - start, url, result);

        Response<ApiNetworkAssetVO> res = JSONUtil.parseObj(result).toBean(new TypeReference<Response<ApiNetworkAssetVO>>(){
        });
        if (!res.isSuccess()) {
            throw new ServiceException(res.getMsg());
        }
        return res.getData();
    }

    @Override
    public List<ApiCompareVO> compareReportQuery(String operationId, Integer type) {
        List<MkAppJob> jobs = mkAppJobMapper.selectList(new QueryWrapper<MkAppJob>().eq("operation_id", operationId).eq("type", type).eq("status", 2));
        List<Long> planIds = jobs.stream().map(MkAppJob::getPlanId).collect(Collectors.toList());
        if (CollUtil.isEmpty(planIds))
            return Collections.emptyList();
        String url = String.format(AppInterfaceConstants.COMPARE_WORD_REPORT, "8083");

        log.info("[脱敏加密检测]-[WORD报告查询] 准备调用 url : {}", url);
        long start = System.currentTimeMillis();
        ApiJobQueryReq apiJobQueryReq = new ApiJobQueryReq();
        apiJobQueryReq.setPlanIds(planIds);
        String result = HttpUtil.post(url, JSONUtil.toJsonStr(apiJobQueryReq), 10_000);
        log.info("[脱敏加密检测]-[WORD报告查询] 调用成功，耗时：{}，请求地址：{}，返回信息：{}", System.currentTimeMillis() - start, url, result);

        Response<List<ApiCompareVO>> res = JSONUtil.parseObj(result).toBean(new TypeReference<Response<List<ApiCompareVO>>>(){
        });
        if (!res.isSuccess()) {
            throw new ServiceException(res.getMsg());
        }
        // 获取数源总数
        List<PreSourceConfig> preSourceConfigList = preSourceConfigMapper.selectList(new QueryWrapper<PreSourceConfig>().eq("operation_id", operationId));
        Map<Long, Integer> AppIdMap = jobs.stream().collect(Collectors.toMap(MkAppJob::getPlanId, MkAppJob::getId));
        Map<Integer, PreSourceConfig> dbSourceMap = new HashMap<>(16);
        preSourceConfigList.forEach(preSourceConfig -> {
            String appIds = preSourceConfig.getAppIds();
            String[] appIdArr = appIds.split(",");
            for (String appId : appIdArr) {
                dbSourceMap.put(Integer.parseInt(appId), preSourceConfig);
            }
        });
        List<TreeLabelDTO> treeLabelList = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
        Map<Long, String> busSystemMap = treeLabelList.stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId,TreeLabelDTO::getTreeName));
        res.getData().forEach(apiCompareVO -> {
            Integer appId = AppIdMap.get(apiCompareVO.getPlanId());
            if (dbSourceMap.containsKey(appId)) {
                PreSourceConfig p = dbSourceMap.get(appId);
                apiCompareVO.setDbName(p.getConfigName());
                apiCompareVO.setBusSystem(busSystemMap.get(p.getSystemId()));
            }
        });
        return res.getData();
    }

    public static void main(String[] args) {
//        Yaml yaml = new Yaml();
//        String dockerCompose = FileUtil.readString("D:\\compose\\dataway\\docker-compose.yml", StandardCharsets.UTF_8);
//        Map<String,Object> dockerComposeMap = yaml.load(dockerCompose);
//        Pattern pattern = Pattern.compile(REGEX);
//        Map<String, Object> map = (Map<String, Object>)dockerComposeMap.get("services");
//        Map<String, Object> dcasMap = (Map<String, Object>)map.get("dataway");
//        List<String> portsList = (List<String>)dcasMap.get("ports");
//        portsList = portsList.stream().map(s -> s.replace(AppConstant.$PORT, 9091+"")).collect(Collectors.toList());
//        dcasMap.put("ports",portsList);
//        System.out.println(IpUtils.getHostIp());
//        System.out.println(NetUtil.getLocalhostStr());
//        if (!dcasMap.containsKey(ContainerConstant.CONTAINER_NAME) || dcasMap.containsKey(
//            ContainerConstant.CONTAINER_NAME) && pattern.matcher(
//            (String)dcasMap.get(ContainerConstant.CONTAINER_NAME)).find()) {
//            log.info("replace container name with 222");
//        } else {
//            log.info("111");
//        }
//        System.out.println(pattern.matcher((String)dcasMap.get(ContainerConstant.CONTAINER_NAME)).find());

        String ip = CharSequenceUtil.subBetween("http://*************/dcas-pcap-1.0.0.tar.gz", HTTP_IP_PREFIX, HTTP_IP_SUFFIX);
        System.out.println(ip);
    }

}
