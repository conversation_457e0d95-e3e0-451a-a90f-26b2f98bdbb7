package com.dcas.market.app.service;

import com.dcas.common.domain.entity.MkApp;
import com.dcas.common.domain.entity.MkAppJob;
import com.dcas.common.model.vo.*;
import com.dcas.market.app.model.App;
import com.dcas.market.app.model.vo.AppDetailVO;
import com.dcas.market.app.model.vo.AppInstalledVO;
import com.dcas.market.app.model.vo.AppVO;
import com.dcas.market.app.model.InstallParam;
import com.dcas.market.app.model.OpParam;
import com.github.pagehelper.PageInfo;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className IAppService
 * @description 应用app接口类
 * @date 2024/05/27 16:12
 */
public interface IAppService {

    /**
     * 获取App列表
     * @return app列表
     */
    PageInfo<AppVO> getAppList(Integer currentPage, Integer pageSize, String name) throws IOException;

    /**
     * 获取安装了的App列表
     * @return app列表
     */
    PageInfo<AppInstalledVO> getInstalledAppList(Integer currentPage, Integer pageSize, String name);

    /**
     * 安装App
     * @param installParam {@link InstallParam}
     */
    Boolean installApp(InstallParam installParam);

    /**
     * 操作App，包含：卸载、重启、停止等
     * @param opParam {@link OpParam}
     */
    void op(OpParam opParam);

    /**
     * 通过appDetailId和版本号获取app详情
     * @param version 版本号
     * @param appId appID
     * @return app {@link  App}
     */
    AppDetailVO getAppDetail(Integer appId, String version);

    /**
     * 通过json配置文件获取app信息
     * @param jsonDir
     * @return
     */
    List<App> getAppList(String jsonDir) throws IOException;

    /**
     * 保存或更新app应用信息
     * @param list {@link App}
     */
    void saveOrUpdate(List<App> list) throws Exception;

    /**
     * 检查app个数是否一致
     * @param size
     * @return 一致返回true
     */
    boolean checkCount(int size);

    /**
     * 查询所有app
     * @return
     */
    List<MkApp> listAll();

    List<AbilityModelVO> getAbilityModel();

    List<MkAppJob> createJob(Integer abilityType, Map<Integer, MkAppJob> jobConfigMap);

    void syncPcapExportJob(ApiJobQueryVO req);

    ApiNetworkAssetVO pcapReportQuery(String operationId);

    List<ApiCompareVO> compareReportQuery(String operationId, Integer type);

    ApiNetworkAssetVO pcapReportQuery(MkAppJob appJob);
}
