package com.dcas.discovery.domain.enums;

import com.dcas.common.api.IResultCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 服务状态码枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2018/8/20
 */
@Getter
@AllArgsConstructor
public enum Status implements IResultCode {

    //***100**********************************************************************************************

    /**
     * 服务不可用
     */
    SERVICE_UNAVAILABLE(1, "服务不可用"),

    /**
     * 存在异常数据
     */
    ABNORMAL_DATA(2, "存在异常数据"),

    /**
     * 缺失初始化数据
     */
    MISS_INIT_DATA(3, "缺失初始化数据"),

    /**
     * 配置异常
     */
    ABNORMAL_CONFIGURATION(10, "配置异常，请检查配置文件"),

    /**
     * mysql导致的异常解决方案提示
     */
    MYSQL_ERROR(70, "迭代异常! 原因: MySQL聚合concat长度有限! 方案: 设置MySQL: SET GLOBAL group_concat_max_len=102400 "),

    //***1000**********************************************************************************************

    /**
     * 参数不能为空
     */
    PARAM_NOT_NULL(400, "参数不能为空"),

    /**
     * 参数不匹配
     */
    PARAM_NOT_MATCH(400, "参数不匹配"),

    /**
     * 参数超长
     */
    PARAM_OVER_LENGTH(400, "参数超长"),

    /**
     * 参数缺失
      */
    PARAM_IS_MISSING(400, "缺少必要的请求参数"),

    /**
     * 权限不够
     */
    FORBIDDEN(403, "license校验未通过"),

    /**
     * 本机器license错误，请更新license
     */
    LICENSE_INVALID(403, "license无法通过认证，请更新license"),

    /**
     * 本机器license已过期，请更新license
     */
    LICENSE_EXPIRED(403, "本机器license已过期，请更新license"),

    /**
     * 本机器未授权，请申请license
     */
    LICENSE_NOTFOUND(403, "本机器未授权，请申请license"),

    HTTP_NOT_FOUND(404, "Not Found"),

    //***1000~1099 全局参数限制异常****************************************************************************

    /**
     * 端口不存在
     */
    PORT_NOT_EXIST(1001, "端口不存在"),

    /**
     * 端口输入有误，范围 1~65535
     */
    PORT_NON_SUPPORT(1001, "端口输入有误"),

    /**
     * ip格式错误
     */
    IP_FORMAT_ERROR(1002, "IP格式异常"),

    IP_CANNOT_FILLED(1003, "无法同时选择IPv4和IPv6地址"),

    NOT_SUPPORT_IPV6(1004, "所选数据库不支持IPV6"),

    //***1100~1199 项目参数限制异常****************************************************************************

    /**
     * 存在重复名称
     */
    DUPLICATE_GROUP_NAME(1100, "存在重复名称"),

    /**
     * 配置信息错误
     */
    CONFIG_INFO_ERROR(1101, "配置信息错误"),

    /**
     * 备注信息不能超过200个字符
     */
    REMARK_SIZE_LENGTH(1102, "备注不超过200个字符！"),

    //***1200~1999 指定参数限制异常****************************************************************************

    /**
     * 字典类型已存在
     */
    DICT_TYPE_EXIST(1200, "分类名称同一模板下不可重复命名"),

    /**
     * 字典名称不能超过20个字符
     */
    DICT_NAME_LENGTH_LIMIT(1201, "字典名称不能超过20个字符"),

    /**
     * 导表对应的字典名称，字典键值，字典标签不能为空
     */
    DICT_NAME_AND_LABEL_NOT_NULL(1202, "导表对应的字典名称，字典键值，字典标签不能为空"),

    /**
     * ASCII码输入错误
     */
    ASCII_CODE_ERROR(1203, "ASCII码的十进制范围是 0~255"),

    /**
     * 不支持的分隔符
     */
    SEPARATOR_NOT_SUPPORTED(1204, "不支持的分隔符"),

    //***2000~2099 文件相关异常   ****************************************************************************
    /**
     * 文件不存在 {@link java.io.File#exists()}
     */
    FILE_NOT_EXIST(2000, "文件不存在"),
    /**
     * 导入文件异常
     */
    IMPORT_FILE_ERROR(2001, "导入文件异常"),
    /**
     * 导入文件与支持的格式不匹配
     */
    IMPORT_FILE_NOT_SUPPORT(2001, "不支持的文件格式"),
    /**
     * 导入excel时,io异常上传文件失败
     */
    EXCEL_IO_ERROR(2001, "IO异常! 上传文件异常 (检查windows/linux路径是否正确) !"),

    /**
     * 上传文件内容为空
     */
    IMPORT_FILE_IS_NULL(2002, "上传文件内容为空"),

    /**
     * 上传文件同名
     */
    IMPORT_FILE_IS_REPEAT(2002, "不允许上传同名文件"),
    /**
     * 导入文件内容超过限制
     */
    IMPORT_FILE_OVER_LIMIT(2003, "导入文件内容超过限制"),
    /**
     * 导入文件大小超过100M
     */
    IMPORT_FILE_SIZE_OVER_100M(2003, "文件大小超过100M"),
    /**
     * 文件内容错误
     */
    IMPORT_FILE_CONTENT_ERROR(2004, "文件格式有误，请检查后重新导入！"),

    /**
     * 导入文件的业务类型不能为空
     */
    IMPORT_BUSINESS_NAME_IS_NULL(2005, "【业务类型名称】为空"),

    /**
     * 导入文件的业务类型长度不能超过最大限制
     */
    IMPORT_BUSINESS_NAME_OVER_LIMIT(2006, "【业务类型名称】长度超过最大限制"),

    /**
     * 导入文件的分类名称不能超过最大限制
     */
    IMPORT_CLASSIFY_TYPE_OVER_LIMIT(2006, "【分类名称】长度超过最大限制"),

    /**
     * 导入文件的分类分级不能为空
     */
    IMPORT_LEVEL_IS_NULL(2007, "【分级】为空"),

    /**
     * 导入文件分级不合理
     */
    IMPORT_LEVEL_NOT_EXIST(2008, "【分级】超出范围"),

    /**
     * 导入文件分级不合理
     */
    IMPORT_LEVEL_NOT_NUMBER(2008, "【分级】必须填数字"),

    /**
     * 分类分级一级分类不可为空
     */
    IMPORT_FIRST_CLASSIFY_IS_NULL(2009, "【一级分类】为空"),

    /**
     * 最小分类的父级分类不能为空
     */
    IMPORT_FATHER_CLASSIFY_IS_NULL(2009, "【父级分类】为空"),

    /**
     * 文件正在生成，暂时不能下载，请稍后
     */
    EXPORT_JOB_PROCESSING(2010, "文件正在生成，暂时不能下载，请稍后"),

    /**
     * 文件正在生成，不能重复生成
     */
    EXPORT_JOB_PROCESSING_REPEAT(2011, "文件正在生成，不能重复生成"),

    /**
     * 当前作业，正在生成的文件个数已达上限，请稍后重试
     */
    EXPORT_FILE_IS_LIMITED(2012, "正在生成的文件个数已达上限，请稍后重试"),

    /**
     * 文件生成失败，请重新生成
     */
    EXPORT_JOB_FAILED(2013, "文件生成失败，请重新生成"),

    /**
     * 文件还未生成，请先生成文件
     */
    EXPORT_JOB_UNDO(2014, "文件还未生成，请先生成文件"),

    /**
     * 文件不存在，请重新生成
     */
    EXPORT_JOB_FILE_NOT_EXIST(2014, "文件不存在，请重新生成"),

    /**
     * 当前作业，正在生成的文件个数已达上限，请稍后重试
     */
    EXPORT_LOG_NOT_EXIST(2014, "当前文件生成记录不存在"),

    /**
     * 选择字典不存在
     */
    IMPORT_FILE_SELECT_DICT_NOT_EXIST_ERROR(2015, "选择字典不存在系统中"),

    /**
     * 导入字段为空
     */
    IMPORT_TABLE_SCHEMA_EMPTY(2016, "文件导入失败！表元数据缺少【Schema】"),
    /**
     * 导入字段为空
     */
    IMPORT_TABLE_TABLE_EMPTY(2016, "文件导入失败！表元数据缺少【表名】"),
    /**
     * 表名相同
     */
    IMPORT_TABLE_DATA_REPETITION(2016, "文件导入失败！表元数据【表名相同】"),
    /**
     * 导入字段为空
     */
    IMPORT_FIELD_SCHEMA_EMPTY(2016, "文件导入失败！列元数据缺少【Schema】"),
    /**
     * 导入字段为空
     */
    IMPORT__FIELD_TABLE_EMPTY(2016, "文件导入失败！列元数据缺少【表名】"),
    /**
     * 导入字段为空
     */
    IMPORT_FIELD_FIELD_EMPTY(2016, "文件导入失败！列元数据缺少【字段名】"),
    /**
     * 字段名相同
     */
    IMPORT_FILE_DATA_REPETITION(2017, "文件导入失败！列元数据【字段名相同】"),
    /**
     * 导入字段异常
     */
    IMPORT_FIELD_SCHEMA_ERROR(2018, "文件导入失败！列元数据【Schema】在表元数据中不存在"),
    /**
     * 导入字段异常
     */
    IMPORT_FIELD_TABLE_ERROR(2018, "文件导入失败！列元数据【表名】在表元数据中不存在"),

    /**
     * 导入文件异常
     */
    IMPORT_FILE_NOT_KERBEROS(2101, "请上传文件krb5.conf"),

    /**
     * 数据库台账格式存在问题
     */
    DATABASE_FILE_ERROR(2201, "上传的数据库台账格式或者内容存在问题，请检查后再次上传！"),


    //***3000~3999 调度相关异常   ****************************************************************************

    /**
     * 调度操作异常
     */
    SCHEDULER_ERROR(3000, "调度操作异常"),

    //***7000~8999 外部服务相关异常   ****************************************************************************

    /**
     * 脚本启动失败
     */
    SCRIPT_START_ERROR(7000, "调度失败"),

    //***9000~9999 系统配置相关   ****************************************************************************

    /**
     * 字典类型不存在
     */
    DICT_TYPE_NOT_EXIST(9100, "分类不存在"),

    /**
     * 存在字典数据，字典类型不允许删除
     */
    DICT_TYPE_NOT_ALLOWED_DELETE(9101, "该分类下存在业务类型,请先删除或者移动业务类型后再进行删除"),

    /**
     * 字典数据不存在
     */
    DICT_DATA_NOT_EXIST(9200, "业务类型不存在"),

    /**
     * 字段业务类型已存在
     */
    DICT_DATA_EXIST(9201, "业务类型已存在"),

    /**
     * 存在重名业务类型，请重新修改
     */
    BIZ_NAME_EXIST(9202, "存在重名业务类型，请重新修改"),

    /**
     * 未检测到重复业务类型, 可继续配置
     */
    BIZ_NAME_NOT_EXIST(9203, "未检测到重复业务类型，可继续配置！"),

    /**
     * 未存在重复业务类型，请继续配置相关属性！
     */
    SYS_DICT_DATA_NOT_EXIST(9202, "未存在重复业务类型，请继续配置相关属性！"),

    /**
     * 已存在该业务类型，但未配置分类分级，请继续配置或者修改相关属性！
     */
    SYS_DICT_DATA_NOT_BIND_TYPE(9203, "已存在该业务类型，但未配置分类分级，请继续配置或者修改相关属性！"),

    /**
     * 该业务类型已经配置分类分级，请继续配置或者修改相关属性！
     */
    SYS_DICT_DATA_NOT_TEMPLATE(9204, "该业务类型已经配置分类分级，请继续配置或者修改相关属性！"),

    /**
     * 该业务类型已经在当前模板中，请继续配置或者修改相关属性！
     */
    SYS_DICT_DATA_BIND_TEMPLATE(9205, "该业务类型已经在当前模板中，请继续配置或者修改相关属性！"),

    /**
     * 该字段业务类型已被设置成敏感组合，无法删除，请先调整敏感组合！
     */
    CURRENT_DICT_DATA_BIND_SENSITIVE_COMBINATION(9206, "该字段业务类型已被设置成敏感组合，无法删除，请先调整敏感组合！"),

    /**
     * 已存在该业务类型, 作业完成之后, 会将规则关联到该业务类型！
     */
    BIZ_NAME_NOT_EXIST_NEXT(9207, "已存在该业务类型, 作业完成之后, 会将规则关联到该业务类型"),

    /**
     * 表格业务类型配置不存在
     */
    TABLE_BUSINESS_CFG_NOT_EXIST(9300, "表格业务类型配置不存在"),

    /**
     * 表格业务类型配置已存在
     */
    TABLE_BUSINESS_CFG_EXIST(9301, "表格业务类型配置已存在"),

    /**
     * 表格业务类型配置添加失败
     */
    TABLE_BUSINESS_CFG_SAVE_ERROR(9302, "表格业务类型配置添加失败"),

    /**
     * 判断规则不存在
     */
    TABLE_BUSINESS_CFG_RULE_NOT_EXIST(9310, "判断规则不存在"),

    /**
     * 判断规则已存在
     */
    TABLE_BUSINESS_CFG_RULES_EXIST(9311, "判断规则已存在"),

    /**
     * 规则添加失败
     */
    TABLE_BUSINESS_CFG_RULE_SAVE_ERROR(9312, "判断规则添加失败"),

    /**
     * 判断规则不能为空
     */
    TABLE_BUSINESS_CFG_RULES_NOT_NULL(9313, "判断规则不能为空"),

    /**
     * 不允许删除预定义规则
     */
    TABLE_BUSINESS_CFG_RULE_DELETE_ERROR(9320, "不允许删除预定义规则"),

    /**
     * 不允许修改预定义规则
     */
    TABLE_BUSINESS_CFG_RULE_UPDATE_ERROR(9321, "不允许修改预定义规则"),

    /**
     * 业务模板已存在
     */
    TEMPLATE_IS_EXIST(9401, "业务模板已存在"),
    /**
     * 业务模板不存在
     */
    TEMPLATE_NOT_EXIST(9402, "业务模板不存在"),
    /**
     * 业务模板已被使用
     */
    TEMPLATE_BE_USED(9403, "业务模板已被使用"),
    /**
     * 默认模板不可删除
     */
    DEFAULT_TEMPLATE_DELETE(9404, "默认模板不可删除"),

    /**
     * 字典数据不存在
     */
    DICT_IS_NOT_EXIST(9501, "无符合导入的字典数据"),

    /**
     * 校验样本数据
     */
    DICTIONARY_DATA_NOT_EXIST_ERROR(9502, "校验失败，该字典数据不存在指定样本"),

    /**
     * 系统中存在指定名称的字典类型
     */
    DICTIONARY_TYPE_EXIST_ERROR(9503, "系统中存在指定名称的字典类型"),

    /**
     * 系统中存在指定名称的字典数据
     */
    DICTIONARY_DATA_EXIST_ERROR(9504, "系统中存在指定名称或键值的字典数据"),

    /**
     * 字典类型存在对应的字典数据
     */
    DICTIONARY_EXIST_DATA_ERROR(9505, "当前字典类型存在配置的字典数据"),

    /**
     * 该字典已经被字段业务类型使用，无法直接删除！请先进行解绑。
     */
    DICTIONARY_EXIST_SYS_DATA(9506, "该字典已经被字段业务类型使用，无法直接删除！请先进行解绑。"),

    /**
     * 敏感组合配置已经绑定
     */
    SENSITIVE_COMBINATION_EXIST(9601, "存在已绑定的敏感组合配置！"),


    /**
     * 敏感组合对应业务类型不能重复
     */
    SENSITIVE_COMBINATION_LENGTH(9602, "绑定的业务类型不能重复选择"),

    /**
     * 分级分类不能为空
     */
    CLASSIFY_NOT_NULL(9603, "分级分类不能为空"),

    //***10000~10999 数据源相关异常 ****************************************************************************

    /**
     * 用户名不能为空
     */
    USERNAME_IS_EMPTY(10001, "用户名不能为空"),

    /**
     * 密码不能为空
     */
    PASSWORD_IS_EMPTY(10002, "密码不能为空"),

    /**
     * 请指定库名/实例名
     */
    NO_DB_NAME_SPECIFIED(10003, "请指定库名/实例名"),

    /**
     * 不支持的数据库类型
     */
    UNSUPPORTED_DATASOURCE_TYPES(10004, "不支持的数据库类型"),

    /**
     * 数据源连接失败
     */
    CONNECTION_FAILED(10005, "数据源连接失败"),

    /**
     * 数据源配置不存在
     */
    SOURCE_CONFIG_NOT_EXIST(10100, "数据源配置不存在"),

    /**
     * 数据源配置已存在
     */
    SOURCE_CONFIG_IS_EXIST(10101, "数据源配置已存在 {}"),

    /**
     * 存在异常的数据源
     */
    DATA_SOURCE_CONFIG_EXCEPTION(10102, "数据源配置存在异常"),

    /**
     * 数据源配置名称不能为空
     */
    SOURCE_CONFIG_NAME_NOT_NULL(10103, "数据源配置名称不能为空"),

    /**
     * 数据源分组不存在
     */
    SOURCE_GROUP_NOT_EXIST(10104, "输入分组不存在,归入未分组"),

    /**
     * 配置文件被使用
     */
    CONFIG_FILE_USED(10105, "文件被使用"),

    /**
     * 远程地址配置不存在
     */
    REMOTE_CONFIG_NOT_EXIST(10106, "远程地址配置不存在"),

    /**
     * 获取远程服务器文件失败
     */
    REMOTE_FILE_LS_ERROR(10107, "获取远程服务器文件列表失败"),

    /**
     * 未连接到远程服务器
     */
    CANNOT_CONNECT_REMOTE(10108, "无法连接到远程文件服务器"),

    /**
     * 远程操作失败
     */
    REMOTE_OPERATION_FAILED(10109, "远程操作异常"),

    /**
     * 文件数据源-没有配置文件最终保存路径
     */
    FILE_PATH_NOT_FOUND(10110, "未找到文件存储路径"),

    /**
     * 远程文件上传失败
     */
    REMOTE_FILE_UPLOAD_ERROR(10111, "远程文件上传失败"),

    /**
     * 远程文件下载失败
     */
    REMOTE_FILE_DOWNLOAD_ERROR(10112, "远程文件下载失败"),

    /**
     * 文件数据源-上传文件重名
     */
    FILE_NAME_DUPLICATE(10113, "上传文件不支持重名"),

    /**
     * 请确保导入失败的信息都已经修改完毕!
     */
    IMPORT_SOURCE_CONFIG_ERROR(10201, "请确保导入失败的信息都已经修改完毕!"),

    /**
     * 数据源扫描配置不存在
     */
    AUTO_SCAN_CONFIG_NOT_EXIT(10800, "配置不存在"),

    /**
     * 数据源发现配置已存在
     */
    AUTO_SCAN_CONFIG_IS_EXIST(10801, "数据源发现配置已存在"),

    /**
     * 扫描范围为空
     */
    AUTO_SCAN_IP_SCOPE_EMPTY(10802, "扫描范围为空"),

    /**
     * IP段范围存在异常
     */
    IP_OVERLAP_ERROR(10803, "IP段范围存在异常"),

    /**
     * 当前扫描结果不存在
     */
    AUTO_SCAN_RESULT_NOT_EXIST(10810, "当前扫描结果不存在"),

    /**
     * 扫描时间为0
     */
    AUTO_SCAN_TIME_INVALID(10811, "扫描时间为0"),

    //***11000~11099 作业新建相关异常 ********************************************************

    /**
     * 源数据发现作业不存在
     */
    JOB_NOT_EXIST(11000, "作业不存在"),

    /**
     * 源数据发现作业已存在
     */
    JOB_IS_EXIST(11001, "作业名称已存在"),

    /**
     * 元数据采集发现作业名称已存在
     */
    SOURCE_DATA_JOB_IS_EXIST(11001, "元数据采集作业名称已存在"),

    /**
     * 分类分级作业名称已存在
     */
    CLASSIFICATION_JOB_IS_EXIST(11001, "分类分级作业名称已存在"),

    /**
     * 存在异常的数据源
     */
    DATA_SOURCE_EXCEPTION(11002, "存在异常的数据源,请检查数据源配置"),

    /**
     * 新建作业限制
     */
    JOB_CONFIG_SOURCE_ERROR(11002, "Schema【{}】已经创建了发现作业，请勿重复创建"),

    /**
     * 新建作业限制
     */
    JOB_CONFIG_SOURCE_USE_ALL(11002, "Schema已经创建了发现作业，请勿重复创建"),

    /**
     * 新建作业限制
     */
    JOB_CONFIG_SOURCE_TABLE_ERROR(11002, "Table【{}】已经创建了发现作业，请勿重复创建"),

    /**
     * 新建作业schema下表名已创建发现限制
     */
    JOB_CONFIG_SOURCE_TABLE_USED(11002, "Table【{}】已经创建了发现作业，本次发现剔除"),
    /**
     * 存在应用该配置的作业正在运行
     */
    CONFIG_APPLY_RUNNING_JOB(11010, "队列中存在未完成的作业"),

    /**
     * 已结束
     */
    JOB_FINISH(11012, "作业已结束"),

    /**
     * 作业待确认
     */
    CONFIG_CONFIRMED(11013, "作业待确认"),

    /**
     * 无可查询的运行日志
     */
    NO_RUN_LOG(11014, "没有可以查看的运行日志记录"),

    //***11100~11999 作业结果相关异常 ********************************************************

    /**
     * 字段不存在
     */
    FIELD_NOT_EXIST(11201, "字段不存在"),

    /**
     * 样本数据格式有误
     */
    SAMPLE_DATA_ERROR(11203, "样本数据格式有误"),

    /**
     * 表格不存在
     */
    TABLE_NOT_EXIST(11301, "表格不存在"),

    /**
     * 特殊表格不存在
     */
    SPECIAL_TABLE_NOT_EXIST(11302, "特殊表格不存在"),

    /**
     * 查询table不存在(实际存在页面上)
     */
    RELATION_TABLE_ERROR(11303, "表格关联关系内部错误! 数据库数据缺失,导致关联关系不存在 !"),

    /**
     * 业务类型已绑定 -> 未确认/已确认状态的作业(候选项, 确认业务类型)
     */
    BIZ_ID_BIND_ERROR(11304, "所选的业务类型都已经被识别或者存在长文本中，无法删除！"),

    /**
     * 业务类型已绑定 -> 未确认/已确认状态的作业(候选项, 确认业务类型)
     */
    PRE_DEFINE_BIZ_ID_ERROR(11305, "预定义业务类型无法删除！"),

    /**
     * 该单一业务类型已绑定长文本, 无法删除
     */
    BIND_LONGTEXT_BIZ_ID_ERROR(11306, "该单一业务类型已绑定长文本, 无法删除！"),


    /**
     * 源数据报告更新,表格业务类型失败
     */
    UPDATE_ERROR(11401, "表格业务类型,更新失败!!"),

    /**
     * 长文本业务类型只能包含一条长文本规则
     */
    LONGTEXT_RULE_ONLY_ONE_ROW(11402, "长文本业务类型只能包含一条长文本规则"),

    /**
     * 长文本业务类型包含的单一类型不超过20个
     */
    LONGTEXT_SUB_BIZ_ID_NO_MORE_THAN_20(11403, "长文本业务类型包含的单一类型不超过20个"),

    // --------------------------------------------------------------------------------------

    REPEAT_TABLE_FIELD_NOT_FULLY_VALIDATED(11404, "重复表字段未完全确认"),

    // --------------------------------------------------------------------------------------
    SIMILARITY_NOT_EQUAL_ZERO(11405, "相似度配置应该大于0"),

    // --------------------------------------------------------------------------------------
    CONFIG_ID_NOT_NULL(11406, "数据源Id不为空"),

    // --------------------------------------------------------------------------------------
    SCHEMA_NOT_NULL(11407, "数据库不为空"),

    // --------------------------------------------------------------------------------------
    SCHEMA_ID_NOT_EXISTS(11408, "数据表Id不存在"),

    // --------------------------------------------------------------------------------------
    RELATION_TABLE_LEN_NO_MORE_THAN_50(11408, "关联表格不超过50个字符"),

    // --------------------------------------------------------------------------------------
    UNABLE_MANUAL_CONFIRM(11409, "部分字段无法自动确认，请逐个手动确认"),

    // --------------------------------------------------------------------------------------
    DATA_DICT_SCHEMA_NOT_NULL(11410, "schema不为空"),

    // --------------------------------------------------------------------------------------
    DATA_DICT_TABLE_NAME_NOT_NULL(11411, "表格名称不为空"),

    // --------------------------------------------------------------------------------------
    DATA_DICT_COLUMN_NAME_NOT_NULL(11412, "字段名称不为空"),

    // --------------------------------------------------------------------------------------
    DATA_DICT_SAME_DATA(11413, "重复数据"),

    // ---------------------------------------OpenApi-----------------------------------------------
    PLUGIN_NOT_NULL(11414, "数据库类型不能为空"),

    UN_SUPPORT_JOB_TYPE(11415, "不支持作业类型 %s"),

    // -------------------------------------------------------------------------------------
    CURRENT_JOB_NOT_FULLY_CONFIRMED(11416, "当前作业未全部确认"),

    // -------------------------------------------------------------------------------------
    BIZ_IS_NOT_NULL(11417, "请先选择业务类型"),

    // --------------------------------------------------------------------------------------
    UN_CONFIRM_CLASSIFY(11418, "当前表格还存在未确认分级分类字段, 确定对该表格字段进行确认吗?"),
    // --------------------------------------------------------------------------------------
    UN_CLASSIFY(11419, "当前表格还存在未分级分类字段, 确定对该表格字段进行确认吗?"),

    CAN_NOT_GENERATE_FEATURE_HAS_RUNNING_DISCOVERY_JOB(11418, "存在运行中的资产发现或资产关系作业，不能生成特征"),
    CAN_NOT_GENERATE_FEATURE_SOURCE_CONFIG_NOT_EXIST(11419, "资产发现作业的数据源不存在，不能生成特征"),
    CAN_NOT_GENERATE_FEATURE_DISCOVERY_JOB_NOT_CONFIRMED(11420, "资产发现作业未确认，不能生成特征"),
    CAN_NOT_GENERATE_FEATURE_JOB_RUNNING(11421, "特征工程作业运行中，不能生成特征"),
    SENSITIVITY_LEVEL_WEIGHT_FAIL(11422, "敏感指数权重不能小于或者等于上一分级"),

    TABLE_CLASSIFY_ERROR(11423, "数据字典表格推荐分类为空或者符合分类树结构"),
    GROUP_PARENT_NOT_EXISTS(11424, "父分组不存在"),
    GROUP_CHILDREN_EXISTS(11425, "分组下存在子分组"),
    GROUP_SOURCE_EXISTS(11425, "分组下存在数据源"),
    ;

    private final int code;
    private final String msg;

}
